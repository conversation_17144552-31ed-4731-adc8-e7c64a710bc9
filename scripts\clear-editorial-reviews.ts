/**
 * Clear Editorial Reviews Script
 * 
 * This script safely deletes all data from the editorial_reviews table
 * and resets any related foreign key references in the tools table.
 * 
 * Usage: npx tsx scripts/clear-editorial-reviews.ts
 */

import { supabase } from '../src/lib/supabase';

interface ClearStats {
  editorialReviewsDeleted: number;
  toolsUpdated: number;
  errors: string[];
}

async function clearEditorialReviews(): Promise<ClearStats> {
  const stats: ClearStats = {
    editorialReviewsDeleted: 0,
    toolsUpdated: 0,
    errors: []
  };

  try {
    console.log('🧹 Starting editorial reviews cleanup...\n');

    // 1. First, get count of existing editorial reviews
    console.log('1️⃣ Checking existing editorial reviews...');
    const { count: reviewCount, error: countError } = await supabase
      .from('editorial_reviews')
      .select('*', { count: 'exact', head: true });

    if (countError) {
      stats.errors.push(`Failed to count editorial reviews: ${countError.message}`);
      console.error('❌ Error counting editorial reviews:', countError);
      return stats;
    }

    console.log(`📊 Found ${reviewCount || 0} editorial reviews to delete`);

    if (!reviewCount || reviewCount === 0) {
      console.log('✅ No editorial reviews found. Nothing to delete.');
      return stats;
    }

    // 2. Clear editorial_review_id references in tools table
    console.log('\n2️⃣ Clearing editorial_review_id references in tools table...');
    const { count: toolsUpdated, error: toolsError } = await supabase
      .from('tools')
      .update({ editorial_review_id: null })
      .not('editorial_review_id', 'is', null)
      .select('*', { count: 'exact', head: true });

    if (toolsError) {
      stats.errors.push(`Failed to update tools: ${toolsError.message}`);
      console.error('❌ Error updating tools:', toolsError);
    } else {
      stats.toolsUpdated = toolsUpdated || 0;
      console.log(`✅ Updated ${stats.toolsUpdated} tools (cleared editorial_review_id)`);
    }

    // 3. Delete all editorial reviews
    console.log('\n3️⃣ Deleting all editorial reviews...');
    const { count: deletedCount, error: deleteError } = await supabase
      .from('editorial_reviews')
      .delete()
      .neq('id', '00000000-0000-0000-0000-000000000000') // Delete all (using impossible condition to delete all)
      .select('*', { count: 'exact', head: true });

    if (deleteError) {
      stats.errors.push(`Failed to delete editorial reviews: ${deleteError.message}`);
      console.error('❌ Error deleting editorial reviews:', deleteError);
    } else {
      stats.editorialReviewsDeleted = deletedCount || 0;
      console.log(`✅ Deleted ${stats.editorialReviewsDeleted} editorial reviews`);
    }

    // 4. Verify cleanup
    console.log('\n4️⃣ Verifying cleanup...');
    const { count: remainingCount, error: verifyError } = await supabase
      .from('editorial_reviews')
      .select('*', { count: 'exact', head: true });

    if (verifyError) {
      stats.errors.push(`Failed to verify cleanup: ${verifyError.message}`);
      console.error('❌ Error verifying cleanup:', verifyError);
    } else {
      console.log(`📊 Remaining editorial reviews: ${remainingCount || 0}`);
      
      if (remainingCount === 0) {
        console.log('✅ All editorial reviews successfully deleted');
      } else {
        console.log(`⚠️  ${remainingCount} editorial reviews still remain`);
      }
    }

    // 5. Check for orphaned tool references
    console.log('\n5️⃣ Checking for orphaned tool references...');
    const { data: orphanedTools, error: orphanError } = await supabase
      .from('tools')
      .select('id, name, editorial_review_id')
      .not('editorial_review_id', 'is', null);

    if (orphanError) {
      stats.errors.push(`Failed to check orphaned tools: ${orphanError.message}`);
      console.error('❌ Error checking orphaned tools:', orphanError);
    } else {
      if (orphanedTools && orphanedTools.length > 0) {
        console.log(`⚠️  Found ${orphanedTools.length} tools with orphaned editorial_review_id references:`);
        orphanedTools.forEach((tool, index) => {
          console.log(`  ${index + 1}. ${tool.name} (${tool.id}) -> ${tool.editorial_review_id}`);
        });
      } else {
        console.log('✅ No orphaned tool references found');
      }
    }

    return stats;

  } catch (error: any) {
    stats.errors.push(`Unexpected error: ${error.message}`);
    console.error('❌ Unexpected error:', error);
    return stats;
  }
}

async function main() {
  console.log('🚀 Editorial Reviews Cleanup Script');
  console.log('=====================================\n');

  // Confirm with user (in production, you might want to add a confirmation prompt)
  console.log('⚠️  WARNING: This will delete ALL editorial reviews and clear related references!');
  console.log('📝 This action cannot be undone.\n');

  const stats = await clearEditorialReviews();

  console.log('\n📊 CLEANUP SUMMARY');
  console.log('==================');
  console.log(`Editorial Reviews Deleted: ${stats.editorialReviewsDeleted}`);
  console.log(`Tools Updated: ${stats.toolsUpdated}`);
  console.log(`Errors: ${stats.errors.length}`);

  if (stats.errors.length > 0) {
    console.log('\n❌ ERRORS:');
    stats.errors.forEach((error, index) => {
      console.log(`  ${index + 1}. ${error}`);
    });
  }

  if (stats.errors.length === 0) {
    console.log('\n✅ Cleanup completed successfully!');
  } else {
    console.log('\n⚠️  Cleanup completed with errors. Please review the issues above.');
  }

  process.exit(stats.errors.length === 0 ? 0 : 1);
}

// Run the script
if (require.main === module) {
  main().catch((error) => {
    console.error('💥 Script failed:', error);
    process.exit(1);
  });
}

export { clearEditorialReviews };
