import { NextRequest, NextResponse } from 'next/server';
import { validate<PERSON><PERSON><PERSON><PERSON> } from '@/lib/auth';
import { EnhancedContentGenerationPipeline } from '@/lib/content-generation/enhanced-pipeline';
import { supabase } from '@/lib/supabase';

/**
 * POST /api/admin/ai-dude/test
 * Test enhanced AI Dude content generation system
 */
export async function POST(request: NextRequest) {
  try {
    // Validate admin API key
    const isValid = await validateApiKey(request);
    if (!isValid) {
      return NextResponse.json(
        { success: false, error: 'Unauthorized' },
        { status: 401 }
      );
    }

    const body = await request.json();
    const { action, toolId, url, testData } = body;

    const results: any = {
      action,
      timestamp: new Date().toISOString(),
      results: {}
    };

    const pipeline = new EnhancedContentGenerationPipeline();

    switch (action) {
      case 'test_auto_triggers':
        if (!toolId || !url) {
          throw new Error('Tool ID and URL are required for auto-trigger testing');
        }

        // Test enhanced generation with auto-triggers
        const enhancedResult = await pipeline.generateWithAutoTriggers({
          toolId,
          url,
          autoTriggerScraping: true,
          autoTriggerMedia: true,
          includeReleases: true,
          priority: 'high',
          methodology: 'ai_dude'
        });

        results.results.enhancedGeneration = enhancedResult;
        break;

      case 'test_releases_field':
        // Test releases field generation
        const releasesTestData = testData || {
          scrapedContent: `# TestTool v2.1.0 Release Notes

## Version 2.1.0 (2024-01-15)
- Added new AI features
- Improved performance by 50%
- Fixed critical bugs

## Version 2.0.0 (2023-12-01)
- Major UI overhaul
- New pricing plans
- Enhanced security

## Version 1.5.0 (2023-10-15)
- Bug fixes and improvements
- New integrations`,
          existingToolData: {
            name: 'TestTool',
            description: 'A test tool for releases'
          }
        };

        // Test releases generation using AI Dude template
        const releasesResponse = await fetch('/api/generate-content', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            'x-api-key': process.env.NEXT_PUBLIC_ADMIN_API_KEY || 'aidude_admin_2024_secure_key_xyz789'
          },
          body: JSON.stringify({
            url: 'https://testtool.com',
            scrapedData: releasesTestData.scrapedContent,
            existingToolData: releasesTestData.existingToolData,
            sectionType: 'ai_dude_releases'
          })
        });

        if (releasesResponse.ok) {
          const releasesResult = await releasesResponse.json();
          results.results.releasesGeneration = releasesResult;
        } else {
          results.results.releasesGeneration = { error: 'Failed to generate releases' };
        }
        break;

      case 'test_bulk_processing':
        if (!testData?.toolIds || !Array.isArray(testData.toolIds)) {
          throw new Error('Tool IDs array is required for bulk processing testing');
        }

        // Test bulk processing
        const bulkRequests = testData.toolIds.map((id: string) => ({
          toolId: id,
          url: `https://example-${id}.com`,
          autoTriggerScraping: false, // Skip for testing
          autoTriggerMedia: false,
          includeReleases: true,
          methodology: 'ai_dude'
        }));

        const bulkResults = await pipeline.bulkGenerateWithAutoTriggers(bulkRequests);
        results.results.bulkProcessing = {
          totalRequests: bulkRequests.length,
          results: bulkResults,
          successCount: bulkResults.filter(r => r.success !== false).length,
          failureCount: bulkResults.filter(r => r.success === false).length
        };
        break;

      case 'test_validation_system':
        if (!testData?.content) {
          throw new Error('Content is required for validation testing');
        }

        // Test AI Dude validation
        const validationResponse = await fetch('/api/admin/content/validate', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            'x-api-key': process.env.NEXT_PUBLIC_ADMIN_API_KEY || 'aidude_admin_2024_secure_key_xyz789'
          },
          body: JSON.stringify({
            content: testData.content,
            methodology: 'ai_dude',
            toolId: toolId
          })
        });

        if (validationResponse.ok) {
          const validationResult = await validationResponse.json();
          results.results.validation = validationResult;
        } else {
          const errorData = await validationResponse.json();
          results.results.validation = { error: errorData.error };
        }
        break;

      case 'test_prompt_templates':
        // Test all AI Dude prompt templates
        const { data: templates, error: templatesError } = await supabase
          .from('system_configuration')
          .select('config_key, config_value')
          .like('config_key', 'prompt_ai_dude%')
          .eq('config_type', 'prompt_template');

        if (templatesError) {
          throw new Error(`Failed to get templates: ${templatesError.message}`);
        }

        results.results.promptTemplates = {
          totalTemplates: templates?.length || 0,
          templates: templates?.map(t => ({
            key: t.config_key,
            name: t.config_value?.name,
            type: t.config_value?.promptType,
            category: t.config_value?.category
          })) || [],
          expectedCount: 11, // 10 AI Dude + 1 validation (includes universal partial system)
          isComplete: (templates?.length || 0) >= 11
        };
        break;

      case 'test_database_schema':
        // Test enhanced database schema with releases
        const enhancedSchema = pipeline['getEnhancedDatabaseSchema'](true);
        
        results.results.databaseSchema = {
          hasReleasesField: !!enhancedSchema.releases,
          totalFields: Object.keys(enhancedSchema).length,
          requiredFields: Object.entries(enhancedSchema)
            .filter(([key, value]) => typeof value === 'string' && value.includes('required'))
            .map(([key]) => key),
          optionalFields: Object.entries(enhancedSchema)
            .filter(([key, value]) => typeof value === 'string' && value.includes('optional'))
            .map(([key]) => key)
        };
        break;

      default:
        throw new Error(`Unknown test action: ${action}`);
    }

    return NextResponse.json({
      success: true,
      data: results
    });

  } catch (error: any) {
    console.error('AI Dude test API error:', error);
    return NextResponse.json(
      { 
        success: false, 
        error: error.message || 'AI Dude test failed' 
      },
      { status: 500 }
    );
  }
}

/**
 * GET /api/admin/ai-dude/test
 * Get AI Dude test status and available actions
 */
export async function GET(request: NextRequest) {
  try {
    // Validate admin API key
    const isValid = await validateApiKey(request);
    if (!isValid) {
      return NextResponse.json(
        { success: false, error: 'Unauthorized' },
        { status: 401 }
      );
    }

    const availableActions = [
      'test_auto_triggers',
      'test_releases_field',
      'test_bulk_processing',
      'test_validation_system',
      'test_prompt_templates',
      'test_database_schema'
    ];

    return NextResponse.json({
      success: true,
      data: {
        message: 'Enhanced AI Dude test API is ready',
        availableActions,
        usage: 'POST with { "action": "action_name", "toolId": "tool_id", "url": "tool_url", "testData": {...} }',
        features: [
          'Automatic webscraper triggering',
          'Automatic media collection triggering',
          'Enhanced releases field support',
          'Bulk processing capabilities',
          'AI Dude content validation',
          'Complete prompt template system'
        ]
      }
    });

  } catch (error: any) {
    console.error('AI Dude test API error:', error);
    return NextResponse.json(
      { 
        success: false, 
        error: error.message || 'Failed to get test status' 
      },
      { status: 500 }
    );
  }
}
