/**
 * Centralized Supabase mock setup for consistent testing across all test suites
 */

export interface MockQueryBuilder {
  insert: jest.MockedFunction<any>;
  select: jest.MockedFunction<any>;
  update: jest.MockedFunction<any>;
  delete: jest.MockedFunction<any>;
  eq: jest.MockedFunction<any>;
  order: jest.MockedFunction<any>;
  limit: jest.MockedFunction<any>;
  range: jest.MockedFunction<any>;
  single: jest.MockedFunction<any>;
  gt: jest.MockedFunction<any>;
  lt: jest.MockedFunction<any>;
  or: jest.MockedFunction<any>;
  contains: jest.MockedFunction<any>;
  then: jest.MockedFunction<any>;
}

export interface MockSupabaseClient {
  from: jest.MockedFunction<any>;
  rpc: jest.MockedFunction<any>;
}

/**
 * Creates a properly chainable mock query builder that supports all Supabase operations
 */
export function createMockQueryBuilder(): MockQueryBuilder {
  const mockBuilder: Partial<MockQueryBuilder> = {};

  // Create all methods that return 'this' for chaining
  const chainableMethods = ['insert', 'select', 'update', 'delete', 'eq', 'order', 'limit', 'range', 'gt', 'lt', 'or', 'contains'];

  chainableMethods.forEach(method => {
    mockBuilder[method as keyof MockQueryBuilder] = jest.fn().mockReturnValue(mockBuilder);
  });

  // Terminal methods that return promises
  mockBuilder.single = jest.fn().mockResolvedValue({ data: null, error: null });
  mockBuilder.then = jest.fn().mockResolvedValue({ data: null, error: null });

  // Make the query builder itself thenable (Promise-like)
  Object.defineProperty(mockBuilder, 'then', {
    value: jest.fn().mockResolvedValue({ data: null, error: null }),
    writable: true,
    configurable: true
  });

  return mockBuilder as MockQueryBuilder;
}

/**
 * Creates a mock Supabase client with consistent behavior
 */
export function createMockSupabaseClient(): MockSupabaseClient {
  const mockClient = {
    from: jest.fn(() => createMockQueryBuilder()),
    rpc: jest.fn().mockResolvedValue({ data: null, error: null })
  };
  
  return mockClient;
}

/**
 * Global mock setup for Supabase - use this in test files
 */
export function setupSupabaseMocks() {
  const mockSupabase = createMockSupabaseClient();
  
  jest.mock('@supabase/supabase-js', () => ({
    createClient: jest.fn(() => mockSupabase)
  }));
  
  jest.mock('@/lib/supabase', () => ({
    supabaseAdmin: mockSupabase,
    supabase: mockSupabase
  }));
  
  return mockSupabase;
}

/**
 * Helper to get a fresh query builder for specific test setup
 */
export function getMockQueryBuilder(mockSupabase: MockSupabaseClient): MockQueryBuilder {
  const queryBuilder = createMockQueryBuilder();
  mockSupabase.from.mockReturnValue(queryBuilder);
  return queryBuilder;
}

/**
 * Helper to reset all mocks between tests
 */
export function resetSupabaseMocks(mockSupabase: MockSupabaseClient) {
  jest.clearAllMocks();
  // Restore the default behavior
  mockSupabase.from.mockImplementation(() => createMockQueryBuilder());
  mockSupabase.rpc.mockResolvedValue({ data: null, error: null });
}

/**
 * Helper to setup specific mock responses for common test scenarios
 */
export function setupMockResponses(queryBuilder: MockQueryBuilder, responses: {
  insert?: { data?: any; error?: any };
  select?: { data?: any; error?: any };
  single?: { data?: any; error?: any };
  update?: { data?: any; error?: any };
  delete?: { data?: any; error?: any };
}) {
  if (responses.insert) {
    queryBuilder.insert.mockResolvedValue(responses.insert);
  }
  if (responses.select) {
    queryBuilder.then.mockResolvedValue(responses.select);
  }
  if (responses.single) {
    queryBuilder.single.mockResolvedValue(responses.single);
  }
  if (responses.update) {
    queryBuilder.update.mockResolvedValue(responses.update);
  }
  if (responses.delete) {
    queryBuilder.delete.mockResolvedValue(responses.delete);
  }
}
