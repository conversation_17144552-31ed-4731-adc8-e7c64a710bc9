'use client';

import { useState } from 'react';
import { FileUploadSection } from './FileUploadSection';
import { ManualEntrySection } from './ManualEntrySection';
import { ProcessingOptionsPanel } from './ProcessingOptionsPanel';
import { ProgressTracker } from './ProgressTracker';
import { ResultsViewer } from './ResultsViewer';
import { BulkJobHistory } from './BulkJobHistory';
import { ResumeToolSelector } from './ResumeToolSelector';
import { BulkProcessingJob } from '@/lib/types';
import { BulkProcessingOptions } from '@/lib/bulk-processing/bulk-engine';

interface BulkProcessingDashboardProps {
  jobs: BulkProcessingJob[];
  loading: boolean;
  onJobUpdate: () => void;
  onError: (error: string) => void;
}

type InputMethod = 'file' | 'manual' | 'resume' | 'none';
type ProcessingStep = 'input' | 'configure' | 'processing' | 'results';

/**
 * Bulk Processing Dashboard
 * 
 * Main orchestrator component for bulk processing operations.
 * Manages the workflow from input to results with step-by-step navigation.
 */
export function BulkProcessingDashboard({
  jobs,
  loading,
  onJobUpdate,
  onError,
}: BulkProcessingDashboardProps) {
  const [currentStep, setCurrentStep] = useState<ProcessingStep>('input');
  const [inputMethod, setInputMethod] = useState<InputMethod>('none');
  const [processedData, setProcessedData] = useState<any>(null);
  const [processingOptions, setProcessingOptions] = useState<BulkProcessingOptions>({
    batchSize: 5,
    delayBetweenBatches: 2000,
    retryAttempts: 3,
    aiProvider: 'openai',
    skipExisting: true,
    scrapeOnly: false,
    generateContent: true,
    resumeGeneration: false,
    autoPublish: false,
    priority: 'normal',
  });
  const [activeJobId, setActiveJobId] = useState<string | null>(null);
  const [selectedTools, setSelectedTools] = useState<string[]>([]);

  const handleInputMethodSelect = (method: InputMethod) => {
    setInputMethod(method);
    setProcessedData(null);
    setSelectedTools([]);

    // Auto-enable resume generation mode when resume method is selected
    if (method === 'resume') {
      setProcessingOptions(prev => ({
        ...prev,
        resumeGeneration: true,
        scrapeOnly: false,
        generateContent: true,
      }));
    } else {
      setProcessingOptions(prev => ({
        ...prev,
        resumeGeneration: false,
      }));
    }
  };

  const handleDataProcessed = (data: any) => {
    setProcessedData(data);
    setCurrentStep('configure');
  };

  const handleToolsSelected = (toolIds: string[]) => {
    setSelectedTools(toolIds);
    // Create mock processed data for resume generation
    if (toolIds.length > 0) {
      setProcessedData({
        validItems: toolIds.map(id => ({ url: `tool-${id}`, toolId: id })),
        totalItems: toolIds.length,
        invalidItems: [],
        duplicatesRemoved: 0,
      });
      setCurrentStep('configure');
    } else {
      setProcessedData(null);
      setCurrentStep('input');
    }
  };

  const handleOptionsChange = (options: Partial<BulkProcessingOptions>) => {
    setProcessingOptions(prev => ({ ...prev, ...options }));
  };

  const handleStartProcessing = async () => {
    if (!processedData && !processingOptions.resumeGeneration) {
      onError('No data to process');
      return;
    }

    if (processingOptions.resumeGeneration && selectedTools.length === 0) {
      onError('No tools selected for resume generation');
      return;
    }

    try {
      setCurrentStep('processing');

      let endpoint = '/api/admin/bulk-processing';
      let requestBody: any;

      if (processingOptions.resumeGeneration) {
        // Use the resume tools endpoint
        endpoint = '/api/admin/bulk-processing/resume-tools';
        requestBody = {
          toolIds: selectedTools,
          options: processingOptions,
        };
      } else {
        // Use the regular bulk processing endpoint
        requestBody = {
          type: 'processed_data',
          data: processedData.validItems,
          options: processingOptions,
        };
      }

      const response = await fetch(endpoint, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'x-admin-api-key': process.env.NEXT_PUBLIC_ADMIN_API_KEY || '',
        },
        body: JSON.stringify(requestBody),
      });

      if (!response.ok) {
        throw new Error('Failed to start bulk processing');
      }

      const result = await response.json();
      if (result.success) {
        setActiveJobId(result.data.job.id);
        onJobUpdate();
      } else {
        throw new Error(result.error || 'Unknown error');
      }
    } catch (err) {
      onError(err instanceof Error ? err.message : 'Failed to start processing');
      setCurrentStep('configure');
    }
  };

  const handleJobComplete = () => {
    setCurrentStep('results');
    onJobUpdate();
  };

  const handleReset = () => {
    setCurrentStep('input');
    setInputMethod('none');
    setProcessedData(null);
    setActiveJobId(null);
    setSelectedTools([]);
  };

  const stepIndicators = [
    { key: 'input', label: 'Input Data', icon: '📁' },
    { key: 'configure', label: 'Configure', icon: '⚙️' },
    { key: 'processing', label: 'Processing', icon: '🔄' },
    { key: 'results', label: 'Results', icon: '📊' },
  ];

  return (
    <div className="space-y-6">
      {/* Step Indicator */}
      <div className="bg-zinc-800 border border-black rounded-lg p-6">
        <div className="flex items-center justify-between">
          {stepIndicators.map((step, index) => (
            <div key={step.key} className="flex items-center">
              <div className={`
                flex items-center justify-center w-10 h-10 rounded-full border-2 transition-colors
                ${currentStep === step.key 
                  ? 'bg-orange-500 border-orange-500 text-white' 
                  : stepIndicators.findIndex(s => s.key === currentStep) > index
                    ? 'bg-green-500 border-green-500 text-white'
                    : 'bg-zinc-700 border-zinc-600 text-gray-400'
                }
              `}>
                <span className="text-sm">{step.icon}</span>
              </div>
              <div className="ml-3">
                <div className={`font-medium ${
                  currentStep === step.key ? 'text-orange-400' : 'text-gray-400'
                }`}>
                  {step.label}
                </div>
              </div>
              {index < stepIndicators.length - 1 && (
                <div className={`w-16 h-0.5 mx-4 ${
                  stepIndicators.findIndex(s => s.key === currentStep) > index
                    ? 'bg-green-500'
                    : 'bg-zinc-600'
                }`} />
              )}
            </div>
          ))}
        </div>
      </div>

      {/* Step Content */}
      {currentStep === 'input' && (
        <div className="space-y-6">
          <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
            <FileUploadSection
              active={inputMethod === 'file'}
              onSelect={() => handleInputMethodSelect('file')}
              onDataProcessed={handleDataProcessed}
              onError={onError}
            />
            <ManualEntrySection
              active={inputMethod === 'manual'}
              onSelect={() => handleInputMethodSelect('manual')}
              onDataProcessed={handleDataProcessed}
              onError={onError}
            />
            <div
              className={`border-2 border-dashed rounded-lg p-6 transition-colors cursor-pointer ${
                inputMethod === 'resume'
                  ? 'border-orange-500 bg-orange-900/10'
                  : 'border-zinc-600 hover:border-zinc-500'
              }`}
              onClick={() => handleInputMethodSelect('resume')}
            >
              <div className="text-center">
                <div className="text-4xl mb-4">🔄</div>
                <h3 className="text-lg font-medium text-white mb-2">Resume AI Generation</h3>
                <p className="text-gray-400 text-sm">
                  Select tools that have scraped content but need AI content generation
                </p>
              </div>
            </div>
          </div>

          {inputMethod === 'resume' && (
            <ResumeToolSelector
              onToolsSelected={handleToolsSelected}
              selectedTools={selectedTools}
            />
          )}
        </div>
      )}

      {currentStep === 'configure' && processedData && (
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
          <div className="lg:col-span-2">
            <ProcessingOptionsPanel
              options={processingOptions}
              onChange={handleOptionsChange}
              dataPreview={processedData}
              onStartProcessing={handleStartProcessing}
              onBack={() => setCurrentStep('input')}
            />
          </div>
          <div>
            <ResultsViewer
              data={processedData}
              mode="preview"
            />
          </div>
        </div>
      )}

      {currentStep === 'processing' && activeJobId && (
        <ProgressTracker
          jobId={activeJobId}
          onComplete={handleJobComplete}
          onError={onError}
        />
      )}

      {currentStep === 'results' && (
        <div className="space-y-6">
          <div className="flex justify-between items-center">
            <h2 className="text-2xl font-bold">Processing Complete</h2>
            <button
              onClick={handleReset}
              className="bg-orange-500 hover:bg-orange-600 px-4 py-2 rounded-lg font-medium transition-colors"
            >
              Start New Batch
            </button>
          </div>
          <ResultsViewer
            jobId={activeJobId || undefined}
            mode="results"
          />
        </div>
      )}

      {/* Job History */}
      <BulkJobHistory
        jobs={jobs}
        loading={loading}
        onJobSelect={(jobId) => {
          setActiveJobId(jobId);
          setCurrentStep('results');
        }}
      />
    </div>
  );
}
