import { NextRequest, NextResponse } from 'next/server';
import { submitTool } from '@/lib/supabase';
import { getJobManager } from '@/lib/jobs';
import { JobType, JobPriority } from '@/lib/jobs/types';

export async function POST(request: NextRequest) {
  try {
    const submissionData = await request.json();

    // Validate required fields
    const { name, url, description, category, submitterEmail, submissionType } = submissionData;

    if (!name || !url || !description || !category || !submitterEmail) {
      return NextResponse.json(
        { success: false, error: 'Missing required fields: name, url, description, category, submitterEmail' },
        { status: 400 }
      );
    }

    // Validate URL format
    try {
      new URL(url);
    } catch {
      return NextResponse.json(
        { success: false, error: 'Invalid URL format' },
        { status: 400 }
      );
    }

    // Determine submission type (default to simple for backward compatibility)
    const isDetailedSubmission = submissionType === 'detailed';

    console.log(`📝 Processing ${isDetailedSubmission ? 'detailed' : 'simple'} submission for: ${name}`);

    // Validate email format
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!emailRegex.test(submitterEmail)) {
      return NextResponse.json(
        { success: false, error: 'Invalid email format' },
        { status: 400 }
      );
    }

    // Store submission with type information
    await submitTool({
      ...submissionData,
      submissionType: isDetailedSubmission ? 'detailed' : 'simple'
    });

    // Handle different submission types
    if (isDetailedSubmission) {
      // Detailed submissions go directly to editorial review
      console.log('📋 Detailed submission - queuing for editorial review');

      // TODO: Create editorial review record directly
      // For now, just log that it's ready for review

    } else {
      // Simple submissions trigger automated processing
      console.log('⚡ Simple submission - queuing for AI processing');

      if (process.env.JOB_QUEUE_ENABLED === 'true') {
        try {
          // Use enhanced job system for better monitoring
          const jobManager = getJobManager();
          await jobManager.createJob(
            JobType.TOOL_SUBMISSION,
            {
              url: submissionData.url,
              name: submissionData.name,
              description: submissionData.description,
              category: submissionData.category,
              submitterEmail: submissionData.submitterEmail,
              submitterName: submissionData.submitterName,
              submissionType: 'simple',
            },
            {
              priority: JobPriority.NORMAL,
            }
          );
          console.log('✅ Simple submission processing job queued successfully');
        } catch (jobError) {
          console.error('❌ Failed to queue tool processing job:', jobError);
          // Don't fail the request if job queueing fails
        }
      }
    }

    return NextResponse.json({
      success: true,
      message: 'Tool submitted successfully. It will be reviewed and published if approved.',
    });

  } catch (error) {
    console.error('Tool submission error:', error);
    return NextResponse.json(
      { success: false, error: 'Failed to submit tool' },
      { status: 500 }
    );
  }
}
