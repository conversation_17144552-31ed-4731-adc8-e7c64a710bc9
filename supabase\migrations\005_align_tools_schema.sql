-- Align tools table schema with detailed submission fields
-- Migration: 005_align_tools_schema.sql

-- Add missing fields from tool_submissions to tools table
DO $$ BEGIN
  IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name='tools' AND column_name='detailed_description') THEN
    ALTER TABLE tools ADD COLUMN detailed_description TEXT;
    COMMENT ON COLUMN tools.detailed_description IS 'Comprehensive tool description for detail pages';
  END IF;
END $$;

DO $$ BEGIN
  IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name='tools' AND column_name='use_cases') THEN
    ALTER TABLE tools ADD COLUMN use_cases TEXT;
    COMMENT ON COLUMN tools.use_cases IS 'Common use cases and scenarios';
  END IF;
END $$;

DO $$ BEGIN
  IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name='tools' AND column_name='target_audience') THEN
    ALTER TABLE tools ADD COLUMN target_audience TEXT;
    COMMENT ON COLUMN tools.target_audience IS 'Primary target audience description';
  END IF;
END $$;

DO $$ BEGIN
  IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name='tools' AND column_name='integrations') THEN
    ALTER TABLE tools ADD COLUMN integrations TEXT;
    COMMENT ON COLUMN tools.integrations IS 'Third-party integrations and compatibility';
  END IF;
END $$;

DO $$ BEGIN
  IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name='tools' AND column_name='tags') THEN
    ALTER TABLE tools ADD COLUMN tags TEXT[];
    COMMENT ON COLUMN tools.tags IS 'Array of tool tags for categorization';
  END IF;
END $$;

DO $$ BEGIN
  IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name='tools' AND column_name='meta_keywords') THEN
    ALTER TABLE tools ADD COLUMN meta_keywords TEXT;
    COMMENT ON COLUMN tools.meta_keywords IS 'SEO keywords for search optimization';
  END IF;
END $$;

-- Add submission tracking fields
DO $$ BEGIN
  IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name='tools' AND column_name='submission_id') THEN
    ALTER TABLE tools ADD COLUMN submission_id UUID;
    COMMENT ON COLUMN tools.submission_id IS 'Reference to original tool_submissions record';
  END IF;
END $$;

DO $$ BEGIN
  IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name='tools' AND column_name='submitted_by') THEN
    ALTER TABLE tools ADD COLUMN submitted_by VARCHAR(255);
    COMMENT ON COLUMN tools.submitted_by IS 'Original submitter name/email';
  END IF;
END $$;

DO $$ BEGIN
  IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name='tools' AND column_name='submission_date') THEN
    ALTER TABLE tools ADD COLUMN submission_date TIMESTAMP;
    COMMENT ON COLUMN tools.submission_date IS 'Original submission timestamp';
  END IF;
END $$;

-- Add editorial workflow fields
DO $$ BEGIN
  IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name='tools' AND column_name='editorial_notes') THEN
    ALTER TABLE tools ADD COLUMN editorial_notes TEXT;
    COMMENT ON COLUMN tools.editorial_notes IS 'Internal editorial notes and comments';
  END IF;
END $$;

DO $$ BEGIN
  IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name='tools' AND column_name='approved_by') THEN
    ALTER TABLE tools ADD COLUMN approved_by VARCHAR(255);
    COMMENT ON COLUMN tools.approved_by IS 'Admin who approved the submission';
  END IF;
END $$;

DO $$ BEGIN
  IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name='tools' AND column_name='approved_at') THEN
    ALTER TABLE tools ADD COLUMN approved_at TIMESTAMP;
    COMMENT ON COLUMN tools.approved_at IS 'Timestamp of approval';
  END IF;
END $$;

-- Add indexes for performance
CREATE INDEX IF NOT EXISTS idx_tools_submission_id ON tools(submission_id);
CREATE INDEX IF NOT EXISTS idx_tools_submitted_by ON tools(submitted_by);
CREATE INDEX IF NOT EXISTS idx_tools_submission_date ON tools(submission_date);
CREATE INDEX IF NOT EXISTS idx_tools_tags ON tools USING GIN (tags);
CREATE INDEX IF NOT EXISTS idx_tools_use_cases ON tools USING GIN (to_tsvector('english', use_cases));
CREATE INDEX IF NOT EXISTS idx_tools_target_audience ON tools USING GIN (to_tsvector('english', target_audience));

-- Add foreign key constraint
DO $$ BEGIN
  IF NOT EXISTS (SELECT 1 FROM information_schema.table_constraints WHERE constraint_name='fk_tools_submission_id') THEN
    ALTER TABLE tools ADD CONSTRAINT fk_tools_submission_id 
    FOREIGN KEY (submission_id) REFERENCES tool_submissions(id) ON DELETE SET NULL;
  END IF;
END $$;
