'use client';

import React, { useState } from 'react';
import { Zap, RefreshCw, CheckCircle, AlertCircle, Info } from 'lucide-react';

export type PartialSectionType = 'features' | 'pricing' | 'pros_cons' | 'seo' | 'faqs' | 'social' | 'company' | 'content';

interface PartialContentGeneratorProps {
  toolData: any;
  scrapedContent: string;
  toolUrl: string;
  onGenerate: (sectionType: PartialSectionType) => Promise<void>;
  isGenerating?: boolean;
  lastGenerated?: {
    section: PartialSectionType;
    timestamp: string;
    success: boolean;
  };
}

interface SectionOption {
  id: PartialSectionType;
  name: string;
  description: string;
  fields: string[];
  estimatedTime: string;
  priority: 'high' | 'medium' | 'low';
}

const sectionOptions: SectionOption[] = [
  {
    id: 'content',
    name: 'Core Content',
    description: 'Generate description, short_description, and detailed_description',
    fields: ['description', 'short_description', 'detailed_description'],
    estimatedTime: '30-45s',
    priority: 'high'
  },
  {
    id: 'features',
    name: 'Features',
    description: 'Generate 3-8 key features list',
    fields: ['features'],
    estimatedTime: '20-30s',
    priority: 'high'
  },
  {
    id: 'pricing',
    name: 'Pricing Information',
    description: 'Analyze and structure pricing data',
    fields: ['pricing'],
    estimatedTime: '25-35s',
    priority: 'high'
  },
  {
    id: 'pros_cons',
    name: 'Pros & Cons',
    description: 'Generate balanced 3-10 pros and cons',
    fields: ['pros_and_cons'],
    estimatedTime: '30-40s',
    priority: 'medium'
  },
  {
    id: 'seo',
    name: 'SEO Content',
    description: 'Generate meta_title and meta_description',
    fields: ['meta_title', 'meta_description'],
    estimatedTime: '15-25s',
    priority: 'medium'
  },
  {
    id: 'faqs',
    name: 'FAQs',
    description: 'Generate 3-5 relevant Q&As with metadata',
    fields: ['faqs'],
    estimatedTime: '35-45s',
    priority: 'medium'
  },
  {
    id: 'social',
    name: 'Social Links',
    description: 'Extract and format social media links',
    fields: ['social_links'],
    estimatedTime: '10-20s',
    priority: 'low'
  },
  {
    id: 'company',
    name: 'Company Info',
    description: 'Extract company name and details',
    fields: ['company'],
    estimatedTime: '15-25s',
    priority: 'low'
  }
];

export function PartialContentGenerator({
  toolData,
  scrapedContent,
  toolUrl,
  onGenerate,
  isGenerating = false,
  lastGenerated
}: PartialContentGeneratorProps) {
  const [selectedSection, setSelectedSection] = useState<SectionType | null>(null);

  const handleGenerate = async () => {
    if (selectedSection && !isGenerating) {
      await onGenerate(selectedSection);
    }
  };

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case 'high': return 'text-red-400';
      case 'medium': return 'text-yellow-400';
      case 'low': return 'text-green-400';
      default: return 'text-gray-400';
    }
  };

  const getPriorityBg = (priority: string) => {
    switch (priority) {
      case 'high': return 'bg-red-500/20';
      case 'medium': return 'bg-yellow-500/20';
      case 'low': return 'bg-green-500/20';
      default: return 'bg-gray-500/20';
    }
  };

  return (
    <div className="bg-zinc-800 border border-zinc-700 rounded-lg p-6">
      <div className="flex items-center gap-2 mb-4">
        <Zap className="w-5 h-5 text-orange-500" />
        <h3 className="text-lg font-semibold text-white">AI Dude Partial Content Generation</h3>
      </div>

      <div className="mb-6 p-4 bg-zinc-900 border border-zinc-700 rounded-lg">
        <div className="flex items-start gap-2">
          <Info className="w-4 h-4 text-blue-400 mt-0.5 flex-shrink-0" />
          <div className="text-sm text-gray-300">
            <p className="mb-2">
              Generate specific content sections using existing tool data as context. 
              This maintains consistency while updating individual sections.
            </p>
            <p className="text-gray-400">
              <strong>Tool:</strong> {toolData?.name || 'Unknown'} • 
              <strong> URL:</strong> {toolUrl}
            </p>
          </div>
        </div>
      </div>

      {/* Section Selection */}
      <div className="mb-6">
        <h4 className="text-sm font-medium text-white mb-3">Select Section to Generate:</h4>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
          {sectionOptions.map((section) => {
            const isSelected = selectedSection === section.id;
            const hasExistingData = section.fields.some(field => 
              toolData && toolData[field] && 
              (Array.isArray(toolData[field]) ? toolData[field].length > 0 : toolData[field])
            );

            return (
              <div
                key={section.id}
                className={`
                  border rounded-lg p-3 cursor-pointer transition-all duration-200
                  ${isSelected 
                    ? 'border-orange-500 bg-orange-500/10' 
                    : 'border-zinc-600 bg-zinc-900 hover:border-zinc-500'
                  }
                `}
                onClick={() => setSelectedSection(section.id)}
              >
                <div className="flex items-start justify-between mb-2">
                  <div className="flex items-center gap-2">
                    <h5 className="font-medium text-white">{section.name}</h5>
                    {hasExistingData && (
                      <CheckCircle className="w-4 h-4 text-green-400" />
                    )}
                  </div>
                  <div className={`px-2 py-1 rounded text-xs font-medium ${getPriorityBg(section.priority)} ${getPriorityColor(section.priority)}`}>
                    {section.priority}
                  </div>
                </div>
                
                <p className="text-sm text-gray-400 mb-2">{section.description}</p>
                
                <div className="flex items-center justify-between text-xs">
                  <span className="text-gray-500">
                    Fields: {section.fields.join(', ')}
                  </span>
                  <span className="text-gray-500">
                    ~{section.estimatedTime}
                  </span>
                </div>
              </div>
            );
          })}
        </div>
      </div>

      {/* Generation Controls */}
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-4">
          {selectedSection && (
            <div className="text-sm text-gray-300">
              <span className="text-white font-medium">
                {sectionOptions.find(s => s.id === selectedSection)?.name}
              </span>
              <span className="text-gray-400 ml-2">
                • {sectionOptions.find(s => s.id === selectedSection)?.estimatedTime}
              </span>
            </div>
          )}
        </div>

        <button
          onClick={handleGenerate}
          disabled={!selectedSection || isGenerating}
          className={`
            flex items-center gap-2 px-4 py-2 rounded-lg font-medium transition-all duration-200
            ${!selectedSection || isGenerating
              ? 'bg-zinc-700 text-gray-400 cursor-not-allowed'
              : 'bg-orange-500 text-white hover:bg-orange-600'
            }
          `}
        >
          {isGenerating ? (
            <>
              <RefreshCw className="w-4 h-4 animate-spin" />
              Generating...
            </>
          ) : (
            <>
              <Zap className="w-4 h-4" />
              Generate Section
            </>
          )}
        </button>
      </div>

      {/* Last Generation Status */}
      {lastGenerated && (
        <div className="mt-4 p-3 bg-zinc-900 border border-zinc-700 rounded-lg">
          <div className="flex items-center gap-2 text-sm">
            {lastGenerated.success ? (
              <CheckCircle className="w-4 h-4 text-green-400" />
            ) : (
              <AlertCircle className="w-4 h-4 text-red-400" />
            )}
            <span className="text-gray-300">
              Last generated: <span className="text-white font-medium">
                {sectionOptions.find(s => s.id === lastGenerated.section)?.name}
              </span>
              <span className="text-gray-400 ml-2">
                • {new Date(lastGenerated.timestamp).toLocaleTimeString()}
              </span>
              {lastGenerated.success ? (
                <span className="text-green-400 ml-2">✓ Success</span>
              ) : (
                <span className="text-red-400 ml-2">✗ Failed</span>
              )}
            </span>
          </div>
        </div>
      )}
    </div>
  );
}

export default PartialContentGenerator;

// Export types for use in other components
export type { PartialSectionType, PartialContentGeneratorProps };
