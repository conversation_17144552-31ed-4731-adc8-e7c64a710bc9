'use client';

import React, { useState, useEffect } from 'react';

interface ContentOverrideEditorProps {
  toolId: string;
  toolName: string;
  generatedContent: any;
  onSave: (updatedContent: any) => void;
  onCancel: () => void;
  className?: string;
}

interface ContentSection {
  key: string;
  label: string;
  type: 'text' | 'textarea' | 'array' | 'object';
  value: any;
  modified: boolean;
}

export function ContentOverrideEditor({
  toolId,
  toolName,
  generatedContent,
  onSave,
  onCancel,
  className = ''
}: ContentOverrideEditorProps) {
  const [editableContent, setEditableContent] = useState<any>(generatedContent);
  const [contentSections, setContentSections] = useState<ContentSection[]>([]);
  const [hasChanges, setHasChanges] = useState(false);
  const [saving, setSaving] = useState(false);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    // Parse generated content into editable sections
    const sections = parseContentIntoSections(generatedContent);
    setContentSections(sections);
  }, [generatedContent]);

  const parseContentIntoSections = (content: any): ContentSection[] => {
    const sections: ContentSection[] = [];

    // Define the content fields we want to make editable
    const editableFields = [
      { key: 'name', label: 'Tool Name', type: 'text' },
      { key: 'short_description', label: 'Short Description', type: 'textarea' },
      { key: 'detailed_description', label: 'Detailed Description', type: 'textarea' },
      { key: 'features', label: 'Features', type: 'array' },
      { key: 'pricing', label: 'Pricing', type: 'object' },
      { key: 'pros_and_cons', label: 'Pros & Cons', type: 'object' },
      { key: 'meta_title', label: 'SEO Title', type: 'text' },
      { key: 'meta_description', label: 'SEO Description', type: 'textarea' },
      { key: 'meta_keywords', label: 'SEO Keywords', type: 'array' },
      { key: 'faqs', label: 'FAQs', type: 'array' }
    ];

    editableFields.forEach(field => {
      if (content[field.key] !== undefined) {
        sections.push({
          key: field.key,
          label: field.label,
          type: field.type as any,
          value: content[field.key],
          modified: false
        });
      }
    });

    return sections;
  };

  const handleSectionChange = (sectionKey: string, newValue: any) => {
    setContentSections(prev => prev.map(section => 
      section.key === sectionKey 
        ? { ...section, value: newValue, modified: true }
        : section
    ));

    setEditableContent(prev => ({
      ...prev,
      [sectionKey]: newValue
    }));

    setHasChanges(true);
  };

  const handleArrayItemChange = (sectionKey: string, index: number, newValue: string) => {
    const section = contentSections.find(s => s.key === sectionKey);
    if (section && Array.isArray(section.value)) {
      const newArray = [...section.value];
      newArray[index] = newValue;
      handleSectionChange(sectionKey, newArray);
    }
  };

  const handleArrayItemAdd = (sectionKey: string) => {
    const section = contentSections.find(s => s.key === sectionKey);
    if (section && Array.isArray(section.value)) {
      const newArray = [...section.value, ''];
      handleSectionChange(sectionKey, newArray);
    }
  };

  const handleArrayItemRemove = (sectionKey: string, index: number) => {
    const section = contentSections.find(s => s.key === sectionKey);
    if (section && Array.isArray(section.value)) {
      const newArray = section.value.filter((_, i) => i !== index);
      handleSectionChange(sectionKey, newArray);
    }
  };

  const handleSave = async () => {
    try {
      setSaving(true);
      setError(null);

      // Validate content
      const validationErrors = validateContent(editableContent);
      if (validationErrors.length > 0) {
        setError(`Validation errors: ${validationErrors.join(', ')}`);
        return;
      }

      // Save the updated content
      await onSave(editableContent);
      setHasChanges(false);

    } catch (err) {
      console.error('Error saving content:', err);
      setError(err instanceof Error ? err.message : 'Failed to save content');
    } finally {
      setSaving(false);
    }
  };

  const validateContent = (content: any): string[] => {
    const errors: string[] = [];

    if (!content.name || content.name.trim().length === 0) {
      errors.push('Tool name is required');
    }

    if (!content.short_description || content.short_description.trim().length === 0) {
      errors.push('Short description is required');
    }

    if (content.short_description && content.short_description.length > 200) {
      errors.push('Short description must be under 200 characters');
    }

    if (content.detailed_description && content.detailed_description.length > 2000) {
      errors.push('Detailed description must be under 2000 characters');
    }

    return errors;
  };

  const renderSectionEditor = (section: ContentSection) => {
    switch (section.type) {
      case 'text':
        return (
          <input
            type="text"
            value={section.value || ''}
            onChange={(e) => handleSectionChange(section.key, e.target.value)}
            className="w-full px-3 py-2 bg-zinc-700 border border-zinc-600 rounded-md text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-orange-500 focus:border-transparent"
          />
        );

      case 'textarea':
        return (
          <textarea
            value={section.value || ''}
            onChange={(e) => handleSectionChange(section.key, e.target.value)}
            rows={section.key === 'detailed_description' ? 6 : 3}
            className="w-full px-3 py-2 bg-zinc-700 border border-zinc-600 rounded-md text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-orange-500 focus:border-transparent resize-vertical"
          />
        );

      case 'array':
        return (
          <div className="space-y-2">
            {(section.value || []).map((item: string, index: number) => (
              <div key={index} className="flex items-center space-x-2">
                <input
                  type="text"
                  value={item}
                  onChange={(e) => handleArrayItemChange(section.key, index, e.target.value)}
                  className="flex-1 px-3 py-2 bg-zinc-700 border border-zinc-600 rounded-md text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-orange-500 focus:border-transparent"
                />
                <button
                  onClick={() => handleArrayItemRemove(section.key, index)}
                  className="p-2 text-red-400 hover:text-red-300"
                >
                  <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
                  </svg>
                </button>
              </div>
            ))}
            <button
              onClick={() => handleArrayItemAdd(section.key)}
              className="px-3 py-1 bg-zinc-600 hover:bg-zinc-500 text-white rounded text-sm"
            >
              Add Item
            </button>
          </div>
        );

      case 'object':
        return (
          <div className="bg-zinc-700 rounded-md p-3">
            <pre className="text-sm text-gray-300 whitespace-pre-wrap">
              {JSON.stringify(section.value, null, 2)}
            </pre>
            <p className="text-xs text-gray-400 mt-2">
              Complex objects can be edited in the raw JSON view (coming soon)
            </p>
          </div>
        );

      default:
        return (
          <div className="text-gray-400 text-sm">
            Unsupported field type: {section.type}
          </div>
        );
    }
  };

  return (
    <div className={`content-override-editor bg-zinc-800 border border-zinc-700 rounded-lg ${className}`}>
      {/* Header */}
      <div className="border-b border-zinc-700 p-6">
        <div className="flex items-center justify-between">
          <div>
            <h2 className="text-xl font-semibold text-white">Edit Generated Content</h2>
            <p className="text-gray-400 text-sm mt-1">
              Review and modify AI-generated content for {toolName}
            </p>
          </div>
          <div className="flex items-center space-x-3">
            <button
              onClick={onCancel}
              className="px-4 py-2 bg-zinc-600 hover:bg-zinc-500 text-white rounded-md transition-colors"
            >
              Cancel
            </button>
            <button
              onClick={handleSave}
              disabled={!hasChanges || saving}
              className="px-4 py-2 bg-orange-600 hover:bg-orange-700 disabled:bg-orange-800 disabled:opacity-50 text-white rounded-md transition-colors flex items-center gap-2"
            >
              {saving ? (
                <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
              ) : (
                <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                </svg>
              )}
              Save Changes
            </button>
          </div>
        </div>
      </div>

      {/* Content Sections */}
      <div className="p-6 space-y-6 max-h-96 overflow-y-auto">
        {contentSections.map((section) => (
          <div key={section.key} className="space-y-2">
            <div className="flex items-center space-x-2">
              <label className="text-sm font-medium text-white">
                {section.label}
              </label>
              {section.modified && (
                <span className="px-2 py-1 bg-orange-600 text-white text-xs rounded">
                  Modified
                </span>
              )}
            </div>
            {renderSectionEditor(section)}
          </div>
        ))}
      </div>

      {/* Error Display */}
      {error && (
        <div className="border-t border-zinc-700 p-4">
          <div className="bg-red-900/20 border border-red-500 rounded-lg p-3">
            <div className="flex items-center space-x-2">
              <svg className="w-5 h-5 text-red-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
              </svg>
              <span className="text-red-400 text-sm">{error}</span>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}
