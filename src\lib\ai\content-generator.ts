import { OpenAIClient } from './providers/openai-client';
import { OpenRouterClient } from './providers/openrouter-client';
import { ModelSelector } from './model-selector';
import { PromptManager } from './prompt-manager';
import { ContextWindowManager } from './context-window-manager';
import {
  GenerationOptions,
  GenerationResult,
  GeneratedContent,
  ValidationResult,
  ModelConfig,
  AIResponse,
  MultiPromptContext
} from './types';
import { log } from '@/lib/logging';

export class AIContentGenerator {
  private openaiClient: OpenAIClient;
  private openrouterClient: OpenRouterClient;

  constructor() {
    this.openaiClient = new OpenAIClient();
    this.openrouterClient = new OpenRouterClient();
  }

  /**
   * Main content generation method using AI Dude methodology
   */
  async generateContent(
    scrapedContent: string,
    toolUrl: string,
    options: GenerationOptions = {}
  ): Promise<GenerationResult> {
    try {
      // Build AI Dude prompts using new template structure
      const systemPrompt = await PromptManager.buildAIDudeSystemPrompt(
        PromptManager.getAIDudeDatabaseSchema()
      );
      const userPrompt = await PromptManager.buildAIDudeUserPrompt(scrapedContent, toolUrl);

      // Select optimal model
      const modelConfig = ModelSelector.selectOptimalModel({
        contentSize: PromptManager.calculateTokenCount(scrapedContent),
        complexity: options.complexity || 'medium',
        priority: options.priority || 'quality',
        features: ['json_output', 'large_context']
      });

      log.ai('ai-dude-generation', `Using AI Dude methodology with ${modelConfig.model}`, {
        toolUrl,
        model: modelConfig.model,
        provider: modelConfig.provider,
        methodology: 'ai_dude'
      });

      // Generate content
      const client = modelConfig.provider === 'openai' ? this.openaiClient : this.openrouterClient;

      const response = await client.generateContent(systemPrompt, userPrompt, {
        model: modelConfig.model,
        responseFormat: 'json_object',
        maxTokens: Math.floor(modelConfig.maxTokens * 0.8),
        temperature: 0.7
      });

      // Process and validate response
      const parsedContent = PromptManager.extractJsonFromResponse(response.content);
      const mappedContent = PromptManager.processAIDudeResponse(parsedContent);

      // Validate against schema
      const validation = await this.validateAIDudeContent(mappedContent);

      return {
        success: true,
        content: mappedContent,
        validation,
        modelUsed: modelConfig,
        tokenUsage: response.tokenUsage,
        timestamp: new Date().toISOString(),
        methodology: 'ai_dude'
      };

    } catch (error: any) {
      log.ai('ai-dude-generation-error', `AI Dude generation failed: ${error.message}`, {
        toolUrl,
        error: error.message
      });

      return {
        success: false,
        error: error.message,
        timestamp: new Date().toISOString(),
        methodology: 'ai_dude'
      };
    }
  }





  /**
   * Retry operation with exponential backoff
   */
  private async retryWithBackoff<T>(
    operation: () => Promise<T>,
    maxRetries: number
  ): Promise<T> {
    let lastError: Error;

    for (let attempt = 1; attempt <= maxRetries; attempt++) {
      try {
        return await operation();
      } catch (error: any) {
        lastError = error;
        
        if (attempt === maxRetries) {
          break;
        }

        // Exponential backoff: 1s, 2s, 4s, 8s...
        const delay = Math.min(1000 * Math.pow(2, attempt - 1), 30000);
        log.ai('retry-attempt', `Attempt ${attempt} failed, retrying in ${delay}ms`, {
          attempt,
          maxRetries,
          delay,
          error: error.message
        });
        
        await new Promise(resolve => setTimeout(resolve, delay));
      }
    }

    throw lastError!;
  }

  /**
   * Get provider status and capabilities
   */
  async getProviderStatus(): Promise<{
    openai: { available: boolean; info: any };
    openrouter: { available: boolean; info: any };
  }> {
    const [openaiStatus, openrouterStatus] = await Promise.allSettled([
      this.openaiClient.validateConnection(),
      this.openrouterClient.validateConnection()
    ]);

    return {
      openai: {
        available: openaiStatus.status === 'fulfilled' && openaiStatus.value,
        info: this.openaiClient.getProviderInfo()
      },
      openrouter: {
        available: openrouterStatus.status === 'fulfilled' && openrouterStatus.value,
        info: this.openrouterClient.getProviderInfo()
      }
    };
  }

  // ===== AI DUDE METHODOLOGY METHODS =====

  /**
   * Generate partial content using AI Dude methodology with existing tool context
   */
  async generatePartialContentAIDude(
    sectionType: string,
    existingToolData: any,
    scrapedContent: string,
    toolUrl: string,
    options: GenerationOptions = {}
  ): Promise<GenerationResult> {
    try {
      // Build partial generation prompts using new template structure
      const systemPrompt = await PromptManager.buildPartialSystemPrompt(
        sectionType,
        PromptManager.getSectionRequirements(sectionType)
      );
      const userPrompt = await PromptManager.buildPartialUserPrompt(
        existingToolData,
        scrapedContent,
        toolUrl,
        sectionType
      );

      // Select optimal model
      const modelConfig = ModelSelector.selectOptimalModel({
        contentSize: PromptManager.calculateTokenCount(userPrompt),
        complexity: options.complexity || 'medium',
        priority: options.priority || 'quality',
        features: ['json_output']
      });

      log.ai('ai-dude-partial-generation', `Generating ${sectionType} section with AI Dude methodology`, {
        toolUrl,
        sectionType,
        model: modelConfig.model,
        methodology: 'ai_dude_partial'
      });

      // Generate content
      const client = modelConfig.provider === 'openai' ? this.openaiClient : this.openrouterClient;

      const response = await client.generateContent(systemPrompt, userPrompt, {
        model: modelConfig.model,
        responseFormat: 'json_object',
        maxTokens: Math.floor(modelConfig.maxTokens * 0.6),
        temperature: 0.7
      });

      // Process partial response
      const parsedContent = PromptManager.extractJsonFromResponse(response.content);
      const updatedContent = PromptManager.processPartialAIDudeResponse(
        parsedContent,
        sectionType,
        existingToolData
      );

      // Validate updated content
      const validation = await this.validateAIDudeContent(updatedContent);

      return {
        success: true,
        content: updatedContent,
        validation,
        modelUsed: modelConfig,
        tokenUsage: response.tokenUsage,
        timestamp: new Date().toISOString(),
        methodology: 'ai_dude_partial',
        sectionType
      };

    } catch (error: any) {
      log.ai('ai-dude-partial-error', `AI Dude partial generation failed: ${error.message}`, {
        toolUrl,
        sectionType,
        error: error.message
      });

      return {
        success: false,
        error: error.message,
        timestamp: new Date().toISOString(),
        methodology: 'ai_dude_partial',
        sectionType
      };
    }
  }

  /**
   * Validate AI Dude generated content against schema and quality requirements
   */
  private async validateAIDudeContent(content: any): Promise<ValidationResult> {
    const issues: string[] = [];

    // Check required fields for AI Dude methodology
    const requiredFields = [
      'name', 'description', 'short_description', 'detailed_description',
      'company', 'category_id', 'features', 'pricing', 'pros_and_cons',
      'hashtags', 'meta_title', 'meta_description'
    ];

    for (const field of requiredFields) {
      if (!content[field]) {
        issues.push(`Missing required field: ${field}`);
      }
    }

    // Validate field lengths and formats
    if (content.short_description && content.short_description.length > 150) {
      issues.push('Short description exceeds 150 characters');
    }

    if (content.detailed_description) {
      if (content.detailed_description.length < 150) {
        issues.push('Detailed description too short (<150 chars)');
      }
      if (content.detailed_description.length > 300) {
        issues.push('Detailed description too long (>300 words estimated)');
      }
    }

    // Validate meta fields for SEO
    if (content.meta_title && content.meta_title.length > 60) {
      issues.push('Meta title exceeds 60 characters');
    }

    if (content.meta_description) {
      if (content.meta_description.length < 150) {
        issues.push('Meta description too short (<150 chars)');
      }
      if (content.meta_description.length > 160) {
        issues.push('Meta description too long (>160 chars)');
      }
    }

    // Validate features count
    if (content.features && (content.features.length < 3 || content.features.length > 8)) {
      issues.push('Features count should be between 3-8');
    }

    // Validate pricing structure
    if (content.pricing) {
      const validPricingTypes = ['Free', 'Paid', 'Freemium', 'Open Source'];
      if (!validPricingTypes.includes(content.pricing.type)) {
        issues.push('Invalid pricing type');
      }
    }

    // Validate pros and cons
    if (content.pros_and_cons) {
      if (!content.pros_and_cons.pros || content.pros_and_cons.pros.length < 3) {
        issues.push('Need at least 3 pros');
      }
      if (!content.pros_and_cons.cons || content.pros_and_cons.cons.length < 3) {
        issues.push('Need at least 3 cons');
      }
      if (content.pros_and_cons.pros && content.pros_and_cons.pros.length > 10) {
        issues.push('Too many pros (max 10)');
      }
      if (content.pros_and_cons.cons && content.pros_and_cons.cons.length > 10) {
        issues.push('Too many cons (max 10)');
      }
    }

    // Validate FAQs structure
    if (content.faqs && content.faqs.length > 0) {
      for (const [index, faq] of content.faqs.entries()) {
        if (!faq.id || !faq.question || !faq.answer) {
          issues.push(`FAQ ${index + 1} missing required fields (id, question, answer)`);
        }
        if (!faq.category || !['general', 'pricing', 'features', 'support', 'getting-started'].includes(faq.category)) {
          issues.push(`FAQ ${index + 1} has invalid category`);
        }
      }
    }

    // Validate hashtags
    if (content.hashtags && (content.hashtags.length < 5 || content.hashtags.length > 10)) {
      issues.push('Hashtags count should be between 5-10');
    }

    // Validate tooltip length
    if (content.tooltip && content.tooltip.length > 100) {
      issues.push('Tooltip exceeds 100 character limit');
    }

    // Check for AI Dude tone indicators (basic check)
    const toneFields = ['description', 'detailed_description'];
    let hasToneIndicators = false;

    for (const field of toneFields) {
      if (content[field]) {
        const text = content[field].toLowerCase();
        const toneIndicators = ["'", 'no-bs', 'dude', 'snarky', 'witty'];
        if (toneIndicators.some(indicator => text.includes(indicator))) {
          hasToneIndicators = true;
          break;
        }
      }
    }

    if (!hasToneIndicators) {
      issues.push('Content may not reflect AI Dude tone and style');
    }

    return {
      isValid: issues.length === 0,
      issues,
      score: Math.max(0, 100 - (issues.length * 8)), // Quality score (slightly more lenient)
      methodology: 'ai_dude'
    };
  }
}
