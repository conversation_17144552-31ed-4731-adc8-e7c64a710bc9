/**
 * Unified Workflow Manager
 * 
 * Manages the complete tool workflow from submission through publication:
 * 1. Tool Submission/Creation
 * 2. Content Generation (AI Dude)
 * 3. Editorial Review
 * 4. Publication
 * 
 * Features:
 * - Workflow state tracking
 * - Transition management
 * - Progress monitoring
 * - Real-time updates
 * - Error handling and recovery
 */

import { supabase } from '@/lib/supabase';
import { getJobManager } from '@/lib/jobs';
import { JobType, JobPriority } from '@/lib/jobs/types';

export type WorkflowStage = 
  | 'draft'
  | 'content_generation'
  | 'content_review'
  | 'editorial_review'
  | 'approved'
  | 'published'
  | 'rejected'
  | 'error';

export interface WorkflowState {
  toolId: string;
  currentStage: WorkflowStage;
  progress: number; // 0-100
  startedAt: string;
  updatedAt: string;
  completedStages: WorkflowStage[];
  metadata: {
    contentGenerationJobId?: string;
    editorialReviewId?: string;
    lastError?: string;
    estimatedCompletion?: string;
  };
}

export interface WorkflowTransition {
  from: WorkflowStage;
  to: WorkflowStage;
  triggeredBy: string;
  timestamp: string;
  notes?: string;
  jobId?: string;
}

export class WorkflowManager {
  private static instance: WorkflowManager;

  public static getInstance(): WorkflowManager {
    if (!WorkflowManager.instance) {
      WorkflowManager.instance = new WorkflowManager();
    }
    return WorkflowManager.instance;
  }

  /**
   * Initialize workflow for a new tool
   */
  async initializeWorkflow(toolId: string, initialStage: WorkflowStage = 'draft'): Promise<WorkflowState> {
    const workflowState: WorkflowState = {
      toolId,
      currentStage: initialStage,
      progress: this.calculateProgress(initialStage),
      startedAt: new Date().toISOString(),
      updatedAt: new Date().toISOString(),
      completedStages: [],
      metadata: {}
    };

    // Store workflow state in database
    await this.saveWorkflowState(workflowState);
    
    return workflowState;
  }

  /**
   * Transition workflow to next stage
   */
  async transitionTo(
    toolId: string, 
    targetStage: WorkflowStage, 
    triggeredBy: string = 'system',
    notes?: string
  ): Promise<WorkflowState> {
    const currentState = await this.getWorkflowState(toolId);
    
    if (!currentState) {
      throw new Error(`Workflow state not found for tool ${toolId}`);
    }

    // Validate transition
    if (!this.isValidTransition(currentState.currentStage, targetStage)) {
      throw new Error(`Invalid transition from ${currentState.currentStage} to ${targetStage}`);
    }

    // Create transition record
    const transition: WorkflowTransition = {
      from: currentState.currentStage,
      to: targetStage,
      triggeredBy,
      timestamp: new Date().toISOString(),
      notes
    };

    // Update workflow state
    const updatedState: WorkflowState = {
      ...currentState,
      currentStage: targetStage,
      progress: this.calculateProgress(targetStage),
      updatedAt: new Date().toISOString(),
      completedStages: [...currentState.completedStages, currentState.currentStage]
    };

    // Save updated state and transition
    await Promise.all([
      this.saveWorkflowState(updatedState),
      this.saveWorkflowTransition(toolId, transition)
    ]);

    // Update tool record
    await this.updateToolWorkflowStatus(toolId, targetStage);

    return updatedState;
  }

  /**
   * Start content generation workflow
   */
  async startContentGeneration(toolId: string, options: {
    priority?: 'low' | 'normal' | 'high';
    triggeredBy?: string;
  } = {}): Promise<string> {
    const jobManager = getJobManager();
    
    // Get tool data
    const { data: tool, error } = await supabase
      .from('tools')
      .select('id, name, website, description, scraped_data')
      .eq('id', toolId)
      .single();

    if (error || !tool) {
      throw new Error(`Tool not found: ${toolId}`);
    }

    // Transition to content generation stage
    await this.transitionTo(toolId, 'content_generation', options.triggeredBy || 'admin');

    // Create content generation job
    const job = await jobManager.createJob(
      JobType.CONTENT_GENERATION,
      {
        toolId,
        url: tool.website,
        scrapedData: tool.scraped_data || {
          title: tool.name,
          description: tool.description,
          url: tool.website
        }
      },
      {
        priority: options.priority === 'high' ? JobPriority.HIGH : 
                 options.priority === 'low' ? JobPriority.LOW : JobPriority.NORMAL
      }
    );

    // Update workflow metadata
    const currentState = await this.getWorkflowState(toolId);
    if (currentState) {
      currentState.metadata.contentGenerationJobId = job.id;
      await this.saveWorkflowState(currentState);
    }

    return job.id;
  }

  /**
   * Start editorial review workflow
   */
  async startEditorialReview(toolId: string, triggeredBy: string = 'system'): Promise<string> {
    // Transition to editorial review stage
    await this.transitionTo(toolId, 'editorial_review', triggeredBy);

    // Create editorial review record
    const { data: review, error } = await supabase
      .from('editorial_reviews')
      .insert({
        tool_id: toolId,
        review_status: 'pending',
        created_at: new Date().toISOString()
      })
      .select('id')
      .single();

    if (error) {
      throw new Error(`Failed to create editorial review: ${error.message}`);
    }

    // Update workflow metadata
    const currentState = await this.getWorkflowState(toolId);
    if (currentState) {
      currentState.metadata.editorialReviewId = review.id;
      await this.saveWorkflowState(currentState);
    }

    return review.id;
  }

  /**
   * Get current workflow state
   */
  async getWorkflowState(toolId: string): Promise<WorkflowState | null> {
    const { data, error } = await supabase
      .from('workflow_states')
      .select('*')
      .eq('tool_id', toolId)
      .single();

    if (error || !data) {
      return null;
    }

    return {
      toolId: data.tool_id,
      currentStage: data.current_stage,
      progress: data.progress,
      startedAt: data.started_at,
      updatedAt: data.updated_at,
      completedStages: data.completed_stages || [],
      metadata: data.metadata || {}
    };
  }

  /**
   * Get workflow history
   */
  async getWorkflowHistory(toolId: string): Promise<WorkflowTransition[]> {
    const { data, error } = await supabase
      .from('workflow_transitions')
      .select('*')
      .eq('tool_id', toolId)
      .order('timestamp', { ascending: true });

    if (error) {
      throw new Error(`Failed to get workflow history: ${error.message}`);
    }

    return (data || []).map(row => ({
      from: row.from_stage,
      to: row.to_stage,
      triggeredBy: row.triggered_by,
      timestamp: row.timestamp,
      notes: row.notes,
      jobId: row.job_id
    }));
  }

  /**
   * Calculate progress percentage based on current stage
   */
  private calculateProgress(stage: WorkflowStage): number {
    const stageProgress = {
      'draft': 0,
      'content_generation': 25,
      'content_review': 50,
      'editorial_review': 75,
      'approved': 90,
      'published': 100,
      'rejected': 0,
      'error': 0
    };

    return stageProgress[stage] || 0;
  }

  /**
   * Validate workflow transition
   */
  private isValidTransition(from: WorkflowStage, to: WorkflowStage): boolean {
    const validTransitions: Record<WorkflowStage, WorkflowStage[]> = {
      'draft': ['content_generation', 'editorial_review'],
      'content_generation': ['content_review', 'error'],
      'content_review': ['editorial_review', 'content_generation', 'error'],
      'editorial_review': ['approved', 'rejected', 'content_generation'],
      'approved': ['published'],
      'published': [],
      'rejected': ['content_generation', 'editorial_review'],
      'error': ['content_generation', 'editorial_review']
    };

    return validTransitions[from]?.includes(to) || false;
  }

  /**
   * Save workflow state to database
   */
  private async saveWorkflowState(state: WorkflowState): Promise<void> {
    const { error } = await supabase
      .from('workflow_states')
      .upsert({
        tool_id: state.toolId,
        current_stage: state.currentStage,
        progress: state.progress,
        started_at: state.startedAt,
        updated_at: state.updatedAt,
        completed_stages: state.completedStages,
        metadata: state.metadata
      });

    if (error) {
      throw new Error(`Failed to save workflow state: ${error.message}`);
    }
  }

  /**
   * Save workflow transition to database
   */
  private async saveWorkflowTransition(toolId: string, transition: WorkflowTransition): Promise<void> {
    const { error } = await supabase
      .from('workflow_transitions')
      .insert({
        tool_id: toolId,
        from_stage: transition.from,
        to_stage: transition.to,
        triggered_by: transition.triggeredBy,
        timestamp: transition.timestamp,
        notes: transition.notes,
        job_id: transition.jobId
      });

    if (error) {
      throw new Error(`Failed to save workflow transition: ${error.message}`);
    }
  }

  /**
   * Update tool record with workflow status
   */
  private async updateToolWorkflowStatus(toolId: string, stage: WorkflowStage): Promise<void> {
    const statusMapping = {
      'draft': 'draft',
      'content_generation': 'draft',
      'content_review': 'draft',
      'editorial_review': 'under_review',
      'approved': 'approved',
      'published': 'published',
      'rejected': 'rejected',
      'error': 'draft'
    };

    const { error } = await supabase
      .from('tools')
      .update({
        content_status: statusMapping[stage],
        updated_at: new Date().toISOString()
      })
      .eq('id', toolId);

    if (error) {
      throw new Error(`Failed to update tool status: ${error.message}`);
    }
  }
}

export const workflowManager = WorkflowManager.getInstance();
