const http = require('http');

function testAPI(path, method = 'GET', data = null) {
  return new Promise((resolve, reject) => {
    const options = {
      hostname: 'localhost',
      port: 3000,
      path: path,
      method: method,
      headers: {
        'Content-Type': 'application/json',
        'x-api-key': 'aidude_admin_2024_secure_key_xyz789'
      }
    };

    const req = http.request(options, (res) => {
      let responseData = '';
      res.on('data', (chunk) => responseData += chunk);
      res.on('end', () => {
        try {
          const parsed = JSON.parse(responseData);
          resolve({ status: res.statusCode, data: parsed, raw: responseData });
        } catch (e) {
          resolve({ status: res.statusCode, data: responseData, raw: responseData });
        }
      });
    });

    req.on('error', (error) => {
      reject(error);
    });

    if (data) {
      req.write(JSON.stringify(data));
    }
    req.end();
  });
}

async function debugPromptsAPI() {
  console.log('🔍 Debugging Admin Prompts API...\n');

  try {
    const result = await testAPI('/api/admin/prompts');
    
    console.log(`Status: ${result.status}`);
    console.log(`Success: ${result.data.success}`);
    
    if (result.data.success) {
      const prompts = result.data.data || [];
      console.log(`Total prompts found: ${prompts.length}`);
      
      // Filter for AI Dude prompts
      const aiDudePrompts = prompts.filter(p => 
        p.name?.includes('AI Dude') || 
        p.id?.includes('ai_dude') ||
        p.category?.includes('ai_dude')
      );
      
      console.log(`AI Dude prompts found: ${aiDudePrompts.length}`);
      
      if (prompts.length > 0) {
        console.log('\n--- All Prompts ---');
        prompts.forEach((prompt, index) => {
          console.log(`${index + 1}. ${prompt.name || 'Unnamed'}`);
          console.log(`   ID: ${prompt.id}`);
          console.log(`   Category: ${prompt.category}`);
          console.log(`   Type: ${prompt.promptType}`);
          console.log(`   Active: ${prompt.isActive}`);
          console.log('');
        });
      }
      
      if (aiDudePrompts.length > 0) {
        console.log('--- AI Dude Prompts ---');
        aiDudePrompts.forEach((prompt, index) => {
          console.log(`${index + 1}. ${prompt.name}`);
          console.log(`   Template length: ${prompt.template?.length || 0} chars`);
          console.log(`   Variables: ${prompt.variables?.join(', ') || 'none'}`);
        });
      }
    } else {
      console.log(`Error: ${result.data.error}`);
      console.log(`Raw response: ${result.raw}`);
    }

  } catch (error) {
    console.error('Debug failed:', error.message);
  }
}

debugPromptsAPI();
