const http = require('http');

function testAPI(path, method = 'GET', data = null) {
  return new Promise((resolve, reject) => {
    const options = {
      hostname: 'localhost',
      port: 3000,
      path: path,
      method: method,
      headers: {
        'Content-Type': 'application/json',
        'x-api-key': 'aidude_admin_2024_secure_key_xyz789'
      }
    };

    const req = http.request(options, (res) => {
      let responseData = '';
      res.on('data', (chunk) => responseData += chunk);
      res.on('end', () => {
        try {
          const parsed = JSON.parse(responseData);
          resolve({ status: res.statusCode, data: parsed });
        } catch (e) {
          resolve({ status: res.statusCode, data: responseData });
        }
      });
    });

    req.on('error', (error) => {
      reject(error);
    });

    if (data) {
      req.write(JSON.stringify(data));
    }
    req.end();
  });
}

async function runTests() {
  console.log('🧪 Testing AI Dude System APIs...\n');

  try {
    // Test 1: Check admin prompts endpoint
    console.log('1. Testing /api/admin/prompts...');
    const promptsResult = await testAPI('/api/admin/prompts');
    console.log(`   Status: ${promptsResult.status}`);
    if (promptsResult.status === 200) {
      const aiDudePrompts = promptsResult.data.data?.filter(p => p.config_key?.includes('ai_dude')) || [];
      console.log(`   AI Dude prompts found: ${aiDudePrompts.length}`);
    } else {
      console.log(`   Error: ${JSON.stringify(promptsResult.data)}`);
    }

    // Test 2: Check AI health
    console.log('\n2. Testing /api/ai/test...');
    const healthResult = await testAPI('/api/ai/test', 'POST');
    console.log(`   Status: ${healthResult.status}`);
    if (healthResult.status === 200) {
      console.log(`   AI System Status: ${healthResult.data.success ? 'OK' : 'Error'}`);
    } else {
      console.log(`   Error: ${JSON.stringify(healthResult.data)}`);
    }

    // Test 3: Test AI Dude content generation
    console.log('\n3. Testing AI Dude content generation...');
    const testData = {
      url: 'https://openai.com',
      methodology: 'ai_dude',
      scrapedData: {
        title: 'OpenAI',
        description: 'AI research company',
        content: 'OpenAI is an AI research and deployment company. Our mission is to ensure that artificial general intelligence benefits all of humanity.'
      },
      options: {
        priority: 'quality',
        complexity: 'medium'
      }
    };

    const generateResult = await testAPI('/api/generate-content', 'POST', testData);
    console.log(`   Status: ${generateResult.status}`);
    if (generateResult.status === 200) {
      console.log(`   Generation Success: ${generateResult.data.success}`);
      if (generateResult.data.data?.aiContent) {
        const content = generateResult.data.data.aiContent;
        console.log(`   Generated fields: ${Object.keys(content).length}`);
        console.log(`   Methodology used: ${generateResult.data.data.generationMetadata?.methodology || 'unknown'}`);
      }
    } else {
      console.log(`   Error: ${JSON.stringify(generateResult.data)}`);
    }

  } catch (error) {
    console.error('Test failed:', error.message);
  }
}

runTests();
