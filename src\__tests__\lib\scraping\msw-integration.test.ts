/**
 * @jest-environment node
 */

import { describe, it, expect } from '@jest/globals';
import { mockScrapeDoResponse, mockSupabaseResponse, mockScrapedContent, mockSupabaseData } from '@/__mocks__/msw-server';

describe('MSW Integration Tests', () => {
  describe('Scrape.do API Mocking', () => {
    it('should mock scrape.do API responses', async () => {
      const response = await fetch('https://api.scrape.do?url=https://example.com');
      const data = await response.json();
      
      expect(response.status).toBe(200);
      expect(data.title).toBe(mockScrapedContent.title);
      expect(data.content).toContain(mockScrapedContent.content);
    });

    it('should handle error responses', async () => {
      const response = await fetch('https://api.scrape.do?url=https://error-test.com');
      const data = await response.json();
      
      expect(response.status).toBe(400);
      expect(data.error).toBe('Scraping failed');
    });

    it('should allow custom response overrides', async () => {
      const customResponse = { title: 'Custom Title', content: 'Custom Content' };
      mockScrapeDoResponse('https://custom-test.com', customResponse);
      
      const response = await fetch('https://api.scrape.do?url=https://custom-test.com');
      const data = await response.json();
      
      expect(data.title).toBe('Custom Title');
      expect(data.content).toBe('Custom Content');
    });
  });

  describe('Supabase API Mocking', () => {
    it('should mock Supabase GET requests', async () => {
      const response = await fetch('https://test.supabase.co/rest/v1/tools');
      const data = await response.json();
      
      expect(response.status).toBe(200);
      expect(Array.isArray(data)).toBe(true);
      expect(data[0]).toMatchObject({
        id: expect.any(Number),
        title: mockSupabaseData.title
      });
    });

    it('should mock Supabase POST requests', async () => {
      const newTool = { title: 'New Tool', description: 'New Description' };
      const response = await fetch('https://test.supabase.co/rest/v1/tools', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(newTool)
      });
      const data = await response.json();
      
      expect(response.status).toBe(201);
      expect(data.id).toBeDefined();
      expect(data.title).toBe(mockSupabaseData.title);
    });

    it('should allow custom Supabase response overrides', async () => {
      const customData = { id: 999, title: 'Custom Tool', custom: true };
      mockSupabaseResponse('tools', 'GET', [customData]);
      
      const response = await fetch('https://test.supabase.co/rest/v1/tools');
      const data = await response.json();
      
      expect(data[0].id).toBe(999);
      expect(data[0].title).toBe('Custom Tool');
      expect(data[0].custom).toBe(true);
    });
  });

  describe('Integration Workflow', () => {
    it('should support combined API mocking scenarios', async () => {
      // Mock scraping response
      const scrapedData = { title: 'Integration Test', content: 'Test content' };
      mockScrapeDoResponse('https://integration-test.com', scrapedData);
      
      // Mock database storage
      const storedData = { id: 123, ...scrapedData, stored: true };
      mockSupabaseResponse('scraped_content', 'POST', storedData, 201);
      
      // Test scraping
      const scrapeResponse = await fetch('https://api.scrape.do?url=https://integration-test.com');
      const scraped = await scrapeResponse.json();
      
      expect(scraped.title).toBe('Integration Test');
      
      // Test storage
      const storeResponse = await fetch('https://test.supabase.co/rest/v1/scraped_content', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(scraped)
      });
      const stored = await storeResponse.json();
      
      expect(stored.id).toBe(123);
      expect(stored.stored).toBe(true);
    });
  });
});
