# Editorial Reviews Cleanup Scripts

This directory contains scripts to clean up editorial review data and fix UUID parsing issues in the AI Dude Directory system.

## Problem Description

The system is experiencing UUID parsing errors like:
```
invalid input syntax for type uuid: "ai_job_7bd7b798-f0b8-4946-a96c-6aea14e0678d"
```

These errors occur because:
1. AI job IDs have malformed formats (double prefixes like "ai_job_ai_job_...")
2. Editorial reviews may reference these malformed job IDs
3. PostgreSQL cannot parse these as valid UUIDs

## Available Scripts

### 1. Comprehensive Fix (Recommended)

**File:** `fix-editorial-uuid-issues.ts`
**Usage:** `npx tsx scripts/fix-editorial-uuid-issues.ts`

This script:
- ✅ Finds and deletes AI jobs with malformed UUIDs
- ✅ Deletes all editorial reviews (to clear bad references)
- ✅ Clears tool references to deleted records
- ✅ Provides detailed logging and verification
- ✅ Handles all UUID parsing issues

### 2. Simple Editorial Reviews Cleanup

**File:** `clear-editorial-reviews.ts`
**Usage:** `npx tsx scripts/clear-editorial-reviews.ts`

This script:
- ✅ Deletes all editorial reviews
- ✅ Clears editorial_review_id references in tools
- ✅ Provides cleanup verification
- ❌ Does not fix malformed AI job UUIDs

### 3. SQL-Based Cleanup

**File:** `clear-editorial-reviews.sql`
**Usage:** Execute in Supabase SQL editor or via psql

This script:
- ✅ Pure SQL approach (no Node.js dependencies)
- ✅ Deletes editorial reviews and malformed AI jobs
- ✅ Clears all related references
- ✅ Provides step-by-step verification queries
- ✅ Includes detailed comments and documentation

### 4. Easy Runners

**Windows:** `run-editorial-cleanup.bat`
**Linux/Mac:** `run-editorial-cleanup.sh`

These scripts:
- ✅ Provide user-friendly interface
- ✅ Include confirmation prompts
- ✅ Create timestamped log files
- ✅ Handle errors gracefully

## Quick Start

### Option A: Automated (Recommended)

**Windows:**
```cmd
scripts\run-editorial-cleanup.bat
```

**Linux/Mac:**
```bash
./scripts/run-editorial-cleanup.sh
```

### Option B: Manual TypeScript

```bash
# Comprehensive fix (recommended)
npx tsx scripts/fix-editorial-uuid-issues.ts

# OR simple cleanup
npx tsx scripts/clear-editorial-reviews.ts
```

### Option C: Manual SQL

1. Open Supabase SQL editor
2. Copy and paste contents of `scripts/clear-editorial-reviews.sql`
3. Execute the script

## What Gets Deleted

⚠️ **WARNING: These operations are irreversible!**

### Data Deleted:
- All records in `editorial_reviews` table
- AI generation jobs with malformed UUIDs (patterns like "ai_job_ai_job_...")
- Tool references to deleted records

### Data Preserved:
- All tools (only references are cleared)
- Valid AI generation jobs
- All other system data

## Verification

After running any cleanup script, verify the fix by:

1. **Check the admin dashboard:** Visit `/admin` - should load without errors
2. **Check API endpoints:** `/api/editorial/review` should return 200 OK
3. **Check logs:** No more UUID parsing errors in terminal
4. **Check database:** Run verification queries from the SQL script

## Expected Results

### Before Cleanup:
```
❌ GET /api/editorial/review?action=queue&status=pending 500
❌ Error: invalid input syntax for type uuid: "ai_job_7bd7b798-f0b8-4946-a96c-6aea14e0678d"
```

### After Cleanup:
```
✅ GET /api/editorial/review?action=queue&status=pending 200
✅ Admin dashboard loads successfully
✅ No UUID parsing errors
```

## Troubleshooting

### Script Fails to Run
- Ensure you're in the project root directory
- Check that Node.js and npx are installed
- Verify database connection in `.env.local`

### Partial Cleanup
- Run the SQL script manually for guaranteed cleanup
- Check the log files for specific error details
- Verify Supabase permissions

### Still Getting Errors
- Check for additional malformed UUIDs in other tables
- Verify all foreign key references are cleared
- Restart the Next.js development server

## Recovery

If you need to restore editorial reviews:
1. The scripts don't backup data automatically
2. Use your database backup to restore if needed
3. Consider implementing proper UUID generation going forward

## Prevention

To prevent future UUID issues:
1. Ensure AI job creation uses proper UUID generation
2. Validate UUIDs before database insertion
3. Implement proper error handling for malformed IDs
4. Consider adding database constraints for UUID format validation

## Support

If you encounter issues:
1. Check the generated log files in `logs/` directory
2. Review the verification queries in the SQL script
3. Examine the detailed error messages in the TypeScript scripts
4. Consider running the SQL script manually as a fallback
