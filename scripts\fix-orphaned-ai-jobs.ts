#!/usr/bin/env tsx

import * as dotenv from 'dotenv';
import { createClient } from '@supabase/supabase-js';

// Load environment variables
dotenv.config({ path: '.env.local' });

// Create Supabase client
const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL!;
const supabaseKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!;
const supabase = createClient(supabaseUrl, supabaseKey);

async function fixOrphanedAIJobs() {
  console.log('🔧 Fixing Orphaned AI Jobs...\n');

  try {
    // 1. Find orphaned AI jobs
    console.log('1️⃣ Finding orphaned AI jobs...');
    
    const { data: aiJobs, error: aiJobsError } = await supabase
      .from('ai_generation_jobs')
      .select('id, tool_id, status, job_type, created_at');

    if (aiJobsError) {
      throw new Error(`Failed to fetch AI jobs: ${aiJobsError.message}`);
    }

    const { data: tools, error: toolsError } = await supabase
      .from('tools')
      .select('id, name');

    if (toolsError) {
      throw new Error(`Failed to fetch tools: ${toolsError.message}`);
    }

    const toolIds = new Set(tools.map(t => t.id));
    const orphanedJobs = aiJobs.filter(job => !toolIds.has(job.tool_id));

    console.log(`📊 Found ${orphanedJobs.length} orphaned AI jobs`);

    if (orphanedJobs.length === 0) {
      console.log('✅ No orphaned AI jobs found!');
      return;
    }

    // 2. Handle orphaned jobs
    console.log('\n2️⃣ Handling orphaned AI jobs...');
    
    for (const job of orphanedJobs) {
      console.log(`\n🔍 Processing orphaned job: ${job.id}`);
      console.log(`   Invalid tool_id: ${job.tool_id}`);
      
      // Option 1: Try to find a similar tool
      const similarTool = tools.find(t => 
        t.id.toLowerCase().includes(job.tool_id.toLowerCase()) ||
        t.name.toLowerCase().includes(job.tool_id.toLowerCase())
      );

      if (similarTool) {
        console.log(`   🔗 Found similar tool: ${similarTool.name} (${similarTool.id})`);
        console.log(`   ✅ Updating AI job to use correct tool_id...`);
        
        const { error: updateError } = await supabase
          .from('ai_generation_jobs')
          .update({ tool_id: similarTool.id })
          .eq('id', job.id);

        if (updateError) {
          console.log(`   ❌ Failed to update: ${updateError.message}`);
        } else {
          console.log(`   ✅ Successfully updated AI job`);
        }
      } else {
        // Option 2: Delete the orphaned job
        console.log(`   ❌ No similar tool found`);
        console.log(`   🗑️  Deleting orphaned AI job...`);
        
        const { error: deleteError } = await supabase
          .from('ai_generation_jobs')
          .delete()
          .eq('id', job.id);

        if (deleteError) {
          console.log(`   ❌ Failed to delete: ${deleteError.message}`);
        } else {
          console.log(`   ✅ Successfully deleted orphaned AI job`);
        }
      }
    }

    // 3. Verify fixes
    console.log('\n3️⃣ Verifying fixes...');
    
    const { data: remainingJobs, error: verifyError } = await supabase
      .from('ai_generation_jobs')
      .select('id, tool_id');

    if (verifyError) {
      throw new Error(`Failed to verify fixes: ${verifyError.message}`);
    }

    const remainingOrphaned = remainingJobs.filter(job => !toolIds.has(job.tool_id));
    
    if (remainingOrphaned.length === 0) {
      console.log('✅ All orphaned AI jobs have been fixed!');
    } else {
      console.log(`⚠️  ${remainingOrphaned.length} orphaned jobs still remain`);
    }

    // 4. Update AI job references in tools table
    console.log('\n4️⃣ Updating tool references...');
    
    for (const tool of tools) {
      const relatedJob = aiJobs.find(job => job.tool_id === tool.id);
      if (relatedJob) {
        const { error: updateToolError } = await supabase
          .from('tools')
          .update({ ai_generation_job_id: relatedJob.id })
          .eq('id', tool.id);

        if (updateToolError) {
          console.log(`❌ Failed to update tool ${tool.id}: ${updateToolError.message}`);
        }
      }
    }

    console.log('✅ Tool references updated');

  } catch (error) {
    console.error('❌ Script error:', error);
    throw error;
  }
}

// Run the fix script
fixOrphanedAIJobs()
  .then(() => {
    console.log('\n🎉 Orphaned AI jobs fix complete!');
    process.exit(0);
  })
  .catch((error) => {
    console.error('❌ Fix failed:', error);
    process.exit(1);
  });
