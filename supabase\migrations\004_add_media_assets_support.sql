-- Add media assets support for detailed submissions
-- Migration: 004_add_media_assets_support.sql

-- Add media assets columns to tool_submissions table
DO $$ BEGIN
  IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name='tool_submissions' AND column_name='media_assets') THEN
    ALTER TABLE tool_submissions ADD COLUMN media_assets JSONB;
    COMMENT ON COLUMN tool_submissions.media_assets IS 'User-provided media assets for detailed submissions';
  END IF;
END $$;

DO $$ BEGIN
  IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name='tool_submissions' AND column_name='use_cases') THEN
    ALTER TABLE tool_submissions ADD COLUMN use_cases TEXT;
    COMMENT ON COLUMN tool_submissions.use_cases IS 'Common use cases for the tool';
  END IF;
END $$;

DO $$ BEGIN
  IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name='tool_submissions' AND column_name='target_audience') THEN
    ALTER TABLE tool_submissions ADD COLUMN target_audience TEXT;
    COMMENT ON COLUMN tool_submissions.target_audience IS 'Target audience for the tool';
  END IF;
END $$;

DO $$ BEGIN
  IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name='tool_submissions' AND column_name='company') THEN
    ALTER TABLE tool_submissions ADD COLUMN company VARCHAR(255);
    COMMENT ON COLUMN tool_submissions.company IS 'Company or organization name';
  END IF;
END $$;

-- Add media-related columns to tools table if not exists
DO $$ BEGIN
  IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name='tools' AND column_name='media_source') THEN
    ALTER TABLE tools ADD COLUMN media_source VARCHAR(50) DEFAULT 'auto_collected';
    COMMENT ON COLUMN tools.media_source IS 'Source of media: auto_collected, user_provided, manual';
  END IF;
END $$;

DO $$ BEGIN
  IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name='tools' AND column_name='media_updated_at') THEN
    ALTER TABLE tools ADD COLUMN media_updated_at TIMESTAMP;
    COMMENT ON COLUMN tools.media_updated_at IS 'When media assets were last updated';
  END IF;
END $$;

DO $$ BEGIN
  IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name='tools' AND column_name='primary_image') THEN
    ALTER TABLE tools ADD COLUMN primary_image TEXT;
    COMMENT ON COLUMN tools.primary_image IS 'Primary image URL (screenshot or OG image)';
  END IF;
END $$;

DO $$ BEGIN
  IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name='tools' AND column_name='primary_image_type') THEN
    ALTER TABLE tools ADD COLUMN primary_image_type VARCHAR(20) CHECK (primary_image_type IN ('screenshot', 'ogImage'));
    COMMENT ON COLUMN tools.primary_image_type IS 'Type of primary image: screenshot or ogImage';
  END IF;
END $$;

DO $$ BEGIN
  IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name='tools' AND column_name='logo_url') THEN
    ALTER TABLE tools ADD COLUMN logo_url TEXT;
    COMMENT ON COLUMN tools.logo_url IS 'Direct logo URL';
  END IF;
END $$;

-- Add indexes for better performance
CREATE INDEX IF NOT EXISTS idx_tool_submissions_media_assets ON tool_submissions USING GIN (media_assets);
CREATE INDEX IF NOT EXISTS idx_tools_media_source ON tools(media_source);
CREATE INDEX IF NOT EXISTS idx_tools_media_updated_at ON tools(media_updated_at);

-- Update existing tools to have default media_source
UPDATE tools 
SET media_source = 'auto_collected' 
WHERE media_source IS NULL;
