@echo off
REM Editorial Reviews Cleanup Runner (Windows)
REM 
REM This script provides an easy way to run the editorial cleanup scripts
REM with proper error handling and logging.

setlocal enabledelayedexpansion

echo 🚀 Editorial Reviews Cleanup Runner
echo ===================================
echo.

REM Check if we're in the right directory
if not exist "package.json" (
    echo ❌ Error: Please run this script from the project root directory
    pause
    exit /b 1
)

REM Check if npx is available
npx --version >nul 2>&1
if errorlevel 1 (
    echo ❌ Error: npx is not available. Please install Node.js
    pause
    exit /b 1
)

REM Create logs directory if it doesn't exist
if not exist "logs" mkdir logs

REM Set log file with timestamp
for /f "tokens=2 delims==" %%a in ('wmic OS Get localdatetime /value') do set "dt=%%a"
set "YY=%dt:~2,2%" & set "YYYY=%dt:~0,4%" & set "MM=%dt:~4,2%" & set "DD=%dt:~6,2%"
set "HH=%dt:~8,2%" & set "Min=%dt:~10,2%" & set "Sec=%dt:~12,2%"
set "timestamp=%YYYY%%MM%%DD%-%HH%%Min%%Sec%"
set "LOG_FILE=logs\editorial-cleanup-%timestamp%.log"

echo 📝 Logging to: %LOG_FILE%
echo.

echo ⚠️  WARNING: This will delete editorial review data!
echo 📋 The following operations will be performed:
echo    • Delete all editorial reviews
echo    • Delete malformed AI jobs
echo    • Clear tool references
echo    • Verify cleanup
echo.

set /p "confirm=🤔 Do you want to continue? (y/N): "
if /i not "%confirm%"=="y" (
    echo ❌ Operation cancelled by user
    pause
    exit /b 0
)

echo.
echo 🚀 Starting cleanup process...
echo ==============================
echo.

REM Run the UUID fix script
echo 🔧 Running: UUID Issues Fix
echo 📄 Script: scripts/fix-editorial-uuid-issues.ts
echo.

npx tsx scripts/fix-editorial-uuid-issues.ts 2>&1 | tee %LOG_FILE%

if errorlevel 1 (
    echo.
    echo 💥 Cleanup failed!
    echo 📝 Check the log file for details: %LOG_FILE%
    echo.
    echo 🔧 You can also try running the SQL script manually:
    echo    scripts/clear-editorial-reviews.sql
    pause
    exit /b 1
) else (
    echo.
    echo 🎉 Editorial cleanup completed successfully!
    echo.
    echo 📊 Summary:
    echo    • All editorial reviews deleted
    echo    • Malformed AI jobs removed
    echo    • Tool references cleared
    echo    • UUID parsing issues resolved
    echo.
    echo 📝 Full log available at: %LOG_FILE%
    echo.
    echo ✅ The editorial system should now work without errors
)

echo.
echo 🏁 Script completed
pause
