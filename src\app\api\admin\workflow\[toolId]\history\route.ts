import { NextRequest, NextResponse } from 'next/server';
import { validate<PERSON><PERSON><PERSON><PERSON> } from '@/lib/auth';
import { workflowManager } from '@/lib/workflow/workflow-manager';

/**
 * GET /api/admin/workflow/[toolId]/history
 * Get workflow transition history for a tool
 */
export async function GET(
  request: NextRequest,
  { params }: { params: { toolId: string } }
) {
  try {
    // Validate admin API key
    const isValid = await validateApiKey(request);
    if (!isValid) {
      return NextResponse.json(
        { success: false, error: 'Unauthorized' },
        { status: 401 }
      );
    }

    const { toolId } = params;

    if (!toolId) {
      return NextResponse.json(
        { success: false, error: 'Tool ID is required' },
        { status: 400 }
      );
    }

    // Get workflow history
    const workflowHistory = await workflowManager.getWorkflowHistory(toolId);

    return NextResponse.json({
      success: true,
      data: workflowHistory
    });

  } catch (error: any) {
    console.error('Workflow history API error:', error);
    return NextResponse.json(
      { 
        success: false, 
        error: error.message || 'Failed to get workflow history' 
      },
      { status: 500 }
    );
  }
}
