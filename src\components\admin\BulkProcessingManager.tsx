'use client';

import React, { useState, useEffect } from 'react';
import { PartialContentGeneration } from './PartialContentGeneration';

interface BulkProcessingManagerProps {
  onClose: () => void;
  className?: string;
}

interface ToolSelection {
  id: string;
  name: string;
  website: string;
  content_status: string;
  ai_generation_status: string;
  selected: boolean;
}

interface BulkResult {
  toolId: string;
  toolName: string;
  success: boolean;
  updatedContent?: any;
  sections?: string[];
  error?: string;
}

export function BulkProcessingManager({
  onClose,
  className = ''
}: BulkProcessingManagerProps) {
  const [tools, setTools] = useState<ToolSelection[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [activeTab, setActiveTab] = useState<'selection' | 'processing' | 'results'>('selection');
  const [bulkResults, setBulkResults] = useState<BulkResult[]>([]);
  const [processing, setProcessing] = useState(false);

  useEffect(() => {
    loadTools();
  }, []);

  const loadTools = async () => {
    try {
      setLoading(true);
      setError(null);

      const response = await fetch('/api/admin/tools', {
        headers: {
          'x-admin-api-key': process.env.NEXT_PUBLIC_ADMIN_API_KEY || 'aidude_admin_2024_secure_key_xyz789'
        }
      });

      if (!response.ok) {
        throw new Error('Failed to load tools');
      }

      const data = await response.json();
      const toolsWithSelection = (data.data || []).map((tool: any) => ({
        ...tool,
        selected: false
      }));

      setTools(toolsWithSelection);

    } catch (err) {
      console.error('Error loading tools:', err);
      setError(err instanceof Error ? err.message : 'Failed to load tools');
    } finally {
      setLoading(false);
    }
  };

  const handleToolToggle = (toolId: string) => {
    setTools(prev => prev.map(tool => 
      tool.id === toolId 
        ? { ...tool, selected: !tool.selected }
        : tool
    ));
  };

  const handleSelectAll = () => {
    const hasUnselected = tools.some(t => !t.selected);
    setTools(prev => prev.map(tool => ({
      ...tool,
      selected: hasUnselected
    })));
  };

  const handleStartProcessing = () => {
    const selectedTools = tools.filter(t => t.selected);
    if (selectedTools.length === 0) {
      setError('Please select at least one tool to process');
      return;
    }

    setActiveTab('processing');
    setProcessing(true);
  };

  const handleBulkComplete = async (results: BulkResult[]) => {
    setBulkResults(results);
    setProcessing(false);
    
    // Save results to database
    await saveBulkResults(results);
    
    setActiveTab('results');
  };

  const saveBulkResults = async (results: BulkResult[]) => {
    const successfulResults = results.filter(r => r.success);
    
    for (const result of successfulResults) {
      try {
        await fetch(`/api/admin/tools/${result.toolId}`, {
          method: 'PATCH',
          headers: {
            'Content-Type': 'application/json',
            'x-admin-api-key': process.env.NEXT_PUBLIC_ADMIN_API_KEY || 'aidude_admin_2024_secure_key_xyz789'
          },
          body: JSON.stringify({
            generated_content: result.updatedContent,
            ai_generation_status: 'completed'
          })
        });
      } catch (error) {
        console.error(`Failed to save results for tool ${result.toolId}:`, error);
      }
    }
  };

  const selectedCount = tools.filter(t => t.selected).length;
  const selectedToolIds = tools.filter(t => t.selected).map(t => t.id);

  return (
    <div className={`bulk-processing-manager bg-zinc-900 border border-zinc-700 rounded-lg ${className}`}>
      {/* Header */}
      <div className="border-b border-zinc-700 p-6">
        <div className="flex items-center justify-between">
          <div>
            <h2 className="text-xl font-semibold text-white">Bulk Content Processing</h2>
            <p className="text-gray-400 text-sm mt-1">
              Process multiple tools with partial content generation
            </p>
          </div>
          <button
            onClick={onClose}
            className="text-gray-400 hover:text-white"
          >
            <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
            </svg>
          </button>
        </div>
      </div>

      {/* Tabs */}
      <div className="border-b border-zinc-700">
        <nav className="flex space-x-8 px-6">
          {[
            { key: 'selection', label: 'Tool Selection', disabled: false },
            { key: 'processing', label: 'Processing', disabled: selectedCount === 0 },
            { key: 'results', label: 'Results', disabled: bulkResults.length === 0 }
          ].map((tab) => (
            <button
              key={tab.key}
              onClick={() => !tab.disabled && setActiveTab(tab.key as any)}
              disabled={tab.disabled}
              className={`py-3 px-1 border-b-2 font-medium text-sm ${
                activeTab === tab.key
                  ? 'border-orange-500 text-orange-400'
                  : tab.disabled
                  ? 'border-transparent text-gray-600 cursor-not-allowed'
                  : 'border-transparent text-gray-400 hover:text-gray-300'
              }`}
            >
              {tab.label}
              {tab.key === 'selection' && selectedCount > 0 && (
                <span className="ml-2 px-2 py-1 bg-orange-600 text-white text-xs rounded">
                  {selectedCount}
                </span>
              )}
            </button>
          ))}
        </nav>
      </div>

      {/* Tab Content */}
      <div className="p-6">
        {activeTab === 'selection' && (
          <div className="space-y-4">
            <div className="flex items-center justify-between">
              <h3 className="text-lg font-medium text-white">Select Tools to Process</h3>
              <div className="flex items-center space-x-3">
                <button
                  onClick={handleSelectAll}
                  className="px-3 py-1 bg-zinc-600 hover:bg-zinc-500 text-white rounded text-sm"
                >
                  {tools.every(t => t.selected) ? 'Deselect All' : 'Select All'}
                </button>
                <button
                  onClick={handleStartProcessing}
                  disabled={selectedCount === 0}
                  className="px-4 py-2 bg-orange-600 hover:bg-orange-700 disabled:bg-orange-800 disabled:opacity-50 text-white rounded-md transition-colors"
                >
                  Process {selectedCount} Tools
                </button>
              </div>
            </div>

            {loading ? (
              <div className="flex items-center justify-center py-8">
                <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-orange-500"></div>
              </div>
            ) : (
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 max-h-96 overflow-y-auto">
                {tools.map((tool) => (
                  <div
                    key={tool.id}
                    className={`border rounded-lg p-4 cursor-pointer transition-colors ${
                      tool.selected 
                        ? 'border-orange-500 bg-orange-500/10' 
                        : 'border-zinc-600 hover:border-zinc-500'
                    }`}
                    onClick={() => handleToolToggle(tool.id)}
                  >
                    <div className="flex items-start justify-between">
                      <div className="flex-1">
                        <div className="flex items-center space-x-2">
                          <input
                            type="checkbox"
                            checked={tool.selected}
                            onChange={() => handleToolToggle(tool.id)}
                            className="rounded border-zinc-600 text-orange-500 focus:ring-orange-500"
                          />
                          <h4 className="font-medium text-white truncate">{tool.name}</h4>
                        </div>
                        <p className="text-gray-400 text-sm mt-1 truncate">{tool.website}</p>
                        <div className="flex items-center space-x-2 mt-2">
                          <span className={`px-2 py-1 rounded text-xs ${
                            tool.content_status === 'published' ? 'bg-green-600 text-white' :
                            tool.content_status === 'approved' ? 'bg-blue-600 text-white' :
                            'bg-gray-600 text-white'
                          }`}>
                            {tool.content_status}
                          </span>
                          <span className={`px-2 py-1 rounded text-xs ${
                            tool.ai_generation_status === 'completed' ? 'bg-green-600 text-white' :
                            tool.ai_generation_status === 'processing' ? 'bg-orange-600 text-white' :
                            'bg-gray-600 text-white'
                          }`}>
                            AI: {tool.ai_generation_status}
                          </span>
                        </div>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            )}
          </div>
        )}

        {activeTab === 'processing' && (
          <div className="space-y-6">
            <h3 className="text-lg font-medium text-white">
              Processing {selectedCount} Tools
            </h3>
            
            <PartialContentGeneration
              toolId={selectedToolIds[0] || ''}
              toolName="Bulk Processing"
              existingContent={{}}
              onGenerationComplete={() => {}}
              onError={(error) => setError(error)}
              bulkMode={true}
              selectedTools={selectedToolIds}
              onBulkComplete={handleBulkComplete}
            />
          </div>
        )}

        {activeTab === 'results' && (
          <div className="space-y-4">
            <h3 className="text-lg font-medium text-white">Processing Results</h3>
            
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
              <div className="bg-green-900/20 border border-green-500 rounded-lg p-4">
                <h4 className="font-medium text-green-400">Successful</h4>
                <p className="text-2xl font-bold text-white">
                  {bulkResults.filter(r => r.success).length}
                </p>
              </div>
              <div className="bg-red-900/20 border border-red-500 rounded-lg p-4">
                <h4 className="font-medium text-red-400">Failed</h4>
                <p className="text-2xl font-bold text-white">
                  {bulkResults.filter(r => !r.success).length}
                </p>
              </div>
              <div className="bg-blue-900/20 border border-blue-500 rounded-lg p-4">
                <h4 className="font-medium text-blue-400">Total</h4>
                <p className="text-2xl font-bold text-white">
                  {bulkResults.length}
                </p>
              </div>
            </div>

            <div className="space-y-2 max-h-60 overflow-y-auto">
              {bulkResults.map((result, index) => (
                <div
                  key={index}
                  className={`border rounded-lg p-3 ${
                    result.success 
                      ? 'border-green-600 bg-green-900/10' 
                      : 'border-red-600 bg-red-900/10'
                  }`}
                >
                  <div className="flex items-center justify-between">
                    <div>
                      <h5 className="font-medium text-white">{result.toolName}</h5>
                      {result.success ? (
                        <p className="text-green-400 text-sm">
                          Updated {result.sections?.length || 0} sections
                        </p>
                      ) : (
                        <p className="text-red-400 text-sm">{result.error}</p>
                      )}
                    </div>
                    <span className={`px-2 py-1 rounded text-xs ${
                      result.success ? 'bg-green-600 text-white' : 'bg-red-600 text-white'
                    }`}>
                      {result.success ? 'Success' : 'Failed'}
                    </span>
                  </div>
                </div>
              ))}
            </div>
          </div>
        )}

        {/* Error Display */}
        {error && (
          <div className="bg-red-900/20 border border-red-500 rounded-lg p-3 mt-4">
            <div className="flex items-center space-x-2">
              <svg className="w-5 h-5 text-red-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
              </svg>
              <span className="text-red-400 text-sm">{error}</span>
            </div>
          </div>
        )}
      </div>
    </div>
  );
}
