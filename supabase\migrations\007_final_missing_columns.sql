-- Final missing columns for dual submission system
-- Migration: 007_final_missing_columns.sql

-- Add missing columns to tool_submissions table
DO $$ BEGIN
  IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name='tool_submissions' AND column_name='meta_keywords') THEN
    ALTER TABLE tool_submissions ADD COLUMN meta_keywords TEXT;
    COMMENT ON COLUMN tool_submissions.meta_keywords IS 'SEO keywords for search optimization';
  END IF;
END $$;

DO $$ BEGIN
  IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name='tool_submissions' AND column_name='integrations') THEN
    ALTER TABLE tool_submissions ADD COLUMN integrations TEXT;
    COMMENT ON COLUMN tool_submissions.integrations IS 'Third-party integrations and compatibility';
  END IF;
END $$;

DO $$ BEGIN
  IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name='tool_submissions' AND column_name='reviewed_by') THEN
    ALTER TABLE tool_submissions ADD COLUMN reviewed_by VARC<PERSON>R(255);
    COMMENT ON COLUMN tool_submissions.reviewed_by IS 'Admin who reviewed the submission';
  END IF;
END $$;

-- Add indexes for the new columns
CREATE INDEX IF NOT EXISTS idx_tool_submissions_meta_keywords ON tool_submissions(meta_keywords);
CREATE INDEX IF NOT EXISTS idx_tool_submissions_integrations ON tool_submissions USING GIN (to_tsvector('english', integrations));
CREATE INDEX IF NOT EXISTS idx_tool_submissions_reviewed_by ON tool_submissions(reviewed_by);
