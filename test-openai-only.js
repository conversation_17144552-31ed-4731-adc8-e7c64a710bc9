const http = require('http');

function testAPI(path, method = 'GET', data = null) {
  return new Promise((resolve, reject) => {
    const options = {
      hostname: 'localhost',
      port: 3000,
      path: path,
      method: method,
      headers: {
        'Content-Type': 'application/json',
        'x-api-key': 'aidude_admin_2024_secure_key_xyz789'
      }
    };

    const req = http.request(options, (res) => {
      let responseData = '';
      res.on('data', (chunk) => responseData += chunk);
      res.on('end', () => {
        try {
          const parsed = JSON.parse(responseData);
          resolve({ status: res.statusCode, data: parsed });
        } catch (e) {
          resolve({ status: res.statusCode, data: responseData });
        }
      });
    });

    req.on('error', (error) => {
      reject(error);
    });

    if (data) {
      req.write(JSON.stringify(data));
    }
    req.end();
  });
}

async function testOpenAIOnly() {
  console.log('🧪 Testing AI Dude System with OpenAI Priority...\n');

  try {
    // Test with cost priority to force OpenAI usage
    console.log('Testing AI Dude content generation with cost priority (OpenAI)...');
    const testData = {
      url: 'https://openai.com',
      methodology: 'ai_dude',
      scrapedData: {
        title: 'OpenAI',
        description: 'AI research company',
        content: 'OpenAI creates AI models like GPT-4. It offers API access to developers.'
      },
      options: {
        priority: 'cost',        // This should force OpenAI
        complexity: 'low',       // Keep it simple
        contentQuality: 80       // Good quality
      }
    };

    const generateResult = await testAPI('/api/generate-content', 'POST', testData);
    console.log(`Status: ${generateResult.status}`);
    
    if (generateResult.status === 200) {
      console.log(`✅ Generation Success: ${generateResult.data.success}`);
      if (generateResult.data.data?.aiContent) {
        const content = generateResult.data.data.aiContent;
        const metadata = generateResult.data.data.generationMetadata;
        
        console.log(`Generated fields: ${Object.keys(content).length}`);
        console.log(`Methodology: ${metadata?.methodology}`);
        console.log(`Provider: ${metadata?.provider}`);
        console.log(`Model: ${metadata?.model}`);
        console.log(`Tokens used: ${metadata?.tokensUsed || 'N/A'}`);
        
        // Check for AI Dude tone in key fields
        console.log('\n--- Content Sample ---');
        if (content.name) console.log(`Name: ${content.name}`);
        if (content.description) console.log(`Description: ${content.description}`);
        if (content.features && content.features.length > 0) {
          console.log(`Features (${content.features.length}): ${content.features[0]}`);
        }
        
        // Validate AI Dude characteristics
        const hasAIDudeTone = checkAIDudeTone(content);
        console.log(`\n✅ AI Dude Tone Detected: ${hasAIDudeTone ? 'YES' : 'NO'}`);
        
      }
    } else {
      console.log(`❌ Error: ${JSON.stringify(generateResult.data)}`);
    }

    // Test partial generation
    console.log('\n--- Testing Partial Generation ---');
    const partialData = {
      url: 'https://openai.com',
      methodology: 'ai_dude',
      sectionType: 'features',
      existingToolData: {
        name: 'OpenAI',
        description: 'AI research company'
      },
      scrapedData: {
        title: 'OpenAI',
        content: 'OpenAI provides GPT models, DALL-E for images, and Whisper for speech.'
      },
      options: {
        priority: 'cost',
        complexity: 'low'
      }
    };

    const partialResult = await testAPI('/api/generate-content', 'POST', partialData);
    console.log(`Partial Status: ${partialResult.status}`);
    
    if (partialResult.status === 200) {
      console.log(`✅ Partial Success: ${partialResult.data.success}`);
      console.log(`Section: ${partialResult.data.data?.generationMetadata?.sectionType}`);
    } else {
      console.log(`❌ Partial Error: ${JSON.stringify(partialResult.data)}`);
    }

  } catch (error) {
    console.error('Test failed:', error.message);
  }
}

function checkAIDudeTone(content) {
  const toneIndicators = [
    'no-bs', 'snarky', 'witty', 'dude', 'bs', 'crap', 'damn', 'hell',
    'ain\'t', 'gonna', 'wanna', 'gotta', 'kinda', 'sorta',
    '!', 'seriously', 'honestly', 'frankly', 'basically'
  ];
  
  const textFields = [
    content.description,
    content.detailed_description,
    content.meta_description,
    ...(content.features || []),
    ...(content.pros_and_cons?.pros || []),
    ...(content.pros_and_cons?.cons || [])
  ].filter(Boolean);
  
  const allText = textFields.join(' ').toLowerCase();
  
  return toneIndicators.some(indicator => allText.includes(indicator.toLowerCase()));
}

testOpenAIOnly();
