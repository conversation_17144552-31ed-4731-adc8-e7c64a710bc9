'use client';

import { useState } from 'react';

interface EnhancedFilters {
  search: string;
  contentStatus: string;
  submissionType: string;
  submittedBy: string;
  dateRange: string;
  hasMedia: string;
}

interface EnhancedToolsFiltersProps {
  filters: EnhancedFilters;
  onFilterChange: (filters: Partial<EnhancedFilters>) => void;
  loading?: boolean;
}

export function EnhancedToolsFilters({ filters, onFilterChange, loading }: EnhancedToolsFiltersProps) {
  const [showAdvanced, setShowAdvanced] = useState(false);

  const handleFilterChange = (key: keyof EnhancedFilters, value: string) => {
    onFilterChange({ [key]: value });
  };

  const clearFilters = () => {
    onFilterChange({
      search: '',
      contentStatus: '',
      submissionType: '',
      submittedBy: '',
      dateRange: '',
      hasMedia: ''
    });
  };

  const hasActiveFilters = Object.values(filters).some(value => value !== '');

  return (
    <div className="bg-zinc-800 border border-zinc-700 rounded-lg p-4 space-y-4">
      {/* Basic Filters Row */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        {/* Search */}
        <div>
          <label className="block text-sm font-medium text-gray-300 mb-1">
            Search Tools
          </label>
          <input
            type="text"
            value={filters.search}
            onChange={(e) => handleFilterChange('search', e.target.value)}
            placeholder="Search by name, description..."
            className="w-full px-3 py-2 bg-zinc-700 border border-zinc-600 rounded-md text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-orange-500"
            disabled={loading}
          />
        </div>

        {/* Content Status */}
        <div>
          <label className="block text-sm font-medium text-gray-300 mb-1">
            Status
          </label>
          <select
            value={filters.contentStatus}
            onChange={(e) => handleFilterChange('contentStatus', e.target.value)}
            className="w-full px-3 py-2 bg-zinc-700 border border-zinc-600 rounded-md text-white focus:outline-none focus:ring-2 focus:ring-orange-500"
            disabled={loading}
          >
            <option value="">All Statuses</option>
            <option value="published">Published</option>
            <option value="draft">Draft</option>
            <option value="archived">Archived</option>
            <option value="under_review">Under Review</option>
          </select>
        </div>

        {/* Submission Type */}
        <div>
          <label className="block text-sm font-medium text-gray-300 mb-1">
            Submission Type
          </label>
          <select
            value={filters.submissionType}
            onChange={(e) => handleFilterChange('submissionType', e.target.value)}
            className="w-full px-3 py-2 bg-zinc-700 border border-zinc-600 rounded-md text-white focus:outline-none focus:ring-2 focus:ring-orange-500"
            disabled={loading}
          >
            <option value="">All Types</option>
            <option value="simple">Simple (AI Generated)</option>
            <option value="detailed">Detailed (User Provided)</option>
            <option value="admin">Admin Created</option>
          </select>
        </div>

        {/* Actions */}
        <div className="flex items-end gap-2">
          <button
            onClick={() => setShowAdvanced(!showAdvanced)}
            className="px-4 py-2 bg-zinc-600 hover:bg-zinc-500 text-white rounded-md transition-colors"
            disabled={loading}
          >
            {showAdvanced ? 'Hide' : 'Advanced'}
          </button>
          
          {hasActiveFilters && (
            <button
              onClick={clearFilters}
              className="px-4 py-2 bg-red-600 hover:bg-red-500 text-white rounded-md transition-colors"
              disabled={loading}
            >
              Clear
            </button>
          )}
        </div>
      </div>

      {/* Advanced Filters */}
      {showAdvanced && (
        <div className="border-t border-zinc-700 pt-4">
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            {/* Submitted By */}
            <div>
              <label className="block text-sm font-medium text-gray-300 mb-1">
                Submitted By
              </label>
              <input
                type="text"
                value={filters.submittedBy}
                onChange={(e) => handleFilterChange('submittedBy', e.target.value)}
                placeholder="Email or name..."
                className="w-full px-3 py-2 bg-zinc-700 border border-zinc-600 rounded-md text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-orange-500"
                disabled={loading}
              />
            </div>

            {/* Date Range */}
            <div>
              <label className="block text-sm font-medium text-gray-300 mb-1">
                Date Range
              </label>
              <select
                value={filters.dateRange}
                onChange={(e) => handleFilterChange('dateRange', e.target.value)}
                className="w-full px-3 py-2 bg-zinc-700 border border-zinc-600 rounded-md text-white focus:outline-none focus:ring-2 focus:ring-orange-500"
                disabled={loading}
              >
                <option value="">All Time</option>
                <option value="today">Today</option>
                <option value="week">This Week</option>
                <option value="month">This Month</option>
                <option value="quarter">This Quarter</option>
                <option value="year">This Year</option>
              </select>
            </div>

            {/* Has Media */}
            <div>
              <label className="block text-sm font-medium text-gray-300 mb-1">
                Media Assets
              </label>
              <select
                value={filters.hasMedia}
                onChange={(e) => handleFilterChange('hasMedia', e.target.value)}
                className="w-full px-3 py-2 bg-zinc-700 border border-zinc-600 rounded-md text-white focus:outline-none focus:ring-2 focus:ring-orange-500"
                disabled={loading}
              >
                <option value="">All Tools</option>
                <option value="with_media">With Media</option>
                <option value="without_media">Without Media</option>
                <option value="user_provided">User Provided Media</option>
                <option value="auto_collected">Auto Collected Media</option>
              </select>
            </div>
          </div>

          {/* Filter Summary */}
          {hasActiveFilters && (
            <div className="mt-4 p-3 bg-zinc-700/50 rounded-md">
              <div className="text-sm text-gray-300 mb-2">Active Filters:</div>
              <div className="flex flex-wrap gap-2">
                {Object.entries(filters).map(([key, value]) => {
                  if (!value) return null;
                  
                  const labels: Record<string, string> = {
                    search: 'Search',
                    contentStatus: 'Status',
                    submissionType: 'Type',
                    submittedBy: 'Submitter',
                    dateRange: 'Date',
                    hasMedia: 'Media'
                  };

                  return (
                    <span
                      key={key}
                      className="inline-flex items-center gap-1 px-2 py-1 bg-orange-600 text-white text-xs rounded"
                    >
                      {labels[key]}: {value}
                      <button
                        onClick={() => handleFilterChange(key as keyof EnhancedFilters, '')}
                        className="ml-1 hover:text-orange-200"
                      >
                        ×
                      </button>
                    </span>
                  );
                })}
              </div>
            </div>
          )}
        </div>
      )}

      {/* Filter Stats */}
      <div className="flex justify-between items-center text-sm text-gray-400 border-t border-zinc-700 pt-3">
        <div>
          {hasActiveFilters ? 'Filtered results' : 'Showing all tools'}
        </div>
        <div className="flex items-center gap-4">
          <span>📊 Enhanced filtering for dual submission system</span>
          {loading && (
            <div className="flex items-center gap-2">
              <div className="animate-spin w-4 h-4 border-2 border-orange-500 border-t-transparent rounded-full"></div>
              <span>Loading...</span>
            </div>
          )}
        </div>
      </div>
    </div>
  );
}
