/**
 * Simplified Version Manager Tests using consistent Supabase mocking
 * @jest-environment node
 */

import { VersionManager } from '@/lib/versioning/version-manager';
import { CreateVersionRequest, RollbackRequest } from '@/lib/types/versioning';

// Create a comprehensive mock for Supabase client
const createMockQueryBuilder = () => {
  const mockBuilder = {
    select: jest.fn().mockReturnThis(),
    insert: jest.fn().mockReturnThis(),
    update: jest.fn().mockReturnThis(),
    delete: jest.fn().mockReturnThis(),
    eq: jest.fn().mockReturnThis(),
    order: jest.fn().mockReturnThis(),
    limit: jest.fn().mockReturnThis(),
    range: jest.fn().mockReturnThis(),
    single: jest.fn().mockResolvedValue({ data: null, error: null }),
    then: jest.fn().mockResolvedValue({ data: null, error: null })
  };

  // Make all chainable methods return the builder
  Object.keys(mockBuilder).forEach(key => {
    if (typeof mockBuilder[key] === 'function' && !['single', 'then'].includes(key)) {
      mockBuilder[key].mockReturnValue(mockBuilder);
    }
  });

  return mockBuilder;
};

jest.mock('@/lib/supabase', () => ({
  supabaseAdmin: {
    from: jest.fn(() => createMockQueryBuilder()),
    rpc: jest.fn().mockResolvedValue({ data: null, error: null })
  }
}));

// Mock audit logger and comparator
jest.mock('@/lib/versioning/audit-logger', () => ({
  VersionAuditLogger: jest.fn().mockImplementation(() => ({
    logAction: jest.fn().mockResolvedValue('audit-log-id')
  }))
}));

jest.mock('@/lib/versioning/version-comparator', () => ({
  VersionComparator: jest.fn().mockImplementation(() => ({
    compareVersions: jest.fn()
  }))
}));

describe('VersionManager (Simplified)', () => {
  let versionManager: VersionManager;
  let mockQueryBuilder: any;
  let mockSupabaseAdmin: any;

  beforeEach(() => {
    jest.clearAllMocks();
    versionManager = new VersionManager();
    mockQueryBuilder = createMockQueryBuilder();
    mockSupabaseAdmin = require('@/lib/supabase').supabaseAdmin;
    mockSupabaseAdmin.from.mockReturnValue(mockQueryBuilder);
  });

  describe('createVersion', () => {
    it('should create a new version successfully', async () => {
      const mockTool = {
        id: 'test-tool-id',
        name: 'Test Tool',
        slug: 'test-tool',
        link: '/tools/test-tool'
      };

      const mockVersion = {
        id: 'version-id',
        tool_id: 'test-tool-id',
        version_number: 2,
        version_data: mockTool,
        change_summary: 'Test update',
        created_by: 'admin',
        created_at: '2024-01-01T00:00:00Z',
        is_current: true,
        change_type: 'update',
        change_source: 'admin_panel',
        parent_version_id: 'parent-version-id',
        rollback_reason: null
      };

      // Setup mock responses in sequence
      mockQueryBuilder.single
        .mockResolvedValueOnce({ data: mockTool, error: null }) // Tool fetch
        .mockResolvedValueOnce({ data: { id: 'parent-version-id' }, error: null }) // Current version
        .mockResolvedValueOnce({ data: mockVersion, error: null }); // Version creation

      mockQueryBuilder.then
        .mockResolvedValueOnce({ data: null, error: null }); // Tool update

      const request: CreateVersionRequest = {
        toolId: 'test-tool-id',
        changeSummary: 'Test update',
        changeType: 'update',
        changeSource: 'admin_panel',
        createdBy: 'admin'
      };

      const result = await versionManager.createVersion(request);

      expect(result.success).toBe(true);
      expect(result.data).toBeDefined();
      expect(result.versionId).toBe('version-id');
      expect(result.performedBy).toBe('admin');
    });

    it('should handle tool not found error', async () => {
      // Mock tool not found
      mockQueryBuilder.single.mockResolvedValueOnce({
        data: null,
        error: { message: 'Tool not found' }
      });

      const request: CreateVersionRequest = {
        toolId: 'non-existent-tool',
        changeSummary: 'Test update',
        createdBy: 'admin'
      };

      const result = await versionManager.createVersion(request);

      expect(result.success).toBe(false);
      expect(result.error).toContain('Tool not found');
    });

    it('should handle version creation failure', async () => {
      const mockTool = {
        id: 'test-tool-id',
        name: 'Test Tool'
      };

      // Setup mock responses
      mockQueryBuilder.single
        .mockResolvedValueOnce({ data: mockTool, error: null }) // Tool fetch
        .mockResolvedValueOnce({ data: null, error: null }) // Current version
        .mockResolvedValueOnce({ data: null, error: { message: 'Insert failed' } }); // Version creation failure

      const request: CreateVersionRequest = {
        toolId: 'test-tool-id',
        changeSummary: 'Test update',
        createdBy: 'admin'
      };

      const result = await versionManager.createVersion(request);

      expect(result.success).toBe(false);
      expect(result.error).toContain('Failed to create version');
    });
  });

  describe('validateRollback', () => {
    it('should validate rollback successfully', async () => {
      const mockTargetVersion = {
        id: 'target-version-id',
        tool_id: 'test-tool-id',
        version_number: 1
      };

      const mockTool = {
        id: 'test-tool-id',
        name: 'Test Tool'
      };

      const mockCurrentVersion = {
        version_number: 2
      };

      // Setup mock responses
      mockQueryBuilder.single
        .mockResolvedValueOnce({ data: mockTargetVersion, error: null }) // Target version
        .mockResolvedValueOnce({ data: mockTool, error: null }) // Tool fetch
        .mockResolvedValueOnce({ data: mockCurrentVersion, error: null }); // Current version

      const request: RollbackRequest = {
        toolId: 'test-tool-id',
        targetVersionNumber: 1,
        reason: 'Test rollback',
        performedBy: 'admin'
      };

      const validation = await versionManager.validateRollback(request);

      expect(validation.isValid).toBe(true);
      expect(validation.canRollback).toBe(true);
      expect(validation.errors).toHaveLength(0);
    });

    it('should fail validation for non-existent target version', async () => {
      // Mock target version not found
      mockQueryBuilder.single.mockResolvedValueOnce({
        data: null,
        error: { message: 'Version not found' }
      });

      const request: RollbackRequest = {
        toolId: 'test-tool-id',
        targetVersionNumber: 999,
        reason: 'Test rollback',
        performedBy: 'admin'
      };

      const validation = await versionManager.validateRollback(request);

      expect(validation.isValid).toBe(false);
      expect(validation.canRollback).toBe(false);
      expect(validation.errors).toContain('Target version 999 not found');
    });
  });

  describe('getVersionHistory', () => {
    it('should fetch version history with pagination', async () => {
      const mockVersions = [
        {
          id: 'version-1',
          tool_id: 'test-tool-id',
          version_number: 2,
          created_at: '2024-01-02T00:00:00Z'
        },
        {
          id: 'version-2',
          tool_id: 'test-tool-id',
          version_number: 1,
          created_at: '2024-01-01T00:00:00Z'
        }
      ];

      // Mock version history fetch
      mockQueryBuilder.then.mockResolvedValueOnce({
        data: mockVersions,
        error: null,
        count: 2
      });

      const result = await versionManager.getVersionHistory({
        toolId: 'test-tool-id',
        page: 1,
        limit: 10
      });

      expect(result.success).toBe(true);
      expect(result.data).toBeDefined();
      expect(result.data.versions).toHaveLength(2);
      expect(result.data.totalCount).toBe(2);
    });
  });
});
