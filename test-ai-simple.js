const http = require('http');

function testAPI(path, method = 'GET', data = null) {
  return new Promise((resolve, reject) => {
    const options = {
      hostname: 'localhost',
      port: 3000,
      path: path,
      method: method,
      headers: {
        'Content-Type': 'application/json',
        'x-api-key': 'aidude_admin_2024_secure_key_xyz789'
      }
    };

    const req = http.request(options, (res) => {
      let responseData = '';
      res.on('data', (chunk) => responseData += chunk);
      res.on('end', () => {
        try {
          const parsed = JSON.parse(responseData);
          resolve({ status: res.statusCode, data: parsed });
        } catch (e) {
          resolve({ status: res.statusCode, data: responseData });
        }
      });
    });

    req.on('error', (error) => {
      reject(error);
    });

    if (data) {
      req.write(JSON.stringify(data));
    }
    req.end();
  });
}

async function testSimpleGeneration() {
  console.log('🧪 Testing AI Dude System with Reduced Scope...\n');

  try {
    // Test with OpenAI only and reduced tokens
    console.log('Testing AI Dude content generation with reduced tokens...');
    const testData = {
      url: 'https://openai.com',
      methodology: 'ai_dude',
      scrapedData: {
        title: 'OpenAI',
        description: 'AI research company',
        content: 'OpenAI creates AI models like GPT-4.'
      },
      options: {
        priority: 'quality',
        complexity: 'low',
        maxTokens: 2000  // Much smaller request
      }
    };

    const generateResult = await testAPI('/api/generate-content', 'POST', testData);
    console.log(`Status: ${generateResult.status}`);
    
    if (generateResult.status === 200) {
      console.log(`✅ Generation Success: ${generateResult.data.success}`);
      if (generateResult.data.data?.aiContent) {
        const content = generateResult.data.data.aiContent;
        console.log(`Generated fields: ${Object.keys(content).length}`);
        console.log(`Methodology: ${generateResult.data.data.generationMetadata?.methodology}`);
        console.log(`Provider: ${generateResult.data.data.generationMetadata?.provider}`);
        console.log(`Model: ${generateResult.data.data.generationMetadata?.model}`);
        
        // Check for AI Dude tone
        if (content.description) {
          console.log(`Description: ${content.description.substring(0, 100)}...`);
        }
      }
    } else {
      console.log(`❌ Error: ${JSON.stringify(generateResult.data)}`);
    }

    // Test standard methodology for comparison
    console.log('\n--- Testing Standard Methodology for Comparison ---');
    const standardData = {
      ...testData,
      methodology: 'standard'
    };

    const standardResult = await testAPI('/api/generate-content', 'POST', standardData);
    console.log(`Standard Status: ${standardResult.status}`);
    
    if (standardResult.status === 200) {
      console.log(`✅ Standard Success: ${standardResult.data.success}`);
      console.log(`Provider: ${standardResult.data.data?.generationMetadata?.provider}`);
    } else {
      console.log(`❌ Standard Error: ${JSON.stringify(standardResult.data)}`);
    }

  } catch (error) {
    console.error('Test failed:', error.message);
  }
}

testSimpleGeneration();
