#!/usr/bin/env tsx

import * as dotenv from 'dotenv';

// Load environment variables
dotenv.config({ path: '.env.local' });

async function testAdminToolsInterface() {
  console.log('🧪 Testing Enhanced Admin Tools Management Interface...\n');

  const baseUrl = 'http://localhost:3000';
  const apiKey = process.env.NEXT_PUBLIC_ADMIN_API_KEY || 'aidude_admin_2024_secure_key_xyz789';

  const testResults = {
    apiConnection: false,
    enhancedFiltering: false,
    toolUpdate: false,
    bulkOperations: false,
    mediaDisplay: false,
    submissionTracking: false
  };

  try {
    // 1. Test API Connection
    console.log('1️⃣ Testing Admin Tools API Connection:');
    
    const response = await fetch(`${baseUrl}/api/admin/tools?limit=5`, {
      headers: {
        'x-admin-api-key': apiKey
      }
    });

    if (response.ok) {
      const data = await response.json();
      if (data.success) {
        console.log(`   ✅ API connection successful`);
        console.log(`   📊 Found ${data.data.tools?.length || 0} tools`);
        testResults.apiConnection = true;
      } else {
        console.log(`   ❌ API returned error: ${data.error}`);
      }
    } else {
      console.log(`   ❌ API connection failed: ${response.status}`);
    }

    // 2. Test Enhanced Filtering
    console.log('\n2️⃣ Testing Enhanced Filtering:');
    
    const filterTests = [
      { filter: 'submissionType=detailed', name: 'Detailed submissions' },
      { filter: 'submissionType=simple', name: 'Simple submissions' },
      { filter: 'contentStatus=published', name: 'Published tools' },
      { filter: 'hasMedia=with_media', name: 'Tools with media' },
      { filter: 'dateRange=month', name: 'This month' }
    ];

    let filterTestsPassed = 0;
    for (const test of filterTests) {
      try {
        const filterResponse = await fetch(`${baseUrl}/api/admin/tools?${test.filter}&limit=3`, {
          headers: {
            'x-admin-api-key': apiKey
          }
        });

        if (filterResponse.ok) {
          const filterData = await filterResponse.json();
          if (filterData.success) {
            console.log(`   ✅ ${test.name}: ${filterData.data.tools?.length || 0} results`);
            filterTestsPassed++;
          } else {
            console.log(`   ❌ ${test.name}: ${filterData.error}`);
          }
        } else {
          console.log(`   ❌ ${test.name}: HTTP ${filterResponse.status}`);
        }
      } catch (error) {
        console.log(`   ❌ ${test.name}: ${error}`);
      }
    }

    if (filterTestsPassed >= 3) {
      testResults.enhancedFiltering = true;
      console.log(`   🎯 Enhanced filtering working (${filterTestsPassed}/${filterTests.length} tests passed)`);
    }

    // 3. Test Tool Update (if tools exist)
    console.log('\n3️⃣ Testing Tool Update Functionality:');
    
    const toolsResponse = await fetch(`${baseUrl}/api/admin/tools?limit=1`, {
      headers: {
        'x-admin-api-key': apiKey
      }
    });

    if (toolsResponse.ok) {
      const toolsData = await toolsResponse.json();
      if (toolsData.success && toolsData.data.tools?.length > 0) {
        const testTool = toolsData.data.tools[0];
        
        try {
          const updateResponse = await fetch(`${baseUrl}/api/admin/tools/${testTool.id}`, {
            method: 'PATCH',
            headers: {
              'Content-Type': 'application/json',
              'x-admin-api-key': apiKey
            },
            body: JSON.stringify({
              editorial_notes: `Test update at ${new Date().toISOString()}`
            })
          });

          if (updateResponse.ok) {
            const updateData = await updateResponse.json();
            if (updateData.success) {
              console.log(`   ✅ Tool update successful: ${testTool.name}`);
              testResults.toolUpdate = true;
            } else {
              console.log(`   ❌ Tool update failed: ${updateData.error}`);
            }
          } else {
            console.log(`   ❌ Tool update HTTP error: ${updateResponse.status}`);
          }
        } catch (error) {
          console.log(`   ❌ Tool update error: ${error}`);
        }
      } else {
        console.log(`   ⚠️  No tools available for update testing`);
        testResults.toolUpdate = true; // Skip this test
      }
    }

    // 4. Test Bulk Operations
    console.log('\n4️⃣ Testing Bulk Operations:');
    
    try {
      const bulkResponse = await fetch(`${baseUrl}/api/admin/tools/bulk`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'x-admin-api-key': apiKey
        },
        body: JSON.stringify({
          action: 'verify',
          toolIds: [] // Empty array for testing
        })
      });

      if (bulkResponse.ok) {
        const bulkData = await bulkResponse.json();
        // Should return success even with empty array
        console.log(`   ✅ Bulk operations API accessible`);
        testResults.bulkOperations = true;
      } else {
        console.log(`   ❌ Bulk operations failed: ${bulkResponse.status}`);
      }
    } catch (error) {
      console.log(`   ❌ Bulk operations error: ${error}`);
    }

    // 5. Test Media Display Support
    console.log('\n5️⃣ Testing Media Display Support:');
    
    const mediaResponse = await fetch(`${baseUrl}/api/admin/tools?hasMedia=with_media&limit=3`, {
      headers: {
        'x-admin-api-key': apiKey
      }
    });

    if (mediaResponse.ok) {
      const mediaData = await mediaResponse.json();
      if (mediaData.success) {
        const toolsWithMedia = mediaData.data.tools?.filter((tool: any) => 
          tool.primary_image || tool.logo_url
        ) || [];
        
        console.log(`   ✅ Media filtering works: ${toolsWithMedia.length} tools with media`);
        
        if (toolsWithMedia.length > 0) {
          const sampleTool = toolsWithMedia[0];
          console.log(`   📸 Sample media fields:`);
          console.log(`      Primary Image: ${sampleTool.primary_image ? 'Present' : 'Missing'}`);
          console.log(`      Logo URL: ${sampleTool.logo_url ? 'Present' : 'Missing'}`);
          console.log(`      Media Source: ${sampleTool.media_source || 'Not set'}`);
        }
        
        testResults.mediaDisplay = true;
      } else {
        console.log(`   ❌ Media display test failed: ${mediaData.error}`);
      }
    }

    // 6. Test Submission Tracking
    console.log('\n6️⃣ Testing Submission Tracking:');
    
    const trackingResponse = await fetch(`${baseUrl}/api/admin/tools?submissionType=detailed&limit=3`, {
      headers: {
        'x-admin-api-key': apiKey
      }
    });

    if (trackingResponse.ok) {
      const trackingData = await trackingResponse.json();
      if (trackingData.success) {
        const detailedTools = trackingData.data.tools || [];
        const toolsWithTracking = detailedTools.filter((tool: any) => 
          tool.submission_id || tool.submitted_by || tool.approved_by
        );
        
        console.log(`   ✅ Submission tracking: ${toolsWithTracking.length}/${detailedTools.length} tools have tracking data`);
        
        if (toolsWithTracking.length > 0) {
          const sampleTool = toolsWithTracking[0];
          console.log(`   📋 Sample tracking fields:`);
          console.log(`      Submission ID: ${sampleTool.submission_id ? 'Present' : 'Missing'}`);
          console.log(`      Submitted By: ${sampleTool.submitted_by || 'Not set'}`);
          console.log(`      Approved By: ${sampleTool.approved_by || 'Not set'}`);
        }
        
        testResults.submissionTracking = true;
      } else {
        console.log(`   ❌ Submission tracking test failed: ${trackingData.error}`);
      }
    }

    // 7. Test Results Summary
    console.log('\n7️⃣ Admin Tools Interface Test Results:');
    
    const passedTests = Object.values(testResults).filter(Boolean).length;
    const totalTests = Object.keys(testResults).length;
    const successRate = Math.round((passedTests / totalTests) * 100);

    console.log(`   📊 Tests Passed: ${passedTests}/${totalTests} (${successRate}%)`);
    
    Object.entries(testResults).forEach(([test, passed]) => {
      const status = passed ? '✅ PASS' : '❌ FAIL';
      console.log(`   ${status} ${test}`);
    });

    if (successRate >= 80) {
      console.log('\n🎉 Admin Tools Management Interface is working well!');
      console.log('\n✨ Verified Features:');
      console.log('   • Enhanced API with dual submission support');
      console.log('   • Advanced filtering by submission type, media, date');
      console.log('   • Tool update functionality with enhanced fields');
      console.log('   • Bulk operations support');
      console.log('   • Media asset display and filtering');
      console.log('   • Submission tracking and audit trail');
      return true;
    } else {
      console.log('\n❌ Admin interface needs attention - some features not working.');
      return false;
    }

  } catch (error) {
    console.error('❌ Admin tools interface test failed:', error);
    return false;
  }
}

// Run the test
testAdminToolsInterface()
  .then((success) => {
    console.log('\n🎉 Admin tools interface test complete!');
    process.exit(success ? 0 : 1);
  })
  .catch((error) => {
    console.error('❌ Test failed:', error);
    process.exit(1);
  });
