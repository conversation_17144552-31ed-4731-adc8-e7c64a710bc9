import { NextRequest, NextResponse } from 'next/server';
import { validate<PERSON><PERSON><PERSON><PERSON> } from '@/lib/auth';
import { getJobManager } from '@/lib/jobs';
import { JobType, JobPriority } from '@/lib/jobs/types';
import { supabase } from '@/lib/supabase';

/**
 * POST /api/admin/content/generate
 * Start content generation for tools or manage generation queue
 */
export async function POST(request: NextRequest) {
  try {
    // Validate admin API key
    const isValid = await validateApiKey(request);
    if (!isValid) {
      return NextResponse.json(
        { success: false, error: 'Unauthorized' },
        { status: 401 }
      );
    }

    const body = await request.json();
    const { action, toolId, options = {} } = body;

    const jobManager = getJobManager();

    switch (action) {
      case 'start_batch_generation':
        // Start content generation for tools that need it
        const { data: toolsNeedingGeneration, error: toolsError } = await supabase
          .from('tools')
          .select('id, name, website, description')
          .in('ai_generation_status', ['pending', 'failed'])
          .limit(options.maxJobs || 10);

        if (toolsError) {
          throw new Error('Failed to fetch tools needing generation');
        }

        const createdJobs = [];
        for (const tool of toolsNeedingGeneration || []) {
          try {
            const job = await jobManager.createJob(
              JobType.CONTENT_GENERATION,
              {
                toolId: tool.id,
                url: tool.website,
                scrapedData: {
                  title: tool.name,
                  description: tool.description,
                  url: tool.website
                }
              },
              {
                priority: options.priority === 'high' ? JobPriority.HIGH : JobPriority.NORMAL
              }
            );
            createdJobs.push(job);
          } catch (jobError) {
            console.error(`Failed to create job for tool ${tool.id}:`, jobError);
          }
        }

        return NextResponse.json({
          success: true,
          message: `Started content generation for ${createdJobs.length} tools`,
          data: {
            jobsCreated: createdJobs.length,
            jobs: createdJobs.map(job => ({
              id: job.id,
              toolId: job.toolId,
              status: job.status
            }))
          }
        });

      case 'generate_single':
        // Generate content for a specific tool
        if (!toolId) {
          return NextResponse.json(
            { success: false, error: 'toolId is required for single generation' },
            { status: 400 }
          );
        }

        const { data: tool, error: toolError } = await supabase
          .from('tools')
          .select('id, name, website, description, scraped_data')
          .eq('id', toolId)
          .single();

        if (toolError || !tool) {
          return NextResponse.json(
            { success: false, error: 'Tool not found' },
            { status: 404 }
          );
        }

        const singleJob = await jobManager.createJob(
          JobType.CONTENT_GENERATION,
          {
            toolId: tool.id,
            url: tool.website,
            scrapedData: tool.scraped_data || {
              title: tool.name,
              description: tool.description,
              url: tool.website
            }
          },
          {
            priority: options.priority === 'high' ? JobPriority.HIGH : JobPriority.NORMAL
          }
        );

        return NextResponse.json({
          success: true,
          message: `Content generation started for ${tool.name}`,
          data: {
            jobId: singleJob.id,
            toolId: tool.id,
            status: singleJob.status
          }
        });

      case 'retry_failed':
        // Retry failed content generation jobs
        const { data: failedJobs, error: failedError } = await supabase
          .from('ai_generation_jobs')
          .select('id, tool_id, scraped_data')
          .eq('status', 'failed')
          .eq('job_type', 'generate')
          .limit(options.maxRetries || 5);

        if (failedError) {
          throw new Error('Failed to fetch failed jobs');
        }

        const retriedJobs = [];
        for (const failedJob of failedJobs || []) {
          try {
            const retryJob = await jobManager.createJob(
              JobType.CONTENT_GENERATION,
              {
                toolId: failedJob.tool_id,
                scrapedData: failedJob.scraped_data
              },
              {
                priority: JobPriority.NORMAL
              }
            );
            retriedJobs.push(retryJob);
          } catch (retryError) {
            console.error(`Failed to retry job ${failedJob.id}:`, retryError);
          }
        }

        return NextResponse.json({
          success: true,
          message: `Retried ${retriedJobs.length} failed jobs`,
          data: {
            jobsRetried: retriedJobs.length,
            jobs: retriedJobs.map(job => ({
              id: job.id,
              toolId: job.toolId,
              status: job.status
            }))
          }
        });

      default:
        return NextResponse.json(
          { success: false, error: 'Invalid action' },
          { status: 400 }
        );
    }

  } catch (error: any) {
    console.error('Content generation API error:', error);
    return NextResponse.json(
      { 
        success: false, 
        error: error.message || 'Failed to process content generation request' 
      },
      { status: 500 }
    );
  }
}
