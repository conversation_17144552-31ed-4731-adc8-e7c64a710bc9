'use client';

import React, { useState, useEffect } from 'react';
import { WorkflowStatusIndicator } from './WorkflowStatusIndicator';

interface ManualContentGenerationProps {
  toolId: string;
  toolName: string;
  currentStatus?: string;
  onGenerationComplete?: (result: any) => void;
  onError?: (error: string) => void;
  className?: string;
}

interface GenerationProgress {
  jobId: string;
  status: 'pending' | 'processing' | 'completed' | 'failed';
  progress: number;
  stage: string;
  estimatedTimeRemaining?: number;
  error?: string;
}

export function ManualContentGeneration({
  toolId,
  toolName,
  currentStatus = 'draft',
  onGenerationComplete,
  onError,
  className = ''
}: ManualContentGenerationProps) {
  const [isGenerating, setIsGenerating] = useState(false);
  const [generationProgress, setGenerationProgress] = useState<GenerationProgress | null>(null);
  const [error, setError] = useState<string | null>(null);
  const [priority, setPriority] = useState<'low' | 'normal' | 'high'>('normal');

  // Poll for progress updates when generation is active
  useEffect(() => {
    let interval: NodeJS.Timeout;
    
    if (isGenerating && generationProgress?.jobId) {
      interval = setInterval(async () => {
        try {
          const response = await fetch(`/api/automation/jobs/${generationProgress.jobId}`, {
            headers: {
              'x-admin-api-key': process.env.NEXT_PUBLIC_ADMIN_API_KEY || 'aidude_admin_2024_secure_key_xyz789'
            }
          });

          if (response.ok) {
            const jobData = await response.json();
            const job = jobData.data;

            setGenerationProgress(prev => prev ? {
              ...prev,
              status: job.status,
              progress: job.progress || 0,
              stage: job.progressDetails?.stage || 'Processing',
              estimatedTimeRemaining: job.progressDetails?.estimatedTimeRemaining,
              error: job.error
            } : null);

            // Check if job is complete
            if (job.status === 'completed') {
              setIsGenerating(false);
              onGenerationComplete?.(job.result);
            } else if (job.status === 'failed') {
              setIsGenerating(false);
              const errorMessage = job.error || 'Content generation failed';
              setError(errorMessage);
              onError?.(errorMessage);
            }
          }
        } catch (err) {
          console.error('Error polling job progress:', err);
        }
      }, 2000); // Poll every 2 seconds
    }

    return () => {
      if (interval) {
        clearInterval(interval);
      }
    };
  }, [isGenerating, generationProgress?.jobId, onGenerationComplete, onError]);

  const handleStartGeneration = async () => {
    try {
      setIsGenerating(true);
      setError(null);
      setGenerationProgress(null);

      // Start content generation workflow
      const response = await fetch(`/api/admin/workflow/${toolId}/state`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'x-admin-api-key': process.env.NEXT_PUBLIC_ADMIN_API_KEY || 'aidude_admin_2024_secure_key_xyz789'
        },
        body: JSON.stringify({
          action: 'start_content_generation',
          priority,
          triggeredBy: 'admin_manual'
        })
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to start content generation');
      }

      const result = await response.json();
      
      setGenerationProgress({
        jobId: result.data.jobId,
        status: 'pending',
        progress: 0,
        stage: 'Initializing'
      });

    } catch (err) {
      console.error('Error starting content generation:', err);
      const errorMessage = err instanceof Error ? err.message : 'Failed to start content generation';
      setError(errorMessage);
      setIsGenerating(false);
      onError?.(errorMessage);
    }
  };

  const handleCancelGeneration = async () => {
    if (!generationProgress?.jobId) return;

    try {
      const response = await fetch(`/api/automation/jobs/${generationProgress.jobId}`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'x-admin-api-key': process.env.NEXT_PUBLIC_ADMIN_API_KEY || 'aidude_admin_2024_secure_key_xyz789'
        },
        body: JSON.stringify({ action: 'cancel' })
      });

      if (response.ok) {
        setIsGenerating(false);
        setGenerationProgress(null);
      }
    } catch (err) {
      console.error('Error canceling generation:', err);
    }
  };

  const formatTimeRemaining = (seconds?: number) => {
    if (!seconds) return 'Calculating...';
    
    const minutes = Math.floor(seconds / 60);
    const remainingSeconds = seconds % 60;
    
    if (minutes > 0) {
      return `${minutes}m ${remainingSeconds}s`;
    }
    return `${remainingSeconds}s`;
  };

  return (
    <div className={`manual-content-generation bg-zinc-800 border border-zinc-700 rounded-lg p-6 ${className}`}>
      <div className="flex items-center justify-between mb-4">
        <div>
          <h3 className="text-lg font-semibold text-white">Content Generation</h3>
          <p className="text-gray-400 text-sm">Generate AI content for {toolName}</p>
        </div>
        
        {!isGenerating && (
          <div className="flex items-center space-x-3">
            <select
              value={priority}
              onChange={(e) => setPriority(e.target.value as 'low' | 'normal' | 'high')}
              className="px-3 py-1 bg-zinc-700 border border-zinc-600 rounded text-white text-sm focus:outline-none focus:ring-2 focus:ring-orange-500"
            >
              <option value="low">Low Priority</option>
              <option value="normal">Normal Priority</option>
              <option value="high">High Priority</option>
            </select>
            
            <button
              onClick={handleStartGeneration}
              disabled={isGenerating}
              className="px-4 py-2 bg-orange-600 hover:bg-orange-700 disabled:bg-orange-800 disabled:opacity-50 text-white rounded-md transition-colors flex items-center gap-2"
            >
              <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 10V3L4 14h7v7l9-11h-7z" />
              </svg>
              Generate Content
            </button>
          </div>
        )}
      </div>

      {/* Generation Progress */}
      {isGenerating && generationProgress && (
        <div className="mb-4">
          <div className="flex items-center justify-between mb-2">
            <span className="text-sm font-medium text-white">
              {generationProgress.stage}
            </span>
            <div className="flex items-center space-x-3">
              <span className="text-sm text-gray-400">
                {generationProgress.progress}%
              </span>
              <button
                onClick={handleCancelGeneration}
                className="text-red-400 hover:text-red-300 text-sm"
              >
                Cancel
              </button>
            </div>
          </div>
          
          <div className="w-full bg-zinc-700 rounded-full h-2 mb-2">
            <div 
              className="bg-orange-500 h-2 rounded-full transition-all duration-300"
              style={{ width: `${generationProgress.progress}%` }}
            />
          </div>
          
          {generationProgress.estimatedTimeRemaining && (
            <p className="text-xs text-gray-400">
              Estimated time remaining: {formatTimeRemaining(generationProgress.estimatedTimeRemaining)}
            </p>
          )}
        </div>
      )}

      {/* Workflow Status */}
      <WorkflowStatusIndicator
        toolId={toolId}
        currentStage={currentStatus as any}
        showHistory={false}
        className="mb-4"
      />

      {/* Error Display */}
      {error && (
        <div className="bg-red-900/20 border border-red-500 rounded-lg p-3 mb-4">
          <div className="flex items-center space-x-2">
            <svg className="w-5 h-5 text-red-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
            </svg>
            <span className="text-red-400 text-sm">{error}</span>
          </div>
        </div>
      )}

      {/* Generation Status */}
      {generationProgress && (
        <div className="bg-zinc-700 rounded-lg p-4">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-white">Job ID: {generationProgress.jobId}</p>
              <p className="text-xs text-gray-400">Status: {generationProgress.status}</p>
            </div>
            <div className="flex items-center space-x-2">
              {generationProgress.status === 'processing' && (
                <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-orange-500"></div>
              )}
              <span className={`px-2 py-1 rounded text-xs ${
                generationProgress.status === 'completed' ? 'bg-green-600 text-white' :
                generationProgress.status === 'failed' ? 'bg-red-600 text-white' :
                generationProgress.status === 'processing' ? 'bg-orange-600 text-white' :
                'bg-gray-600 text-white'
              }`}>
                {generationProgress.status.toUpperCase()}
              </span>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}
