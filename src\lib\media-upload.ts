// Media Upload Handler for Detailed Submissions
// Handles file uploads and storage for user-provided media assets

export interface MediaUploadResult {
  success: boolean;
  url?: string;
  error?: string;
  metadata?: {
    filename: string;
    size: number;
    type: string;
    dimensions?: { width: number; height: number };
  };
}

export interface MediaAssets {
  primaryImage?: string; // Either screenshot OR OG image (required)
  primaryImageType?: 'screenshot' | 'ogImage';
  logo?: string; // Logo/favicon (required) - can be upload or URL
  logoSource?: 'upload' | 'url';
}

export class MediaUploadHandler {
  private maxFileSize = {
    logo: 1024 * 1024, // 1MB for logo/favicon
    primaryImage: 5 * 1024 * 1024, // 5MB for primary image
  };

  private allowedTypes = {
    logo: ['image/x-icon', 'image/png', 'image/jpeg', 'image/jpg'],
    primaryImage: ['image/png', 'image/jpeg', 'image/jpg'],
  };

  /**
   * Validate file before upload
   */
  private validateFile(file: File, type: 'logo' | 'primaryImage'): { valid: boolean; error?: string } {
    // Check file size
    if (file.size > this.maxFileSize[type]) {
      const maxSizeMB = Math.round(this.maxFileSize[type] / (1024 * 1024));
      return { valid: false, error: `File too large. Maximum size: ${maxSizeMB}MB` };
    }

    // Check file type
    if (!this.allowedTypes[type].includes(file.type)) {
      const allowedExtensions = this.allowedTypes[type].map(t => t.split('/')[1]).join(', ');
      return { valid: false, error: `Invalid file type. Allowed: ${allowedExtensions}` };
    }

    return { valid: true };
  }

  /**
   * Upload logo/favicon file
   */
  async uploadLogo(file: File, toolId: string): Promise<MediaUploadResult> {
    const validation = this.validateFile(file, 'logo');
    if (!validation.valid) {
      return { success: false, error: validation.error };
    }

    try {
      const base64 = await this.fileToBase64(file);
      const dataUrl = `data:${file.type};base64,${base64}`;

      return {
        success: true,
        url: dataUrl,
        metadata: {
          filename: file.name,
          size: file.size,
          type: file.type,
        }
      };
    } catch (error) {
      return { success: false, error: 'Failed to upload logo' };
    }
  }

  /**
   * Upload primary image (screenshot or OG image)
   */
  async uploadPrimaryImage(file: File, toolId: string, imageType: 'screenshot' | 'ogImage'): Promise<MediaUploadResult> {
    const validation = this.validateFile(file, 'primaryImage');
    if (!validation.valid) {
      return { success: false, error: validation.error };
    }

    try {
      const base64 = await this.fileToBase64(file);
      const dataUrl = `data:${file.type};base64,${base64}`;

      return {
        success: true,
        url: dataUrl,
        metadata: {
          filename: file.name,
          size: file.size,
          type: file.type,
        }
      };
    } catch (error) {
      return { success: false, error: `Failed to upload ${imageType}` };
    }
  }

  /**
   * Process simplified media assets from form data
   * Requires: One primary image (screenshot OR OG image) + One logo (upload OR URL)
   */
  async processMediaAssets(formData: FormData, toolId: string): Promise<MediaAssets> {
    const assets: MediaAssets = {};

    // Process primary image - check screenshot first, then OG image
    const screenshotFile = formData.get('primaryImage') as File;
    const imageType = formData.get('primaryImageType') as string;

    if (screenshotFile && screenshotFile.size > 0) {
      const result = await this.uploadPrimaryImage(
        screenshotFile,
        toolId,
        imageType === 'ogImage' ? 'ogImage' : 'screenshot'
      );
      if (result.success) {
        assets.primaryImage = result.url;
        assets.primaryImageType = imageType === 'ogImage' ? 'ogImage' : 'screenshot';
      }
    }

    // Process logo - check file upload first, then URL
    const logoFile = formData.get('logo') as File;
    const logoUrl = formData.get('logoUrl') as string;

    if (logoFile && logoFile.size > 0) {
      // User uploaded a logo file
      const result = await this.uploadLogo(logoFile, toolId);
      if (result.success) {
        assets.logo = result.url;
        assets.logoSource = 'upload';
      }
    } else if (logoUrl && logoUrl.trim()) {
      // User provided a logo URL
      if (this.isValidUrl(logoUrl.trim())) {
        assets.logo = logoUrl.trim();
        assets.logoSource = 'url';
      }
    }

    return assets;
  }

  /**
   * Convert file to base64
   */
  private fileToBase64(file: File): Promise<string> {
    return new Promise((resolve, reject) => {
      const reader = new FileReader();
      reader.readAsDataURL(file);
      reader.onload = () => {
        const result = reader.result as string;
        const base64 = result.split(',')[1]; // Remove data:image/...;base64, prefix
        resolve(base64);
      };
      reader.onerror = error => reject(error);
    });
  }

  /**
   * Validate URL format
   */
  private isValidUrl(url: string): boolean {
    try {
      new URL(url);
      return true;
    } catch {
      return false;
    }
  }

  /**
   * Get image dimensions (for validation)
   */
  private getImageDimensions(file: File): Promise<{ width: number; height: number }> {
    return new Promise((resolve, reject) => {
      const img = new Image();
      const url = URL.createObjectURL(file);
      
      img.onload = () => {
        URL.revokeObjectURL(url);
        resolve({ width: img.width, height: img.height });
      };
      
      img.onerror = () => {
        URL.revokeObjectURL(url);
        reject(new Error('Failed to load image'));
      };
      
      img.src = url;
    });
  }
}

// Export singleton instance
export const mediaUploadHandler = new MediaUploadHandler();
