import { NextRequest, NextResponse } from 'next/server';
import { getAdminTools, updateTool, deleteTool } from '@/lib/supabase';
import { validateApiKey } from '@/lib/auth';
import { supabase } from '@/lib/supabase';
import { workflowManager } from '@/lib/workflow/workflow-manager';

/**
 * GET /api/admin/tools/[id]
 * Get a specific tool for admin (includes drafts and archived)
 */
export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    // Validate admin API key
    const isValid = await validateApiKey(request);
    if (!isValid) {
      return NextResponse.json(
        { success: false, error: 'Unauthorized' },
        { status: 401 }
      );
    }

    const { id } = await params;

    if (!id) {
      return NextResponse.json(
        { success: false, error: 'Tool ID is required' },
        { status: 400 }
      );
    }

    // Get tool data with workflow information
    const { data: tool, error: toolError } = await supabase
      .from('tools')
      .select(`
        *,
        workflow_states (
          current_stage,
          progress,
          started_at,
          updated_at,
          completed_stages,
          metadata
        )
      `)
      .eq('id', id)
      .single();

    if (toolError || !tool) {
      return NextResponse.json(
        { success: false, error: 'Tool not found' },
        { status: 404 }
      );
    }

    // Add workflow stage to tool data
    const toolWithWorkflow = {
      ...tool,
      workflow_stage: tool.workflow_states?.current_stage || 'draft',
      workflow_progress: tool.workflow_states?.progress || 0,
      workflow_metadata: tool.workflow_states?.metadata || {}
    };

    // Remove the nested workflow_states object
    delete toolWithWorkflow.workflow_states;

    return NextResponse.json({
      success: true,
      data: toolWithWorkflow,
    });
  } catch (error) {
    console.error('Error fetching admin tool:', error);
    return NextResponse.json(
      { success: false, error: 'Failed to fetch tool' },
      { status: 500 }
    );
  }
}

/**
 * PUT /api/admin/tools/[id]
 * Update a specific tool (admin only)
 */
export async function PUT(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    // Validate admin API key
    const isValid = await validateApiKey(request);
    if (!isValid) {
      return NextResponse.json(
        { success: false, error: 'Unauthorized' },
        { status: 401 }
      );
    }

    const { id } = await params;

    if (!id) {
      return NextResponse.json(
        { success: false, error: 'Tool ID is required' },
        { status: 400 }
      );
    }

    // Check if versioning is requested
    const { searchParams } = new URL(request.url);
    const createVersion = searchParams.get('createVersion') !== 'false';

    const updates = await request.json();

    // Ensure link field is populated (required by database)
    // If no URL slug provided, generate one from the tool slug/name
    if (!updates.link || !updates.link.trim()) {
      if (updates.slug) {
        updates.link = `/tools/${updates.slug}`;
      } else if (updates.name) {
        updates.link = `/tools/${updates.name.toLowerCase().replace(/\s+/g, '-')}`;
      }
    }

    // Pass updates directly to updateTool - it handles the camelCase to snake_case transformation
    const updatedTool = await updateTool(id, updates, createVersion);

    return NextResponse.json({
      success: true,
      data: updatedTool,
    });
  } catch (error) {
    console.error('Error updating tool:', error);
    return NextResponse.json(
      { success: false, error: 'Failed to update tool' },
      { status: 500 }
    );
  }
}

/**
 * PATCH /api/admin/tools/[id]
 * Update tool information with workflow support
 */
export async function PATCH(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    // Validate admin API key
    const isValid = await validateApiKey(request);
    if (!isValid) {
      return NextResponse.json(
        { success: false, error: 'Unauthorized' },
        { status: 401 }
      );
    }

    const { id } = await params;
    const body = await request.json();

    if (!id) {
      return NextResponse.json(
        { success: false, error: 'Tool ID is required' },
        { status: 400 }
      );
    }

    // Prepare update data
    const updateData: any = {
      updated_at: new Date().toISOString()
    };

    // Handle specific field updates (enhanced for dual submission system)
    const allowedFields = [
      'name', 'website', 'description', 'short_description', 'detailed_description',
      'features', 'pricing', 'pros_and_cons', 'meta_title', 'meta_description',
      'meta_keywords', 'faqs', 'generated_content', 'ai_generation_status',
      'content_status', 'category_id', 'tags', 'hashtags',
      // Enhanced dual submission fields
      'use_cases', 'target_audience', 'integrations', 'pricing_type', 'pricing_details',
      'pros', 'cons', 'submission_type', 'submitted_by', 'approved_by', 'approved_at',
      'editorial_notes', 'primary_image', 'primary_image_type', 'logo_url',
      'media_source', 'media_updated_at', 'published_at'
    ];

    allowedFields.forEach(field => {
      if (body[field] !== undefined) {
        updateData[field] = body[field];
      }
    });

    // Update tool in database
    const { data: updatedTool, error: updateError } = await supabase
      .from('tools')
      .update(updateData)
      .eq('id', id)
      .select()
      .single();

    if (updateError) {
      throw new Error(`Failed to update tool: ${updateError.message}`);
    }

    // Handle workflow transitions if needed
    if (body.workflow_action) {
      try {
        switch (body.workflow_action) {
          case 'start_content_generation':
            await workflowManager.startContentGeneration(id, {
              priority: body.priority || 'normal',
              triggeredBy: 'admin'
            });
            break;

          case 'start_editorial_review':
            await workflowManager.startEditorialReview(id, 'admin');
            break;

          case 'transition':
            if (body.target_stage) {
              await workflowManager.transitionTo(
                id,
                body.target_stage,
                'admin',
                body.notes
              );
            }
            break;
        }
      } catch (workflowError) {
        console.error('Workflow action error:', workflowError);
        // Don't fail the entire request for workflow errors
      }
    }

    return NextResponse.json({
      success: true,
      data: updatedTool,
      message: 'Tool updated successfully'
    });

  } catch (error: any) {
    console.error('Tool update API error:', error);
    return NextResponse.json(
      {
        success: false,
        error: error.message || 'Failed to update tool'
      },
      { status: 500 }
    );
  }
}

/**
 * DELETE /api/admin/tools/[id]
 * Delete a specific tool (admin only)
 */
export async function DELETE(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    // Validate admin API key
    const isValid = await validateApiKey(request);
    if (!isValid) {
      return NextResponse.json(
        { success: false, error: 'Unauthorized' },
        { status: 401 }
      );
    }

    const { id } = await params;

    if (!id) {
      return NextResponse.json(
        { success: false, error: 'Tool ID is required' },
        { status: 400 }
      );
    }

    await deleteTool(id);

    return NextResponse.json({
      success: true,
      message: 'Tool deleted successfully',
    });
  } catch (error) {
    console.error('Error deleting tool:', error);
    return NextResponse.json(
      { success: false, error: 'Failed to delete tool' },
      { status: 500 }
    );
  }
}
