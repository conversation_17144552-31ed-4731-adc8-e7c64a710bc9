// AI System Main Exports
// Dual AI Provider Setup (OpenAI + OpenRouter) with Intelligent Model Selection

// Main Content Generator
export { AIContentGenerator } from './content-generator';

// Provider Clients
export { OpenAIClient } from './providers/openai-client';
export { OpenRouterClient } from './providers/openrouter-client';

// Core AI System Components
export { ModelSelector } from './model-selector';
export { PromptManager } from './prompt-manager';
export { ContextWindowManager } from './context-window-manager';
export { AIErrorHandler } from './error-handler';

// Types and Interfaces
export type {
  AIProvider,
  AIModel,
  ContentComplexity,
  ProcessingPriority,
  AIModelConfig,
  ModelSelectionCriteria,
  ModelConfig,
  GenerationOptions,
  AIResponse,
  GeneratedContent,
  ValidationResult,
  GenerationResult,
  ErrorContext,
  ErrorResult,
  MultiPromptContext,
  AIProcessingConfig,
  ProcessedContent
} from './types';

// Configuration Constants
export {
  MODEL_OUTPUT_LIMITS,
  OPENAI_CONFIG,
  OPENROUTER_CONFIG
} from './types';

// Utility Functions
export const AIUtils = {
  /**
   * Calculate token count for text (approximate)
   */
  calculateTokenCount: (text: string): number => {
    return Math.ceil(text.length / 4);
  },

  /**
   * Get recommended model for specific use case
   */
  getRecommendedModel: async (useCase: 'speed' | 'quality' | 'cost' | 'largeContent') => {
    const { ModelSelector } = await import('./model-selector');
    return ModelSelector.getRecommendedModels()[useCase];
  },

  /**
   * Validate AI provider configuration
   */
  validateConfiguration: (): { valid: boolean; issues: string[] } => {
    const issues: string[] = [];

    if (!process.env.OPENAI_API_KEY) {
      issues.push('OPENAI_API_KEY environment variable is missing');
    }

    if (!process.env.OPENROUTER_API_KEY) {
      issues.push('OPENROUTER_API_KEY environment variable is missing');
    }

    if (!process.env.SITE_URL) {
      issues.push('SITE_URL environment variable is missing (required for OpenRouter)');
    }

    return {
      valid: issues.length === 0,
      issues
    };
  },

  /**
   * Get provider status
   */
  getProviderStatus: async () => {
    const { AIContentGenerator } = await import('./content-generator');
    const generator = new AIContentGenerator();
    return await generator.getProviderStatus();
  }
};

// Factory Functions
export const createAIContentGenerator = async (options?: {
  preferredProvider?: any;
  fallbackEnabled?: boolean;
}) => {
  const { AIContentGenerator } = await import('./content-generator');
  return new AIContentGenerator();
};

export const createOpenAIClient = async (config?: any) => {
  const { OpenAIClient } = await import('./providers/openai-client');
  return new OpenAIClient(config);
};

export const createOpenRouterClient = async (config?: any) => {
  const { OpenRouterClient } = await import('./providers/openrouter-client');
  return new OpenRouterClient(config);
};

// Quick Start Functions
export const quickGenerate = async (
  content: string,
  url: string,
  options?: {
    priority?: any;
    complexity?: any;
  }
): Promise<any> => {
  const { AIContentGenerator } = await import('./content-generator');
  const generator = new AIContentGenerator();
  return await generator.generateContent(content, url, options);
};

export const quickValidateContent = async (content: any): Promise<any> => {
  const { AIContentGenerator } = await import('./content-generator');
  const generator = new AIContentGenerator();
  return await (generator as any).validateGeneratedContent(content);
};

// AI Dude Quick Generate Function (now the sole methodology)
export const quickGenerateAIDude = async (
  content: string,
  url: string,
  options?: {
    priority?: any;
    complexity?: any;
  }
): Promise<any> => {
  const { AIContentGenerator } = await import('./content-generator');
  const generator = new AIContentGenerator();

  // Always use AI Dude methodology (sole methodology)
  return await generator.generateContent(content, url, options);
};

// Error Handling Utilities
export const handleAIError = async (error: any, context?: any): Promise<any> => {
  const { AIErrorHandler } = await import('./error-handler');
  return AIErrorHandler.handleAIError(error, context || {});
};

export const retryWithFallback = async <T>(
  operation: () => Promise<T>,
  maxRetries?: number,
  context?: any
): Promise<T> => {
  const { AIErrorHandler } = await import('./error-handler');
  return AIErrorHandler.retryWithFallback(operation, maxRetries, context);
};

// Configuration Helpers
export const getModelCapabilities = async (model: any) => {
  const { ModelSelector } = await import('./model-selector');
  return ModelSelector.getModelCapabilities(model);
};

export const estimateProcessingCost = async (
  model: any,
  inputTokens: number,
  outputTokens: number
): Promise<number> => {
  const { ModelSelector } = await import('./model-selector');
  return ModelSelector.estimateCost(model, inputTokens, outputTokens);
};

// Content Processing Utilities
export const optimizeContentForAI = async (
  content: string,
  contentQuality?: number,
  scrapingCost?: number
) => {
  const { ContextWindowManager } = await import('./context-window-manager');
  return ContextWindowManager.optimizeContentForAI(content, contentQuality, scrapingCost);
};

export const splitContentForModel = async (content: string, modelConfig: any): Promise<string[]> => {
  const { ContextWindowManager } = await import('./context-window-manager');
  return ContextWindowManager.splitContentForModel(content, modelConfig);
};

// Prompt Utilities
export const buildPrompt = async (content: string, url: string, options?: any) => {
  const { PromptManager } = await import('./prompt-manager');
  return PromptManager.buildSinglePrompt(content, url, options);
};

export const extractJsonFromResponse = async (response: string) => {
  const { PromptManager } = await import('./prompt-manager');
  return PromptManager.extractJsonFromResponse(response);
};

// System Health Check
export const performHealthCheck = async (): Promise<{
  status: 'healthy' | 'degraded' | 'unhealthy';
  providers: {
    openai: { status: 'up' | 'down'; latency?: number };
    openrouter: { status: 'up' | 'down'; latency?: number };
  };
  configuration: { valid: boolean; issues: string[] };
}> => {
  const configValidation = AIUtils.validateConfiguration();
  
  try {
    const startTime = Date.now();
    const providerStatus = await AIUtils.getProviderStatus();
    const endTime = Date.now();

    const openaiLatency = providerStatus.openai.available ? endTime - startTime : undefined;
    const openrouterLatency = providerStatus.openrouter.available ? endTime - startTime : undefined;

    let status: 'healthy' | 'degraded' | 'unhealthy' = 'healthy';
    
    if (!providerStatus.openai.available && !providerStatus.openrouter.available) {
      status = 'unhealthy';
    } else if (!providerStatus.openai.available || !providerStatus.openrouter.available) {
      status = 'degraded';
    }

    return {
      status,
      providers: {
        openai: {
          status: providerStatus.openai.available ? 'up' : 'down',
          latency: openaiLatency
        },
        openrouter: {
          status: providerStatus.openrouter.available ? 'up' : 'down',
          latency: openrouterLatency
        }
      },
      configuration: configValidation
    };

  } catch (error) {
    return {
      status: 'unhealthy',
      providers: {
        openai: { status: 'down' },
        openrouter: { status: 'down' }
      },
      configuration: configValidation
    };
  }
};

// Version and Info
export const AI_SYSTEM_INFO = {
  version: '1.0.0',
  providers: ['OpenAI', 'OpenRouter'],
  models: {
    openai: ['gpt-4o-2024-11-20', 'gpt-4o', 'gpt-4o-mini'],
    openrouter: ['google/gemini-2.5-pro-preview', 'google/gemini-pro-1.5', 'anthropic/claude-3.5-sonnet']
  },
  features: [
    'Dual Provider Support',
    'Intelligent Model Selection',
    'Context Window Management',
    'Multi-Prompt Processing',
    'Automatic Fallback',
    'Error Recovery',
    'Cost Optimization',
    'Quality-First Processing'
  ]
};
