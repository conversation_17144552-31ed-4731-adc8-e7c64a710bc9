-- Add missing columns to tools table for detailed submission support
-- Migration: 003_add_missing_tools_columns.sql

-- Add pricing_type column if it doesn't exist
DO $$ BEGIN
  IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name='tools' AND column_name='pricing_type') THEN
    ALTER TABLE tools ADD COLUMN pricing_type VARCHAR(50);
    COMMENT ON COLUMN tools.pricing_type IS 'Pricing model: free, freemium, paid, open-source, etc.';
  END IF;
END $$;

-- Add pricing_details column if it doesn't exist
DO $$ BEGIN
  IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name='tools' AND column_name='pricing_details') THEN
    ALTER TABLE tools ADD COLUMN pricing_details TEXT;
    COMMENT ON COLUMN tools.pricing_details IS 'Detailed pricing information and plans';
  END IF;
END $$;

-- Add pros column if it doesn't exist
DO $$ BEGIN
  IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name='tools' AND column_name='pros') THEN
    ALTER TABLE tools ADD COLUMN pros TEXT;
    COMMENT ON COLUMN tools.pros IS 'Advantages and strengths of the tool';
  END IF;
END $$;

-- Add cons column if it doesn't exist
DO $$ BEGIN
  IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name='tools' AND column_name='cons') THEN
    ALTER TABLE tools ADD COLUMN cons TEXT;
    COMMENT ON COLUMN tools.cons IS 'Limitations and areas for improvement';
  END IF;
END $$;

-- Add indexes for better query performance
CREATE INDEX IF NOT EXISTS idx_tools_pricing_type ON tools(pricing_type);
CREATE INDEX IF NOT EXISTS idx_tools_submission_type ON tools(submission_type);

-- Update any existing tools to have default pricing_type
UPDATE tools 
SET pricing_type = 'unknown' 
WHERE pricing_type IS NULL;
