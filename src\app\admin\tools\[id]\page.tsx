'use client';

import React, { useState, useEffect } from 'react';
import { useParams, useRouter } from 'next/navigation';
// AdminLayout is provided by the parent layout.tsx file
import { WorkflowStatusIndicator } from '@/components/admin/WorkflowStatusIndicator';
import { ManualContentGeneration } from '@/components/admin/ManualContentGeneration';
import { ContentOverrideEditor } from '@/components/admin/ContentOverrideEditor';
import { PartialContentGeneration } from '@/components/admin/PartialContentGeneration';

interface Tool {
  id: string;
  name: string;
  website: string;
  description: string;
  content_status: string;
  ai_generation_status: string;
  generated_content: any;
  workflow_stage: string;
  created_at: string;
  updated_at: string;
}

export default function ToolManagementPage() {
  const params = useParams();
  const router = useRouter();
  const toolId = params.id as string;

  const [tool, setTool] = useState<Tool | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [activeTab, setActiveTab] = useState<'overview' | 'content' | 'workflow' | 'partial'>('overview');
  const [showContentEditor, setShowContentEditor] = useState(false);

  useEffect(() => {
    loadToolData();
  }, [toolId]);

  const loadToolData = async () => {
    try {
      setLoading(true);
      setError(null);

      const response = await fetch(`/api/admin/tools/${toolId}`, {
        headers: {
          'x-admin-api-key': process.env.NEXT_PUBLIC_ADMIN_API_KEY || 'aidude_admin_2024_secure_key_xyz789'
        }
      });

      if (!response.ok) {
        throw new Error('Failed to load tool data');
      }

      const data = await response.json();
      setTool(data.success ? data.data : null);

    } catch (err) {
      console.error('Error loading tool:', err);
      setError(err instanceof Error ? err.message : 'Failed to load tool');
    } finally {
      setLoading(false);
    }
  };

  const handleContentGenerationComplete = (result: any) => {
    console.log('Content generation completed:', result);
    // Refresh tool data to get updated content
    loadToolData();
  };

  const handleContentSave = async (updatedContent: any) => {
    try {
      const response = await fetch(`/api/admin/tools/${toolId}`, {
        method: 'PATCH',
        headers: {
          'Content-Type': 'application/json',
          'x-admin-api-key': process.env.NEXT_PUBLIC_ADMIN_API_KEY || 'aidude_admin_2024_secure_key_xyz789'
        },
        body: JSON.stringify({
          generated_content: updatedContent,
          ai_generation_status: 'completed'
        })
      });

      if (!response.ok) {
        throw new Error('Failed to save content');
      }

      setShowContentEditor(false);
      loadToolData();
    } catch (err) {
      throw err; // Re-throw to be handled by the editor component
    }
  };

  const handlePartialGenerationComplete = (updatedContent: any, sections: string[]) => {
    console.log('Partial generation completed:', { updatedContent, sections });
    // Update tool with new content
    handleContentSave(updatedContent);
  };

  const handleError = (error: string) => {
    setError(error);
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center min-h-96">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-orange-500"></div>
      </div>
    );
  }

  if (error || !tool) {
    return (
      <div className="bg-red-900/20 border border-red-500 rounded-lg p-6">
        <h2 className="text-xl font-semibold text-red-400 mb-2">Error</h2>
        <p className="text-red-300">{error || 'Tool not found'}</p>
        <button
          onClick={() => router.back()}
          className="mt-4 px-4 py-2 bg-red-600 hover:bg-red-700 text-white rounded-md"
        >
          Go Back
        </button>
      </div>
    );
  }

  return (
    <div className="space-y-6">
        {/* Header */}
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-2xl font-bold text-white">{tool.name}</h1>
            <p className="text-gray-400">{tool.website}</p>
          </div>
          <div className="flex items-center space-x-3">
            <a
              href={tool.website}
              target="_blank"
              rel="noopener noreferrer"
              className="px-4 py-2 bg-zinc-600 hover:bg-zinc-500 text-white rounded-md transition-colors"
            >
              Visit Tool
            </a>
            <button
              onClick={() => router.back()}
              className="px-4 py-2 bg-zinc-600 hover:bg-zinc-500 text-white rounded-md transition-colors"
            >
              Back
            </button>
          </div>
        </div>

        {/* Tabs */}
        <div className="border-b border-zinc-700">
          <nav className="flex space-x-8">
            {[
              { key: 'overview', label: 'Overview' },
              { key: 'content', label: 'Content Generation' },
              { key: 'workflow', label: 'Workflow' },
              { key: 'partial', label: 'Partial Generation' }
            ].map((tab) => (
              <button
                key={tab.key}
                onClick={() => setActiveTab(tab.key as any)}
                className={`py-2 px-1 border-b-2 font-medium text-sm ${
                  activeTab === tab.key
                    ? 'border-orange-500 text-orange-400'
                    : 'border-transparent text-gray-400 hover:text-gray-300'
                }`}
              >
                {tab.label}
              </button>
            ))}
          </nav>
        </div>

        {/* Tab Content */}
        <div className="space-y-6">
          {activeTab === 'overview' && (
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
              {/* Tool Information */}
              <div className="bg-zinc-800 border border-zinc-700 rounded-lg p-6">
                <h3 className="text-lg font-semibold text-white mb-4">Tool Information</h3>
                <div className="space-y-3">
                  <div>
                    <label className="text-sm font-medium text-gray-400">Name</label>
                    <p className="text-white">{tool.name}</p>
                  </div>
                  <div>
                    <label className="text-sm font-medium text-gray-400">Website</label>
                    <p className="text-white">{tool.website}</p>
                  </div>
                  <div>
                    <label className="text-sm font-medium text-gray-400">Description</label>
                    <p className="text-white">{tool.description}</p>
                  </div>
                  <div>
                    <label className="text-sm font-medium text-gray-400">Status</label>
                    <div className="flex items-center space-x-2">
                      <span className={`px-2 py-1 rounded text-xs ${
                        tool.content_status === 'published' ? 'bg-green-600 text-white' :
                        tool.content_status === 'approved' ? 'bg-blue-600 text-white' :
                        tool.content_status === 'under_review' ? 'bg-yellow-600 text-white' :
                        'bg-gray-600 text-white'
                      }`}>
                        {tool.content_status}
                      </span>
                      <span className={`px-2 py-1 rounded text-xs ${
                        tool.ai_generation_status === 'completed' ? 'bg-green-600 text-white' :
                        tool.ai_generation_status === 'processing' ? 'bg-orange-600 text-white' :
                        tool.ai_generation_status === 'failed' ? 'bg-red-600 text-white' :
                        'bg-gray-600 text-white'
                      }`}>
                        AI: {tool.ai_generation_status}
                      </span>
                    </div>
                  </div>
                </div>
              </div>

              {/* Workflow Status */}
              <div className="bg-zinc-800 border border-zinc-700 rounded-lg p-6">
                <h3 className="text-lg font-semibold text-white mb-4">Workflow Status</h3>
                <WorkflowStatusIndicator
                  toolId={tool.id}
                  currentStage={tool.workflow_stage as any}
                  showHistory={true}
                />
              </div>
            </div>
          )}

          {activeTab === 'content' && (
            <div className="space-y-6">
              <ManualContentGeneration
                toolId={tool.id}
                toolName={tool.name}
                currentStatus={tool.workflow_stage}
                onGenerationComplete={handleContentGenerationComplete}
                onError={handleError}
              />

              {tool.generated_content && (
                <div className="bg-zinc-800 border border-zinc-700 rounded-lg p-6">
                  <div className="flex items-center justify-between mb-4">
                    <h3 className="text-lg font-semibold text-white">Generated Content</h3>
                    <button
                      onClick={() => setShowContentEditor(true)}
                      className="px-4 py-2 bg-orange-600 hover:bg-orange-700 text-white rounded-md transition-colors"
                    >
                      Edit Content
                    </button>
                  </div>
                  <div className="bg-zinc-700 rounded-lg p-4 max-h-60 overflow-y-auto">
                    <pre className="text-sm text-gray-300 whitespace-pre-wrap">
                      {JSON.stringify(tool.generated_content, null, 2)}
                    </pre>
                  </div>
                </div>
              )}
            </div>
          )}

          {activeTab === 'workflow' && (
            <div className="bg-zinc-800 border border-zinc-700 rounded-lg p-6">
              <h3 className="text-lg font-semibold text-white mb-4">Workflow Management</h3>
              <WorkflowStatusIndicator
                toolId={tool.id}
                currentStage={tool.workflow_stage as any}
                showHistory={true}
                onStageClick={(stage) => console.log('Stage clicked:', stage)}
              />
            </div>
          )}

          {activeTab === 'partial' && tool.generated_content && (
            <PartialContentGeneration
              toolId={tool.id}
              toolName={tool.name}
              existingContent={tool.generated_content}
              onGenerationComplete={handlePartialGenerationComplete}
              onError={handleError}
            />
          )}
        </div>

        {/* Content Editor Modal */}
        {showContentEditor && tool.generated_content && (
          <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
            <div className="bg-zinc-900 rounded-lg max-w-4xl w-full max-h-[90vh] overflow-hidden">
              <ContentOverrideEditor
                toolId={tool.id}
                toolName={tool.name}
                generatedContent={tool.generated_content}
                onSave={handleContentSave}
                onCancel={() => setShowContentEditor(false)}
              />
            </div>
          </div>
        )}

        {/* Error Display */}
        {error && (
          <div className="bg-red-900/20 border border-red-500 rounded-lg p-4">
            <div className="flex items-center space-x-2">
              <svg className="w-5 h-5 text-red-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
              </svg>
              <span className="text-red-400 text-sm">{error}</span>
            </div>
          </div>
        )}
    </div>
  );
}
