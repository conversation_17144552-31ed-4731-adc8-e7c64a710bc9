'use client';

import { useState, useEffect } from 'react';

interface ResumeTool {
  id: string;
  name: string;
  website: string;
  description: string;
  hasScrapedData: boolean;
  aiGenerationStatus: string;
  lastScrapedAt: string;
  scrapedDataSize: number;
}

interface ResumeToolSelectorProps {
  onToolsSelected: (toolIds: string[]) => void;
  selectedTools: string[];
}

/**
 * Resume Tool Selector Component
 * 
 * Allows users to select tools that have scraped data but need AI content generation
 */
export function ResumeToolSelector({ onToolsSelected, selectedTools }: ResumeToolSelectorProps) {
  const [tools, setTools] = useState<ResumeTool[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [searchTerm, setSearchTerm] = useState('');
  const [selectAll, setSelectAll] = useState(false);

  useEffect(() => {
    fetchResumeTools();
  }, []);

  const fetchResumeTools = async () => {
    try {
      setLoading(true);
      setError(null);

      const response = await fetch('/api/admin/bulk-processing/resume-tools', {
        headers: {
          'x-admin-api-key': process.env.NEXT_PUBLIC_ADMIN_API_KEY || 'aidude_admin_2024_secure_key_xyz789',
        },
      });

      if (!response.ok) {
        throw new Error('Failed to fetch resume tools');
      }

      const data = await response.json();
      if (data.success) {
        setTools(data.data.tools);
      } else {
        throw new Error(data.error || 'Failed to fetch tools');
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Unknown error');
    } finally {
      setLoading(false);
    }
  };

  const filteredTools = tools.filter(tool =>
    tool.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
    tool.website.toLowerCase().includes(searchTerm.toLowerCase())
  );

  const handleToolToggle = (toolId: string) => {
    const newSelection = selectedTools.includes(toolId)
      ? selectedTools.filter(id => id !== toolId)
      : [...selectedTools, toolId];
    
    onToolsSelected(newSelection);
  };

  const handleSelectAll = () => {
    if (selectAll) {
      onToolsSelected([]);
    } else {
      onToolsSelected(filteredTools.map(tool => tool.id));
    }
    setSelectAll(!selectAll);
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString();
  };

  const formatDataSize = (bytes: number) => {
    if (bytes < 1024) return `${bytes} B`;
    if (bytes < 1024 * 1024) return `${(bytes / 1024).toFixed(1)} KB`;
    return `${(bytes / (1024 * 1024)).toFixed(1)} MB`;
  };

  if (loading) {
    return (
      <div className="bg-zinc-800 rounded-lg p-6">
        <div className="flex items-center justify-center space-x-2">
          <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-orange-500"></div>
          <span className="text-white">Loading tools...</span>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="bg-red-900/20 border border-red-500/50 rounded-lg p-4">
        <div className="flex items-center space-x-2">
          <span className="text-red-400">⚠️</span>
          <span className="text-red-400">{error}</span>
          <button
            onClick={fetchResumeTools}
            className="ml-auto bg-red-600 hover:bg-red-700 px-3 py-1 rounded text-sm transition-colors"
          >
            Retry
          </button>
        </div>
      </div>
    );
  }

  return (
    <div className="bg-zinc-800 rounded-lg p-6">
      <div className="flex justify-between items-center mb-4">
        <h3 className="text-lg font-medium text-white">
          Select Tools for AI Content Generation
        </h3>
        <div className="text-sm text-gray-400">
          {selectedTools.length} of {filteredTools.length} selected
        </div>
      </div>

      {/* Search and Controls */}
      <div className="flex space-x-4 mb-4">
        <div className="flex-1">
          <input
            type="text"
            placeholder="Search tools..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            className="w-full bg-zinc-700 border border-zinc-600 rounded-lg px-3 py-2 text-white placeholder-gray-400 focus:outline-none focus:border-orange-500"
          />
        </div>
        <button
          onClick={handleSelectAll}
          className="bg-zinc-700 hover:bg-zinc-600 px-4 py-2 rounded-lg text-white transition-colors"
        >
          {selectAll ? 'Deselect All' : 'Select All'}
        </button>
      </div>

      {/* Tools List */}
      <div className="max-h-96 overflow-y-auto space-y-2">
        {filteredTools.length === 0 ? (
          <div className="text-center py-8 text-gray-400">
            {searchTerm ? 'No tools match your search.' : 'No tools available for AI content generation.'}
          </div>
        ) : (
          filteredTools.map((tool) => (
            <div
              key={tool.id}
              className={`flex items-center space-x-3 p-3 rounded-lg border transition-colors cursor-pointer ${
                selectedTools.includes(tool.id)
                  ? 'bg-orange-900/20 border-orange-500/50'
                  : 'bg-zinc-700 border-zinc-600 hover:bg-zinc-600'
              }`}
              onClick={() => handleToolToggle(tool.id)}
            >
              <input
                type="checkbox"
                checked={selectedTools.includes(tool.id)}
                onChange={() => handleToolToggle(tool.id)}
                className="rounded border-zinc-600 bg-zinc-700 text-orange-500 focus:ring-orange-500"
              />
              <div className="flex-1 min-w-0">
                <div className="flex items-center space-x-2">
                  <h4 className="font-medium text-white truncate">{tool.name}</h4>
                  <span className={`px-2 py-1 rounded text-xs ${
                    tool.aiGenerationStatus === 'failed' 
                      ? 'bg-red-900/20 text-red-400' 
                      : 'bg-yellow-900/20 text-yellow-400'
                  }`}>
                    {tool.aiGenerationStatus}
                  </span>
                </div>
                <div className="text-sm text-gray-400 truncate">{tool.website}</div>
                <div className="flex items-center space-x-4 text-xs text-gray-500 mt-1">
                  <span>Scraped: {formatDate(tool.lastScrapedAt)}</span>
                  <span>Data: {formatDataSize(tool.scrapedDataSize)}</span>
                </div>
              </div>
            </div>
          ))
        )}
      </div>

      {/* Summary */}
      {selectedTools.length > 0 && (
        <div className="mt-4 p-3 bg-zinc-700 rounded-lg">
          <div className="text-sm text-gray-300">
            <strong>{selectedTools.length}</strong> tools selected for AI content generation
          </div>
        </div>
      )}
    </div>
  );
}
