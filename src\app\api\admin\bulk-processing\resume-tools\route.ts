/**
 * Resume Tools API Route
 * Fetches tools that have scraped data but need AI content generation
 */

import { NextRequest, NextResponse } from 'next/server';
import { createClient } from '@supabase/supabase-js';

// Admin API key validation
function validateAdminApiKey(request: NextRequest): boolean {
  const apiKey = request.headers.get('x-admin-api-key');
  const expectedKey = process.env.ADMIN_API_KEY || 'aidude_admin_2024_secure_key_xyz789';
  return apiKey === expectedKey;
}

// Initialize Supabase client
const supabase = createClient(
  process.env.NEXT_PUBLIC_SUPABASE_URL!,
  process.env.SUPABASE_SERVICE_ROLE_KEY!
);

/**
 * GET /api/admin/bulk-processing/resume-tools
 * Get tools that have scraped data but need AI content generation
 */
export async function GET(request: NextRequest) {
  try {
    // Validate admin access
    if (!validateAdminApiKey(request)) {
      return NextResponse.json(
        { success: false, error: 'Unauthorized' },
        { status: 401 }
      );
    }

    const { searchParams } = new URL(request.url);
    const limit = parseInt(searchParams.get('limit') || '50');
    const offset = parseInt(searchParams.get('offset') || '0');

    // Query tools that have scraped data but need AI generation
    const { data: tools, error } = await supabase
      .from('tools')
      .select(`
        id,
        name,
        website,
        description,
        scraped_data,
        ai_generation_status,
        last_scraped_at,
        created_at
      `)
      .not('scraped_data', 'is', null) // Has scraped data
      .in('ai_generation_status', ['pending', 'failed']) // Needs AI generation
      .order('last_scraped_at', { ascending: false })
      .range(offset, offset + limit - 1);

    if (error) {
      console.error('Error fetching resume tools:', error);
      return NextResponse.json(
        { success: false, error: 'Failed to fetch tools' },
        { status: 500 }
      );
    }

    // Get total count for pagination
    const { count, error: countError } = await supabase
      .from('tools')
      .select('*', { count: 'exact', head: true })
      .not('scraped_data', 'is', null)
      .in('ai_generation_status', ['pending', 'failed']);

    if (countError) {
      console.error('Error getting count:', countError);
    }

    // Transform tools data for frontend
    const transformedTools = tools?.map(tool => ({
      id: tool.id,
      name: tool.name,
      website: tool.website,
      description: tool.description,
      hasScrapedData: !!tool.scraped_data,
      aiGenerationStatus: tool.ai_generation_status,
      lastScrapedAt: tool.last_scraped_at,
      createdAt: tool.created_at,
      scrapedDataSize: tool.scraped_data ? JSON.stringify(tool.scraped_data).length : 0,
    })) || [];

    return NextResponse.json({
      success: true,
      data: {
        tools: transformedTools,
        pagination: {
          limit,
          offset,
          total: count || 0,
          hasMore: (count || 0) > offset + limit,
        },
        summary: {
          totalToolsWithScrapedData: count || 0,
          toolsReturned: transformedTools.length,
        },
      },
    });
  } catch (error) {
    console.error('Failed to get resume tools:', error);
    return NextResponse.json(
      { success: false, error: 'Failed to get resume tools' },
      { status: 500 }
    );
  }
}

/**
 * POST /api/admin/bulk-processing/resume-tools
 * Create bulk processing job for selected tools that need AI generation
 */
export async function POST(request: NextRequest) {
  try {
    // Validate admin access
    if (!validateAdminApiKey(request)) {
      return NextResponse.json(
        { success: false, error: 'Unauthorized' },
        { status: 401 }
      );
    }

    const body = await request.json();
    const { toolIds, options } = body;

    if (!toolIds || !Array.isArray(toolIds) || toolIds.length === 0) {
      return NextResponse.json(
        { success: false, error: 'toolIds array is required' },
        { status: 400 }
      );
    }

    // Fetch the selected tools with their scraped data
    const { data: tools, error } = await supabase
      .from('tools')
      .select(`
        id,
        name,
        website,
        description,
        scraped_data,
        ai_generation_status
      `)
      .in('id', toolIds)
      .not('scraped_data', 'is', null);

    if (error) {
      console.error('Error fetching selected tools:', error);
      return NextResponse.json(
        { success: false, error: 'Failed to fetch selected tools' },
        { status: 500 }
      );
    }

    if (!tools || tools.length === 0) {
      return NextResponse.json(
        { success: false, error: 'No valid tools found with scraped data' },
        { status: 400 }
      );
    }

    // Convert tools to URLs for bulk processing
    const urls = tools.map(tool => tool.website).filter(Boolean);

    if (urls.length === 0) {
      return NextResponse.json(
        { success: false, error: 'No valid URLs found in selected tools' },
        { status: 400 }
      );
    }

    // Create bulk processing job with resume generation mode
    const { getBulkProcessingEngine } = await import('@/lib/bulk-processing/bulk-engine');
    const { ManualEntryProcessor } = await import('@/lib/bulk-processing/file-processors');
    
    const processor = new ManualEntryProcessor();
    const processingResult = processor.processInput(urls.join('\n'));

    if (!processingResult.success || !processingResult.data) {
      return NextResponse.json(
        { success: false, error: 'Failed to process tool URLs' },
        { status: 400 }
      );
    }

    // Set processing options for resume generation
    const processingOptions = {
      batchSize: options?.batchSize || 5,
      delayBetweenBatches: options?.delayBetweenBatches || 2000,
      retryAttempts: options?.retryAttempts || 3,
      aiProvider: options?.aiProvider || 'openai',
      skipExisting: false, // Don't skip existing since we want to regenerate
      scrapeOnly: false,
      generateContent: true,
      resumeGeneration: true, // This is the key flag
      autoPublish: options?.autoPublish || false,
      priority: options?.priority || 'normal',
    };

    const bulkEngine = getBulkProcessingEngine();
    const bulkJob = await bulkEngine.createBulkJob(
      processingResult.data.validItems,
      processingOptions,
      {
        jobType: 'manual_entry',
        submittedBy: 'admin',
        selectedToolIds: toolIds,
        metadata: {
          resumeMode: true,
          source: 'resume_generation',
          timestamp: new Date().toISOString()
        }
      }
    );

    return NextResponse.json({
      success: true,
      data: {
        job: bulkJob,
        selectedTools: {
          totalSelected: toolIds.length,
          validTools: tools.length,
          urlsToProcess: urls.length,
        },
        processing: {
          totalItems: processingResult.data.totalItems,
          validItems: processingResult.data.validItems.length,
        },
      },
    });
  } catch (error) {
    console.error('Failed to create resume generation job:', error);
    return NextResponse.json(
      { success: false, error: 'Failed to create resume generation job' },
      { status: 500 }
    );
  }
}
