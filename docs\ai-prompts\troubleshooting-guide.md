# AI Dude Prompt Template System - Troubleshooting Guide

## Overview

This guide provides solutions for common issues with the AI Dude prompt template system, including template loading problems, variable substitution errors, and fallback mechanisms.

## Common Issues and Solutions

### 1. Template Loading Issues

#### Issue: "Failed to load AI Dude template from database"

**Symptoms:**
- Console warnings about template loading failures
- Fallback templates being used instead of database templates
- Missing or incorrect template content

**Causes:**
- Database connection issues
- Missing templates in `system_configuration` table
- Incorrect template keys
- Invalid JSON in template configuration

**Solutions:**

1. **Verify Database Connection:**
   ```bash
   # Check environment variables
   echo $NEXT_PUBLIC_SUPABASE_URL
   echo $SUPABASE_SERVICE_ROLE_KEY
   ```

2. **Check Template Existence:**
   ```sql
   -- Verify all 11 templates exist
   SELECT config_key, config_value->>'name' as name, config_value->>'promptType' as type
   FROM system_configuration
   WHERE config_key LIKE 'prompt_ai_dude%'
   ORDER BY config_key;
   ```

3. **Validate Template JSON:**
   ```sql
   -- Check for JSON syntax errors
   SELECT config_key, 
          CASE WHEN config_value::json IS NOT NULL THEN 'Valid' ELSE 'Invalid' END as json_status
   FROM system_configuration
   WHERE config_key LIKE 'prompt_ai_dude%';
   ```

4. **Re-install Templates:**
   ```sql
   -- Execute the corrected SQL file
   \i sql/ai-dude-prompt-templates.sql
   ```

#### Issue: "Template not found for section type"

**Symptoms:**
- Partial generation fails for specific sections
- Error messages about missing section templates

**Solutions:**

1. **Check Section Template Keys:**
   ```sql
   -- Verify section-specific templates exist
   SELECT config_key FROM system_configuration 
   WHERE config_key IN (
     'prompt_ai_dude_features',
     'prompt_ai_dude_pricing', 
     'prompt_ai_dude_pros_cons',
     'prompt_ai_dude_seo',
     'prompt_ai_dude_faqs',
     'prompt_ai_dude_releases'
   );
   ```

2. **Verify Section Type Mapping:**
   ```typescript
   // Check that section types match template keys
   const validSectionTypes = [
     'features', 'pricing', 'pros_cons', 
     'seo', 'faqs', 'releases'
   ];
   ```

### 2. Variable Substitution Errors

#### Issue: Variables not being replaced in templates

**Symptoms:**
- Template output contains `{variableName}` placeholders
- AI generation fails due to invalid prompts
- Missing data in generated content

**Solutions:**

1. **Check Variable Names:**
   ```typescript
   // Ensure variable names match exactly
   const systemVariables = ['DATABASE_SCHEMA', 'sectionType', 'sectionRequirements'];
   const userVariables = ['toolUrl', 'scrapedContent', 'existingToolData'];
   ```

2. **Verify Variable Values:**
   ```typescript
   // Debug variable substitution
   console.log('Variables before substitution:', {
     toolUrl,
     scrapedContent: scrapedContent?.substring(0, 100),
     existingToolData: Object.keys(existingToolData || {})
   });
   ```

3. **Check Template Structure:**
   ```sql
   -- Verify template contains expected variables
   SELECT config_key, config_value->>'variables' as variables
   FROM system_configuration
   WHERE config_key LIKE 'prompt_ai_dude%';
   ```

### 3. System/User Prompt Separation Issues

#### Issue: Prompts not properly separated

**Symptoms:**
- AI Dude methodology appearing in user prompts
- Raw input data appearing in system prompts
- Inconsistent content generation results

**Solutions:**

1. **Verify Prompt Types:**
   ```sql
   -- Check prompt type distribution
   SELECT config_value->>'promptType' as type, COUNT(*) as count
   FROM system_configuration
   WHERE config_key LIKE 'prompt_ai_dude%'
   GROUP BY config_value->>'promptType';
   -- Expected: system: 3, user: 8
   ```

2. **Check Template Content:**
   ```typescript
   // System prompts should contain methodology
   const systemPrompt = await PromptManager.buildAIDudeSystemPrompt(schema);
   console.log('System contains AI Dude:', systemPrompt.includes('AI Dude'));
   
   // User prompts should be minimal
   const userPrompt = await PromptManager.buildAIDudeUserPrompt(content, url);
   console.log('User is minimal:', userPrompt.length < 1000);
   ```

### 4. Performance Issues

#### Issue: Slow template loading

**Symptoms:**
- Delayed content generation
- Timeout errors during template loading
- High database query times

**Solutions:**

1. **Enable Template Caching:**
   ```typescript
   // Add caching to PromptManager
   private static templateCache = new Map<string, any>();
   
   static async getTemplate(key: string) {
     if (this.templateCache.has(key)) {
       return this.templateCache.get(key);
     }
     
     const template = await this.loadFromDatabase(key);
     this.templateCache.set(key, template);
     return template;
   }
   ```

2. **Optimize Database Queries:**
   ```sql
   -- Create index for faster template lookups
   CREATE INDEX IF NOT EXISTS idx_system_config_ai_dude 
   ON system_configuration(config_key) 
   WHERE config_key LIKE 'prompt_ai_dude%';
   ```

### 5. Admin Panel Integration Issues

#### Issue: Admin panel shows incorrect template counts or null promptType

**Symptoms:**
- Admin panel reports 0 system prompts and 0 user prompts
- Template management interface shows null values for promptType
- Admin content generation triggers fail

**Cause:**
- PostgreSQL JSON path operator syntax issues with `config_value->promptType`
- Column name aliasing problems in Supabase queries

**Solution:**

1. **Use Direct JavaScript Access:**
   ```typescript
   // WRONG - PostgreSQL JSON path operator
   const { data } = await supabase
     .select('config_key, config_value->promptType as prompt_type')
     .from('system_configuration');

   // CORRECT - Direct JavaScript object access
   const { data } = await supabase
     .select('config_key, config_value')
     .from('system_configuration');

   const systemPrompts = data.filter(t => t.config_value?.promptType === 'system');
   const userPrompts = data.filter(t => t.config_value?.promptType === 'user');
   ```

2. **Update Admin Panel Queries:**
   ```typescript
   // Fixed admin panel template classification
   const classifyTemplates = (templates) => {
     return {
       system: templates.filter(t => t.config_value?.promptType === 'system'),
       user: templates.filter(t => t.config_value?.promptType === 'user'),
       total: templates.length
     };
   };
   ```

3. **Verify Template Structure:**
   ```sql
   -- Check that promptType exists in JSON
   SELECT config_key,
          config_value->>'name' as name,
          config_value->>'promptType' as prompt_type
   FROM system_configuration
   WHERE config_key LIKE 'prompt_ai_dude%';
   ```

**Expected Results:**
- 3 system prompts: `prompt_ai_dude_complete_system`, `prompt_ai_dude_partial_system`, `prompt_ai_dude_validation`
- 8 user prompts: `prompt_ai_dude_complete_user`, `prompt_ai_dude_partial_context`, `prompt_ai_dude_features`, `prompt_ai_dude_pricing`, `prompt_ai_dude_pros_cons`, `prompt_ai_dude_seo`, `prompt_ai_dude_faqs`, `prompt_ai_dude_releases`

### 6. Content Generation Failures

#### Issue: AI generation returns invalid JSON

**Symptoms:**
- JSON parsing errors
- Malformed content structure
- Missing required fields

**Solutions:**

1. **Check AI Provider Response:**
   ```typescript
   // Add response validation
   try {
     const parsedContent = JSON.parse(response.content);
     console.log('Valid JSON received');
   } catch (error) {
     console.error('Invalid JSON from AI provider:', error);
     // Use fallback or retry
   }
   ```

2. **Verify Schema Injection:**
   ```typescript
   // Ensure schema is properly injected
   const systemPrompt = await PromptManager.buildAIDudeSystemPrompt(schema);
   console.log('Schema injected:', systemPrompt.includes('"name"'));
   ```

## Fallback Mechanisms

### 1. Template Fallbacks

When database templates fail to load, the system uses hardcoded fallback templates:

```typescript
// System prompt fallback
const fallbackSystemPrompt = `You are AI Dude, the irreverent, no-BS curator of AI tools...`;

// User prompt fallback  
const fallbackUserPrompt = `Tool URL: ${toolUrl}\n\nScraped Content:\n${scrapedContent}`;
```

### 2. Error Recovery

```typescript
// Graceful error handling
try {
  const template = await loadFromDatabase(key);
  return template;
} catch (error) {
  console.warn(`Template loading failed, using fallback: ${error.message}`);
  return getFallbackTemplate(key);
}
```

## Monitoring and Diagnostics

### 1. Template Usage Tracking

```sql
-- Monitor template usage
SELECT config_key, 
       config_value->>'usage' as usage_count,
       updated_at
FROM system_configuration
WHERE config_key LIKE 'prompt_ai_dude%'
ORDER BY (config_value->>'usage')::int DESC;
```

### 2. Error Logging

```typescript
// Enhanced error logging
log.ai('template-loading-error', `Failed to load template: ${templateKey}`, {
  templateKey,
  error: error.message,
  fallbackUsed: true,
  timestamp: new Date().toISOString()
});
```

### 3. Health Check Endpoint

```typescript
// GET /api/admin/ai-dude/health
export async function GET() {
  const healthCheck = {
    templatesLoaded: 0,
    templateErrors: [],
    systemStatus: 'healthy'
  };

  // Check all templates
  for (const templateKey of expectedTemplateKeys) {
    try {
      await PromptManager.loadTemplate(templateKey);
      healthCheck.templatesLoaded++;
    } catch (error) {
      healthCheck.templateErrors.push({
        template: templateKey,
        error: error.message
      });
    }
  }

  if (healthCheck.templateErrors.length > 0) {
    healthCheck.systemStatus = 'degraded';
  }

  return NextResponse.json(healthCheck);
}
```

## Testing and Validation

### 1. Template Validation Script

```bash
# Run template validation
node scripts/validate-ai-dude-templates.js
```

### 2. Integration Testing

```typescript
// Test complete workflow
const testResult = await testAIDudeWorkflow({
  toolUrl: 'https://example.com',
  scrapedContent: 'Test content...',
  sectionType: 'features'
});

console.log('Workflow test result:', testResult.success);
```

## Quick Reference

### Expected Template Count
- **Total**: 11 templates
- **System**: 3 templates
- **User**: 8 templates

### Template Keys
```
prompt_ai_dude_complete_system
prompt_ai_dude_complete_user
prompt_ai_dude_partial_system
prompt_ai_dude_partial_context
prompt_ai_dude_features
prompt_ai_dude_pricing
prompt_ai_dude_pros_cons
prompt_ai_dude_seo
prompt_ai_dude_faqs
prompt_ai_dude_releases
prompt_ai_dude_validation
```

### Emergency Recovery

If the system is completely broken:

1. **Reset Templates:**
   ```sql
   DELETE FROM system_configuration WHERE config_key LIKE 'prompt_ai_dude%';
   \i sql/ai-dude-prompt-templates.sql
   ```

2. **Restart Application:**
   ```bash
   npm run dev
   ```

3. **Verify System Health:**
   ```bash
   curl http://localhost:3000/api/admin/ai-dude/health
   ```

---

*This troubleshooting guide covers the most common issues with the AI Dude prompt template system. For additional support, check the implementation logs and database status.*
