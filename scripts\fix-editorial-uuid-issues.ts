/**
 * Fix Editorial UUID Issues Script
 * 
 * This script addresses the specific UUID parsing issues seen in the logs:
 * - Invalid UUID format: "ai_job_7bd7b798-f0b8-4946-a96c-6aea14e0678d"
 * - Cleans up malformed job IDs and editorial review data
 * 
 * Usage: npx tsx scripts/fix-editorial-uuid-issues.ts
 */

import { supabase } from '../src/lib/supabase';

interface FixStats {
  malformedJobsFound: number;
  malformedJobsFixed: number;
  editorialReviewsDeleted: number;
  aiJobsDeleted: number;
  toolsUpdated: number;
  errors: string[];
}

async function fixEditorialUuidIssues(): Promise<FixStats> {
  const stats: FixStats = {
    malformedJobsFound: 0,
    malformedJobsFixed: 0,
    editorialReviewsDeleted: 0,
    aiJobsDeleted: 0,
    toolsUpdated: 0,
    errors: []
  };

  try {
    console.log('🔧 Starting UUID issues fix...\n');

    // 1. Find AI generation jobs with malformed IDs
    console.log('1️⃣ Checking for malformed AI job IDs...');
    const { data: allJobs, error: jobsError } = await supabase
      .from('ai_generation_jobs')
      .select('id, tool_id, status, job_type, created_at');

    if (jobsError) {
      stats.errors.push(`Failed to fetch AI jobs: ${jobsError.message}`);
      console.error('❌ Error fetching AI jobs:', jobsError);
      return stats;
    }

    console.log(`📊 Found ${allJobs?.length || 0} AI generation jobs`);

    // Check for jobs with malformed IDs (containing "ai_job_" prefix)
    const malformedJobs = allJobs?.filter(job => 
      job.id.includes('ai_job_ai_job_') || 
      job.id.startsWith('ai_job_') && job.id.length > 36
    ) || [];

    stats.malformedJobsFound = malformedJobs.length;
    console.log(`⚠️  Found ${malformedJobs.length} jobs with malformed IDs`);

    if (malformedJobs.length > 0) {
      console.log('📋 Malformed job IDs:');
      malformedJobs.forEach((job, index) => {
        console.log(`  ${index + 1}. ${job.id} (tool: ${job.tool_id})`);
      });
    }

    // 2. Delete malformed AI jobs
    if (malformedJobs.length > 0) {
      console.log('\n2️⃣ Deleting malformed AI jobs...');
      
      for (const job of malformedJobs) {
        try {
          const { error: deleteError } = await supabase
            .from('ai_generation_jobs')
            .delete()
            .eq('id', job.id);

          if (deleteError) {
            stats.errors.push(`Failed to delete job ${job.id}: ${deleteError.message}`);
            console.error(`❌ Failed to delete job ${job.id}:`, deleteError);
          } else {
            stats.aiJobsDeleted++;
            console.log(`✅ Deleted malformed job: ${job.id}`);
          }
        } catch (error: any) {
          stats.errors.push(`Exception deleting job ${job.id}: ${error.message}`);
          console.error(`💥 Exception deleting job ${job.id}:`, error);
        }
      }
    }

    // 3. Clear all editorial reviews (they might have references to malformed jobs)
    console.log('\n3️⃣ Clearing all editorial reviews...');
    const { count: reviewsDeleted, error: reviewsError } = await supabase
      .from('editorial_reviews')
      .delete()
      .neq('id', '00000000-0000-0000-0000-000000000000')
      .select('*', { count: 'exact', head: true });

    if (reviewsError) {
      stats.errors.push(`Failed to delete editorial reviews: ${reviewsError.message}`);
      console.error('❌ Error deleting editorial reviews:', reviewsError);
    } else {
      stats.editorialReviewsDeleted = reviewsDeleted || 0;
      console.log(`✅ Deleted ${stats.editorialReviewsDeleted} editorial reviews`);
    }

    // 4. Clear editorial_review_id and ai_generation_job_id references in tools
    console.log('\n4️⃣ Clearing tool references...');
    const { count: toolsUpdated, error: toolsError } = await supabase
      .from('tools')
      .update({ 
        editorial_review_id: null,
        ai_generation_job_id: null 
      })
      .or('editorial_review_id.not.is.null,ai_generation_job_id.not.is.null')
      .select('*', { count: 'exact', head: true });

    if (toolsError) {
      stats.errors.push(`Failed to update tools: ${toolsError.message}`);
      console.error('❌ Error updating tools:', toolsError);
    } else {
      stats.toolsUpdated = toolsUpdated || 0;
      console.log(`✅ Updated ${stats.toolsUpdated} tools (cleared references)`);
    }

    // 5. Verify cleanup
    console.log('\n5️⃣ Verifying cleanup...');
    
    // Check remaining AI jobs
    const { count: remainingJobs, error: jobCountError } = await supabase
      .from('ai_generation_jobs')
      .select('*', { count: 'exact', head: true });

    if (jobCountError) {
      stats.errors.push(`Failed to count remaining jobs: ${jobCountError.message}`);
    } else {
      console.log(`📊 Remaining AI jobs: ${remainingJobs || 0}`);
    }

    // Check remaining editorial reviews
    const { count: remainingReviews, error: reviewCountError } = await supabase
      .from('editorial_reviews')
      .select('*', { count: 'exact', head: true });

    if (reviewCountError) {
      stats.errors.push(`Failed to count remaining reviews: ${reviewCountError.message}`);
    } else {
      console.log(`📊 Remaining editorial reviews: ${remainingReviews || 0}`);
    }

    // Check for any remaining malformed references
    const { data: remainingMalformed, error: malformedError } = await supabase
      .from('ai_generation_jobs')
      .select('id')
      .or('id.like.%ai_job_ai_job_%,id.like.ai_job_%');

    if (malformedError) {
      stats.errors.push(`Failed to check remaining malformed jobs: ${malformedError.message}`);
    } else {
      const stillMalformed = remainingMalformed?.filter(job => 
        job.id.includes('ai_job_ai_job_') || 
        (job.id.startsWith('ai_job_') && job.id.length > 36)
      ) || [];

      if (stillMalformed.length > 0) {
        console.log(`⚠️  ${stillMalformed.length} malformed jobs still remain`);
        stillMalformed.forEach((job, index) => {
          console.log(`  ${index + 1}. ${job.id}`);
        });
      } else {
        console.log('✅ No malformed job IDs remain');
      }
    }

    return stats;

  } catch (error: any) {
    stats.errors.push(`Unexpected error: ${error.message}`);
    console.error('❌ Unexpected error:', error);
    return stats;
  }
}

async function main() {
  console.log('🚀 Editorial UUID Issues Fix Script');
  console.log('===================================\n');

  console.log('🎯 This script will:');
  console.log('  • Find and delete AI jobs with malformed UUIDs');
  console.log('  • Clear all editorial reviews');
  console.log('  • Clear tool references to deleted records');
  console.log('  • Verify cleanup completion\n');

  console.log('⚠️  WARNING: This will delete data and cannot be undone!\n');

  const stats = await fixEditorialUuidIssues();

  console.log('\n📊 FIX SUMMARY');
  console.log('===============');
  console.log(`Malformed Jobs Found: ${stats.malformedJobsFound}`);
  console.log(`AI Jobs Deleted: ${stats.aiJobsDeleted}`);
  console.log(`Editorial Reviews Deleted: ${stats.editorialReviewsDeleted}`);
  console.log(`Tools Updated: ${stats.toolsUpdated}`);
  console.log(`Errors: ${stats.errors.length}`);

  if (stats.errors.length > 0) {
    console.log('\n❌ ERRORS:');
    stats.errors.forEach((error, index) => {
      console.log(`  ${index + 1}. ${error}`);
    });
  }

  if (stats.errors.length === 0) {
    console.log('\n✅ UUID issues fix completed successfully!');
    console.log('🎉 The editorial system should now work without UUID parsing errors.');
  } else {
    console.log('\n⚠️  Fix completed with errors. Please review the issues above.');
  }

  process.exit(stats.errors.length === 0 ? 0 : 1);
}

// Run the script
if (require.main === module) {
  main().catch((error) => {
    console.error('💥 Script failed:', error);
    process.exit(1);
  });
}

export { fixEditorialUuidIssues };
