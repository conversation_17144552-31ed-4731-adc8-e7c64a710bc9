#!/usr/bin/env tsx

// Test transformation logic without database dependencies

interface ToolSubmission {
  id: string;
  name: string;
  url: string;
  description: string;
  detailed_description?: string;
  category: string;
  subcategory?: string;
  features?: string;
  pricing_type?: string;
  pricing_details?: string;
  pros?: string;
  cons?: string;
  meta_title?: string;
  meta_description?: string;
  faq?: string;
  submitter_name?: string;
  submitter_email: string;
  submission_type: 'simple' | 'detailed';
  status: string;
}

class TestSubmissionTransformer {
  private generateSlug(name: string): string {
    return name
      .toLowerCase()
      .replace(/[^a-z0-9]+/g, '-')
      .replace(/^-+|-+$/g, '');
  }

  private transformFAQ(faqText?: string): any {
    if (!faqText) return null;

    const faqItems = [];
    const lines = faqText.split('\n').filter(line => line.trim());
    
    let currentQ = '';
    let currentA = '';
    
    for (const line of lines) {
      if (line.trim().startsWith('Q:')) {
        if (currentQ && currentA) {
          faqItems.push({ question: currentQ, answer: currentA });
        }
        currentQ = line.replace(/^Q:\s*/, '').trim();
        currentA = '';
      } else if (line.trim().startsWith('A:')) {
        currentA = line.replace(/^A:\s*/, '').trim();
      } else if (currentA) {
        currentA += ' ' + line.trim();
      }
    }
    
    if (currentQ && currentA) {
      faqItems.push({ question: currentQ, answer: currentA });
    }

    return faqItems.length > 0 ? faqItems : null;
  }

  public transformSubmissionToTool(submission: ToolSubmission) {
    const slug = this.generateSlug(submission.name);
    const now = new Date().toISOString();
    
    const contentStatus = submission.submission_type === 'detailed' ? 'published' : 'draft';
    
    return {
      name: submission.name,
      slug: slug,
      link: `/tools/${slug}`,
      description: submission.description,
      short_description: submission.detailed_description || submission.description,
      website: submission.url,
      category_id: submission.category,
      subcategory: submission.subcategory,
      company: submission.submitter_name,
      features: submission.features,
      pricing_type: submission.pricing_type,
      pricing_details: submission.pricing_details,
      pros: submission.pros,
      cons: submission.cons,
      meta_title: submission.meta_title || `${submission.name} - AI Tool Review`,
      meta_description: submission.meta_description || submission.description,
      faqs: this.transformFAQ(submission.faq),
      content_status: contentStatus,
      submission_type: submission.submission_type,
      is_verified: true,
      is_claimed: false,
      created_at: now,
      updated_at: now,
      published_at: contentStatus === 'published' ? now : undefined,
    };
  }
}

function testTransformationLogic() {
  console.log('🧪 Testing Transformation Logic (Unit Tests)...\n');

  const transformer = new TestSubmissionTransformer();
  let passedTests = 0;
  let totalTests = 0;

  // Test 1: Simple Submission Transformation
  console.log('1️⃣ Testing Simple Submission Transformation:');
  totalTests++;
  
  const simpleSubmission: ToolSubmission = {
    id: 'test-1',
    name: 'Test AI Tool',
    url: 'https://test-ai-tool.com',
    description: 'A test AI tool for validation',
    category: 'writing-tools',
    submitter_name: 'Test Company',
    submitter_email: '<EMAIL>',
    submission_type: 'simple',
    status: 'approved'
  };

  const simpleResult = transformer.transformSubmissionToTool(simpleSubmission);
  
  if (simpleResult.content_status === 'draft' && 
      simpleResult.slug === 'test-ai-tool' &&
      simpleResult.link === '/tools/test-ai-tool' &&
      simpleResult.submission_type === 'simple') {
    console.log('   ✅ Simple transformation passed');
    passedTests++;
  } else {
    console.log('   ❌ Simple transformation failed');
    console.log('   Expected: draft status, correct slug');
    console.log('   Got:', { 
      status: simpleResult.content_status, 
      slug: simpleResult.slug,
      type: simpleResult.submission_type 
    });
  }

  // Test 2: Detailed Submission Transformation
  console.log('\n2️⃣ Testing Detailed Submission Transformation:');
  totalTests++;
  
  const detailedSubmission: ToolSubmission = {
    id: 'test-2',
    name: 'Detailed AI Tool',
    url: 'https://detailed-ai-tool.com',
    description: 'A detailed AI tool with all fields',
    detailed_description: 'This is a comprehensive AI tool with detailed information',
    category: 'productivity-ai',
    subcategory: 'Task Automation',
    features: '• Feature 1\n• Feature 2\n• Feature 3',
    pricing_type: 'freemium',
    pricing_details: 'Free: Basic features\nPro: $10/month',
    pros: 'Fast, Reliable, Easy to use',
    cons: 'Limited free tier, Requires signup',
    meta_title: 'Detailed AI Tool - Best Productivity Assistant',
    meta_description: 'Boost productivity with our detailed AI tool',
    faq: 'Q: How does it work?\nA: It uses AI to automate tasks.\n\nQ: Is it free?\nA: We offer a free tier.',
    submitter_name: 'Detailed Company',
    submitter_email: '<EMAIL>',
    submission_type: 'detailed',
    status: 'approved'
  };

  const detailedResult = transformer.transformSubmissionToTool(detailedSubmission);
  
  if (detailedResult.content_status === 'published' && 
      detailedResult.features === '• Feature 1\n• Feature 2\n• Feature 3' &&
      detailedResult.pricing_type === 'freemium' &&
      detailedResult.faqs && Array.isArray(detailedResult.faqs) &&
      detailedResult.published_at) {
    console.log('   ✅ Detailed transformation passed');
    passedTests++;
  } else {
    console.log('   ❌ Detailed transformation failed');
    console.log('   Expected: published status, features, pricing, FAQs');
    console.log('   Got:', { 
      status: detailedResult.content_status,
      features: detailedResult.features ? 'Present' : 'Missing',
      pricing: detailedResult.pricing_type,
      faqs: detailedResult.faqs ? 'Present' : 'Missing'
    });
  }

  // Test 3: FAQ Transformation
  console.log('\n3️⃣ Testing FAQ Transformation:');
  totalTests++;
  
  const faqTest = transformer.transformSubmissionToTool({
    ...simpleSubmission,
    faq: 'Q: What is this?\nA: This is a test tool.\n\nQ: How much does it cost?\nA: It\'s free to try.'
  });

  if (faqTest.faqs && 
      Array.isArray(faqTest.faqs) && 
      faqTest.faqs.length === 2 &&
      faqTest.faqs[0].question === 'What is this?' &&
      faqTest.faqs[0].answer === 'This is a test tool.') {
    console.log('   ✅ FAQ transformation passed');
    passedTests++;
  } else {
    console.log('   ❌ FAQ transformation failed');
    console.log('   Expected: 2 FAQ items with correct structure');
    console.log('   Got:', faqTest.faqs);
  }

  // Test 4: Slug Generation
  console.log('\n4️⃣ Testing Slug Generation:');
  totalTests++;
  
  const slugTests = [
    { input: 'My AI Tool!', expected: 'my-ai-tool' },
    { input: 'Tool with Spaces & Symbols', expected: 'tool-with-spaces-symbols' },
    { input: 'GPT-4 Assistant', expected: 'gpt-4-assistant' }
  ];

  let slugTestsPassed = 0;
  slugTests.forEach(test => {
    const result = transformer.transformSubmissionToTool({
      ...simpleSubmission,
      name: test.input
    });
    
    if (result.slug === test.expected) {
      slugTestsPassed++;
    } else {
      console.log(`   ❌ Slug test failed: "${test.input}" → "${result.slug}" (expected "${test.expected}")`);
    }
  });

  if (slugTestsPassed === slugTests.length) {
    console.log('   ✅ Slug generation passed');
    passedTests++;
  } else {
    console.log(`   ❌ Slug generation failed: ${slugTestsPassed}/${slugTests.length} tests passed`);
  }

  // Test 5: Field Mapping Completeness
  console.log('\n5️⃣ Testing Field Mapping Completeness:');
  totalTests++;
  
  const completeResult = transformer.transformSubmissionToTool(detailedSubmission);
  const requiredFields = [
    'name', 'slug', 'link', 'description', 'website', 'category_id',
    'content_status', 'submission_type', 'is_verified', 'is_claimed',
    'created_at', 'updated_at'
  ];

  const missingFields = requiredFields.filter(field => completeResult[field] === undefined);
  
  if (missingFields.length === 0) {
    console.log('   ✅ Field mapping completeness passed');
    passedTests++;
  } else {
    console.log('   ❌ Field mapping incomplete');
    console.log('   Missing fields:', missingFields);
  }

  // Results Summary
  console.log('\n📊 Test Results Summary:');
  const successRate = Math.round((passedTests / totalTests) * 100);
  console.log(`   Tests Passed: ${passedTests}/${totalTests} (${successRate}%)`);

  if (successRate === 100) {
    console.log('   🎉 All transformation tests passed!');
    return true;
  } else if (successRate >= 80) {
    console.log('   ⚠️  Most tests passed, minor issues detected');
    return false;
  } else {
    console.log('   ❌ Multiple test failures - transformation logic needs fixes');
    return false;
  }
}

// Run the tests
const success = testTransformationLogic();

console.log('\n🎉 Transformation logic test complete!');
process.exit(success ? 0 : 1);
