/**
 * @jest-environment node
 */

import { VersionManager } from '@/lib/versioning/version-manager';
import { CreateVersionRequest, RollbackRequest } from '@/lib/types/versioning';
import { mockSupabaseResponse } from '@/__mocks__/msw-server';

// Mock audit logger and comparator
jest.mock('@/lib/versioning/audit-logger', () => ({
  VersionAuditLogger: jest.fn().mockImplementation(() => ({
    logAction: jest.fn().mockResolvedValue('audit-log-id')
  }))
}));

jest.mock('@/lib/versioning/version-comparator', () => ({
  VersionComparator: jest.fn().mockImplementation(() => ({
    compareVersions: jest.fn()
  }))
}));

describe('VersionManager', () => {
  let versionManager: VersionManager;

  beforeEach(() => {
    versionManager = new VersionManager();
    jest.clearAllMocks();
  });

  describe('createVersion', () => {
    it('should create a new version successfully', async () => {
      const mockTool = {
        id: 'test-tool-id',
        name: 'Test Tool',
        slug: 'test-tool',
        link: '/tools/test-tool'
      };

      const mockVersion = {
        id: 'version-id',
        tool_id: 'test-tool-id',
        version_number: 2,
        version_data: mockTool,
        change_summary: 'Test update',
        created_by: 'admin',
        created_at: '2024-01-01T00:00:00Z',
        is_current: true,
        change_type: 'update',
        change_source: 'admin_panel',
        parent_version_id: 'parent-version-id',
        rollback_reason: null
      };

      // Mock tool fetch
      mockSupabaseResponse('tools', 'GET', mockTool);

      // Mock version creation
      mockSupabaseResponse('tool_versions', 'POST', mockVersion, 201);

      // Mock tool update
      mockSupabaseResponse('tools', 'PATCH', { updated_at: new Date().toISOString() });

      // Mock current version fetch
      mockSupabaseResponse('tool_versions', 'GET', { id: 'parent-version-id' });

      const request: CreateVersionRequest = {
        toolId: 'test-tool-id',
        changeSummary: 'Test update',
        changeType: 'update',
        changeSource: 'admin_panel',
        createdBy: 'admin'
      };

      const result = await versionManager.createVersion(request);

      expect(result.success).toBe(true);
      expect(result.data).toBeDefined();
      expect(result.versionId).toBe('version-id');
      expect(result.performedBy).toBe('admin');
    });

    it('should handle tool not found error', async () => {
      // Mock tool not found
      mockSupabaseResponse('tools', 'GET', {
        error: 'The result contains 0 rows',
        code: 'PGRST116'
      }, 404);

      const request: CreateVersionRequest = {
        toolId: 'non-existent-tool',
        changeSummary: 'Test update',
        createdBy: 'admin'
      };

      const result = await versionManager.createVersion(request);

      expect(result.success).toBe(false);
      expect(result.error).toContain('Tool not found');
    });

    it('should handle version creation failure', async () => {
      const mockTool = {
        id: 'test-tool-id',
        name: 'Test Tool'
      };

      // Mock successful tool fetch
      mockSupabaseResponse('tools', 'GET', mockTool);

      // Mock failed version creation
      mockSupabaseResponse('tool_versions', 'POST', {
        error: 'Insert failed',
        code: '23505'
      }, 400);

      // Mock current version fetch
      mockSupabaseResponse('tool_versions', 'GET', null);

      const request: CreateVersionRequest = {
        toolId: 'test-tool-id',
        changeSummary: 'Test update',
        createdBy: 'admin'
      };

      const result = await versionManager.createVersion(request);

      expect(result.success).toBe(false);
      expect(result.error).toContain('Failed to create version');
    });
  });

  describe('rollbackToVersion', () => {
    it('should rollback to a specific version successfully', async () => {
      const mockTargetVersion = {
        id: 'target-version-id',
        tool_id: 'test-tool-id',
        version_number: 1,
        version_data: { name: 'Old Tool Name' },
        created_at: '2024-01-01T00:00:00Z'
      };

      const mockRollbackVersion = {
        id: 'rollback-version-id',
        tool_id: 'test-tool-id',
        version_number: 3,
        version_data: { name: 'Old Tool Name' },
        change_type: 'rollback',
        created_at: '2024-01-02T00:00:00Z'
      };

      // Mock validation (should be successful)
      jest.spyOn(versionManager, 'validateRollback').mockResolvedValue({
        isValid: true,
        canRollback: true,
        warnings: [],
        errors: [],
        impactAnalysis: {
          affectedFields: [],
          dataLossRisk: 'low',
          dependencyImpact: [],
          estimatedDowntime: 5
        }
      });

      // Mock target version fetch
      mockSupabase.from.mockReturnValueOnce({
        select: jest.fn().mockReturnValue({
          eq: jest.fn().mockReturnValue({
            single: jest.fn().mockResolvedValue({
              data: mockTargetVersion,
              error: null
            })
          })
        })
      });

      // Mock rollback RPC call
      mockSupabase.rpc.mockResolvedValueOnce({
        data: { success: true },
        error: null
      });

      // Mock rollback version creation
      mockSupabase.from.mockReturnValueOnce({
        insert: jest.fn().mockReturnValue({
          select: jest.fn().mockReturnValue({
            single: jest.fn().mockResolvedValue({
              data: mockRollbackVersion,
              error: null
            })
          })
        })
      });

      // Mock tool update
      mockSupabase.from.mockReturnValueOnce({
        update: jest.fn().mockReturnValue({
          eq: jest.fn().mockResolvedValue({
            data: null,
            error: null
          })
        })
      });

      // Mock current version fetch
      mockSupabase.from.mockReturnValueOnce({
        select: jest.fn().mockReturnValue({
          eq: jest.fn().mockReturnValue({
            single: jest.fn().mockResolvedValue({
              data: { id: 'current-version-id' },
              error: null
            })
          })
        })
      });

      const request: RollbackRequest = {
        toolId: 'test-tool-id',
        targetVersionNumber: 1,
        reason: 'Reverting problematic changes',
        performedBy: 'admin'
      };

      const result = await versionManager.rollbackToVersion(request);

      expect(result.success).toBe(true);
      expect(result.data).toBeDefined();
      expect(result.data.rolledBackVersion).toBeDefined();
      expect(result.data.targetVersion).toBeDefined();
    });

    it('should fail rollback when validation fails', async () => {
      // Mock validation failure
      jest.spyOn(versionManager, 'validateRollback').mockResolvedValue({
        isValid: false,
        canRollback: false,
        warnings: [],
        errors: ['Target version not found'],
        impactAnalysis: {
          affectedFields: [],
          dataLossRisk: 'high',
          dependencyImpact: [],
          estimatedDowntime: 0
        }
      });

      const request: RollbackRequest = {
        toolId: 'test-tool-id',
        targetVersionNumber: 999,
        reason: 'Test rollback',
        performedBy: 'admin'
      };

      const result = await versionManager.rollbackToVersion(request);

      expect(result.success).toBe(false);
      expect(result.error).toContain('Rollback validation failed');
    });
  });

  describe('validateRollback', () => {
    it('should validate rollback successfully', async () => {
      const mockTargetVersion = {
        id: 'target-version-id',
        tool_id: 'test-tool-id',
        version_number: 1
      };

      const mockTool = {
        id: 'test-tool-id',
        name: 'Test Tool'
      };

      const mockCurrentVersion = {
        version_number: 2
      };

      // Mock target version fetch
      mockSupabase.from.mockReturnValueOnce({
        select: jest.fn().mockReturnValue({
          eq: jest.fn().mockReturnValue({
            single: jest.fn().mockResolvedValue({
              data: mockTargetVersion,
              error: null
            })
          })
        })
      });

      // Mock tool fetch
      mockSupabase.from.mockReturnValueOnce({
        select: jest.fn().mockReturnValue({
          eq: jest.fn().mockReturnValue({
            single: jest.fn().mockResolvedValue({
              data: mockTool,
              error: null
            })
          })
        })
      });

      // Mock current version fetch
      mockSupabase.from.mockReturnValueOnce({
        select: jest.fn().mockReturnValue({
          eq: jest.fn().mockReturnValue({
            single: jest.fn().mockResolvedValue({
              data: mockCurrentVersion,
              error: null
            })
          })
        })
      });

      const request: RollbackRequest = {
        toolId: 'test-tool-id',
        targetVersionNumber: 1,
        reason: 'Test rollback',
        performedBy: 'admin'
      };

      const validation = await versionManager.validateRollback(request);

      expect(validation.isValid).toBe(true);
      expect(validation.canRollback).toBe(true);
      expect(validation.errors).toHaveLength(0);
    });

    it('should fail validation for non-existent target version', async () => {
      // Mock target version not found
      mockSupabase.from.mockReturnValueOnce({
        select: jest.fn().mockReturnValue({
          eq: jest.fn().mockReturnValue({
            single: jest.fn().mockResolvedValue({
              data: null,
              error: { message: 'Version not found' }
            })
          })
        })
      });

      const request: RollbackRequest = {
        toolId: 'test-tool-id',
        targetVersionNumber: 999,
        reason: 'Test rollback',
        performedBy: 'admin'
      };

      const validation = await versionManager.validateRollback(request);

      expect(validation.isValid).toBe(false);
      expect(validation.canRollback).toBe(false);
      expect(validation.errors).toContain('Target version 999 not found');
    });

    it('should fail validation for rollback to current version', async () => {
      const mockTargetVersion = {
        id: 'target-version-id',
        tool_id: 'test-tool-id',
        version_number: 2
      };

      const mockTool = {
        id: 'test-tool-id',
        name: 'Test Tool'
      };

      const mockCurrentVersion = {
        version_number: 2
      };

      // Mock target version fetch
      mockSupabase.from.mockReturnValueOnce({
        select: jest.fn().mockReturnValue({
          eq: jest.fn().mockReturnValue({
            single: jest.fn().mockResolvedValue({
              data: mockTargetVersion,
              error: null
            })
          })
        })
      });

      // Mock tool fetch
      mockSupabase.from.mockReturnValueOnce({
        select: jest.fn().mockReturnValue({
          eq: jest.fn().mockReturnValue({
            single: jest.fn().mockResolvedValue({
              data: mockTool,
              error: null
            })
          })
        })
      });

      // Mock current version fetch
      mockSupabase.from.mockReturnValueOnce({
        select: jest.fn().mockReturnValue({
          eq: jest.fn().mockReturnValue({
            single: jest.fn().mockResolvedValue({
              data: mockCurrentVersion,
              error: null
            })
          })
        })
      });

      const request: RollbackRequest = {
        toolId: 'test-tool-id',
        targetVersionNumber: 2,
        reason: 'Test rollback',
        performedBy: 'admin'
      };

      const validation = await versionManager.validateRollback(request);

      expect(validation.isValid).toBe(false);
      expect(validation.canRollback).toBe(false);
      expect(validation.errors).toContain('Cannot rollback to current version');
    });
  });

  describe('getVersionHistory', () => {
    it('should fetch version history with pagination', async () => {
      const mockVersions = [
        {
          id: 'version-1',
          tool_id: 'test-tool-id',
          version_number: 2,
          version_data: {},
          created_at: '2024-01-02T00:00:00Z',
          is_current: true,
          change_type: 'update',
          change_source: 'admin_panel'
        },
        {
          id: 'version-2',
          tool_id: 'test-tool-id',
          version_number: 1,
          version_data: {},
          created_at: '2024-01-01T00:00:00Z',
          is_current: false,
          change_type: 'create',
          change_source: 'admin_panel'
        }
      ];

      const mockCurrentVersion = { version_number: 2 };
      const mockVersionStats = [
        { version_number: 1 },
        { version_number: 2 }
      ];

      // Mock version history fetch
      mockSupabase.from.mockReturnValueOnce({
        select: jest.fn().mockReturnValue({
          eq: jest.fn().mockReturnValue({
            order: jest.fn().mockReturnValue({
              range: jest.fn().mockResolvedValue({
                data: mockVersions,
                error: null,
                count: 2
              })
            })
          })
        })
      });

      // Mock current version fetch
      mockSupabase.from.mockReturnValueOnce({
        select: jest.fn().mockReturnValue({
          eq: jest.fn().mockReturnValue({
            single: jest.fn().mockResolvedValue({
              data: mockCurrentVersion,
              error: null
            })
          })
        })
      });

      // Mock version stats fetch
      mockSupabase.from.mockReturnValueOnce({
        select: jest.fn().mockReturnValue({
          eq: jest.fn().mockReturnValue({
            order: jest.fn().mockResolvedValue({
              data: mockVersionStats,
              error: null
            })
          })
        })
      });

      const request = {
        toolId: 'test-tool-id',
        page: 1,
        limit: 20
      };

      const result = await versionManager.getVersionHistory(request);

      expect(result.versions).toHaveLength(2);
      expect(result.pagination.total).toBe(2);
      expect(result.metadata.currentVersion).toBe(2);
      expect(result.metadata.totalVersions).toBe(2);
    });
  });
});
