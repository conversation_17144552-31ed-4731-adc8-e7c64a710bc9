// Submission Transformer Service
// Handles transformation from tool_submissions to tools table

import { supabase } from './supabase';
import { getJobManager } from './jobs/job-manager';
import { JobType, JobPriority } from './jobs/types';
import { MediaAssets } from './media-upload';

interface ToolSubmission {
  id: string;
  name: string;
  url: string;
  description: string;
  detailed_description?: string;
  category: string;
  subcategory?: string;
  features?: string;
  pricing_type?: string;
  pricing_details?: string;
  pros?: string;
  cons?: string;
  meta_title?: string;
  meta_description?: string;
  faq?: string;
  submitter_name?: string;
  submitter_email: string;
  submission_type: 'simple' | 'detailed';
  status: string;
  // Media assets for detailed submissions
  media_assets?: MediaAssets;
}

interface ToolData {
  name: string;
  slug: string;
  link: string;
  description: string;
  short_description?: string;
  detailed_description?: string;
  website: string;
  category_id: string;
  subcategory?: string;
  company?: string;
  features?: string;
  pricing_type?: string;
  pricing_details?: string;
  pros?: string;
  cons?: string;
  meta_title?: string;
  meta_description?: string;
  meta_keywords?: string;
  faqs?: any;
  use_cases?: string;
  target_audience?: string;
  integrations?: string;
  tags?: string[];
  content_status: 'draft' | 'published';
  submission_type: 'simple' | 'detailed';
  is_verified: boolean;
  is_claimed: boolean;
  created_at: string;
  updated_at: string;
  published_at?: string;
  // Submission tracking fields
  submission_id?: string;
  submitted_by?: string;
  submission_date?: string;
  approved_by?: string;
  approved_at?: string;
  editorial_notes?: string;
  // Simplified media fields
  primary_image?: string;
  primary_image_type?: 'screenshot' | 'ogImage';
  logo_url?: string;
  media_source?: string;
  media_updated_at?: string;
}

export class SubmissionTransformer {
  private jobManager = getJobManager();
  /**
   * Generate URL-safe slug from tool name
   */
  private generateSlug(name: string): string {
    return name
      .toLowerCase()
      .replace(/[^a-z0-9]+/g, '-')
      .replace(/^-+|-+$/g, '');
  }

  /**
   * Convert comma-separated tags to array
   */
  private transformTags(tagsText?: string): string[] | null {
    if (!tagsText || !tagsText.trim()) return null;

    return tagsText
      .split(',')
      .map(tag => tag.trim())
      .filter(tag => tag.length > 0)
      .slice(0, 10); // Limit to 10 tags
  }

  /**
   * Convert FAQ text to JSONB array format
   */
  private transformFAQ(faqText?: string): any {
    if (!faqText) return null;

    // Simple transformation: split by Q: and A: patterns
    const faqItems = [];
    const lines = faqText.split('\n').filter(line => line.trim());
    
    let currentQ = '';
    let currentA = '';
    
    for (const line of lines) {
      if (line.trim().startsWith('Q:')) {
        if (currentQ && currentA) {
          faqItems.push({ question: currentQ, answer: currentA });
        }
        currentQ = line.replace(/^Q:\s*/, '').trim();
        currentA = '';
      } else if (line.trim().startsWith('A:')) {
        currentA = line.replace(/^A:\s*/, '').trim();
      } else if (currentA) {
        currentA += ' ' + line.trim();
      }
    }
    
    // Add the last Q&A pair
    if (currentQ && currentA) {
      faqItems.push({ question: currentQ, answer: currentA });
    }

    return faqItems.length > 0 ? faqItems : null;
  }

  /**
   * Transform submission data to tool data with enhanced field mapping
   */
  public transformSubmissionToTool(submission: ToolSubmission, approvedBy?: string): ToolData {
    const slug = this.generateSlug(submission.name);
    const now = new Date().toISOString();

    // Determine content status based on submission type
    const contentStatus = submission.submission_type === 'detailed' ? 'published' : 'draft';

    const toolData: ToolData = {
      // Core fields
      name: submission.name,
      slug: slug,
      link: `/tools/${slug}`,
      description: submission.description,
      short_description: submission.description, // Keep short for listings
      detailed_description: submission.detailed_description,
      website: submission.url,
      category_id: submission.category,
      subcategory: submission.subcategory,
      company: submission.submitter_name,

      // Content fields
      features: submission.features,
      pricing_type: submission.pricing_type,
      pricing_details: submission.pricing_details,
      pros: submission.pros,
      cons: submission.cons,

      // SEO fields
      meta_title: submission.meta_title || `${submission.name} - AI Tool Review`,
      meta_description: submission.meta_description || submission.description,
      meta_keywords: submission.meta_keywords,

      // Enhanced fields from detailed submissions
      use_cases: submission.use_cases,
      target_audience: submission.target_audience,
      integrations: submission.integrations,
      tags: this.transformTags(submission.tags),
      faqs: this.transformFAQ(submission.faq),

      // Status and workflow
      content_status: contentStatus,
      submission_type: submission.submission_type,
      is_verified: true,
      is_claimed: false,

      // Timestamps
      created_at: now,
      updated_at: now,
      published_at: contentStatus === 'published' ? now : undefined,

      // Submission tracking
      submission_id: submission.id,
      submitted_by: submission.submitter_email,
      submission_date: submission.submitted_at,
      approved_by: approvedBy || 'system',
      approved_at: now,

      // Media will be handled separately in storeUserProvidedMedia
      media_source: submission.media_assets ? 'user_provided' : 'auto_collected',
      media_updated_at: now,
    };

    return toolData;
  }

  /**
   * Trigger media collection job for detailed submissions
   */
  private async triggerMediaCollection(toolId: string, url: string): Promise<void> {
    try {
      const job = await this.jobManager.createJob(
        JobType.WEB_SCRAPING,
        {
          url: url,
          toolId: toolId,
          options: {
            extractImages: true,
            extractFavicon: true,
            extractScreenshots: true,
            extractOgImages: true,
            maxScreenshots: 3,
            mediaCollectionOnly: true // Flag to indicate this is for media collection only
          }
        },
        {
          priority: JobPriority.NORMAL
        }
      );

      console.log(`📸 Media collection job ${job.id} created for detailed submission tool ${toolId}`);
    } catch (error) {
      console.error('Failed to trigger media collection:', error);
      throw error;
    }
  }

  /**
   * Check if user provided any media assets
   */
  private hasUserProvidedMedia(mediaAssets: MediaAssets): boolean {
    return !!(
      mediaAssets.primaryImage ||
      mediaAssets.logo
    );
  }

  /**
   * Store user-provided media assets in the tool record with simplified structure
   */
  private async storeUserProvidedMedia(toolId: string, mediaAssets: MediaAssets): Promise<void> {
    try {
      const mediaData = {
        primary_image: mediaAssets.primaryImage || null,
        primary_image_type: mediaAssets.primaryImageType || null,
        logo_url: mediaAssets.logo || null,
        media_source: 'user_provided',
        media_updated_at: new Date().toISOString()
      };

      const { error } = await supabase
        .from('tools')
        .update(mediaData)
        .eq('id', toolId);

      if (error) {
        throw new Error(`Failed to store media: ${error.message}`);
      }

      console.log(`✅ User-provided media stored for tool ${toolId}:`, {
        primaryImage: !!mediaAssets.primaryImage,
        primaryImageType: mediaAssets.primaryImageType,
        logo: !!mediaAssets.logo,
        logoSource: mediaAssets.logoSource
      });
    } catch (error) {
      console.error('Error storing user-provided media:', error);
      throw error;
    }
  }

  /**
   * Create tool from approved submission with enhanced field mapping
   */
  public async createToolFromSubmission(submissionId: string, approvedBy?: string): Promise<{ success: boolean; toolId?: string; error?: string }> {
    try {
      // 1. Get the submission data
      const { data: submission, error: fetchError } = await supabase
        .from('tool_submissions')
        .select('*')
        .eq('id', submissionId)
        .eq('status', 'approved')
        .single();

      if (fetchError || !submission) {
        return { success: false, error: 'Submission not found or not approved' };
      }

      // 2. Check if tool already exists
      const { data: existingTool, error: checkError } = await supabase
        .from('tools')
        .select('id')
        .eq('name', submission.name)
        .single();

      if (existingTool) {
        return { success: false, error: 'Tool with this name already exists' };
      }

      // 3. Transform submission to tool data with enhanced mapping
      const toolData = this.transformSubmissionToTool(submission, approvedBy);

      // 4. Create the tool
      const { data: newTool, error: createError } = await supabase
        .from('tools')
        .insert(toolData)
        .select('id')
        .single();

      if (createError) {
        return { success: false, error: `Failed to create tool: ${createError.message}` };
      }

      // 4.5. Handle media for detailed submissions
      if (submission.submission_type === 'detailed') {
        if (submission.media_assets && this.hasUserProvidedMedia(submission.media_assets)) {
          // User provided media - store it directly
          try {
            await this.storeUserProvidedMedia(newTool.id, submission.media_assets);
            console.log(`📸 User-provided media stored for detailed submission: ${newTool.id}`);
          } catch (mediaError) {
            console.warn('Failed to store user-provided media:', mediaError);
          }
        } else {
          // No user media - trigger automatic collection
          try {
            await this.triggerMediaCollection(newTool.id, submission.url);
            console.log(`📸 Automatic media collection triggered for detailed submission: ${newTool.id}`);
          } catch (mediaError) {
            console.warn('Media collection failed for detailed submission:', mediaError);
          }
        }
      }

      // 5. Update submission status
      const { error: updateError } = await supabase
        .from('tool_submissions')
        .update({
          status: 'completed',
          processed_at: new Date().toISOString(),
          tool_id: newTool.id
        })
        .eq('id', submissionId);

      if (updateError) {
        console.warn('Failed to update submission status:', updateError);
      }

      return { success: true, toolId: newTool.id };

    } catch (error) {
      console.error('Error creating tool from submission:', error);
      return { success: false, error: 'Internal server error' };
    }
  }

  /**
   * Process submission based on type with enhanced field mapping
   */
  public async processApprovedSubmission(submissionId: string, approvedBy?: string): Promise<{ success: boolean; message: string; toolId?: string }> {
    try {
      // Get submission details
      const { data: submission, error: fetchError } = await supabase
        .from('tool_submissions')
        .select('submission_type, name')
        .eq('id', submissionId)
        .single();

      if (fetchError || !submission) {
        return { success: false, message: 'Submission not found' };
      }

      if (submission.submission_type === 'detailed') {
        // Detailed submissions: Create tool immediately with enhanced mapping
        const result = await this.createToolFromSubmission(submissionId, approvedBy);

        if (result.success) {
          return {
            success: true,
            message: `Tool "${submission.name}" created and published from detailed submission with enhanced field mapping`,
            toolId: result.toolId
          };
        } else {
          return {
            success: false,
            message: result.error || 'Failed to create tool from detailed submission'
          };
        }
      } else {
        // Simple submissions: Trigger AI generation
        // This would integrate with your existing AI job system
        return {
          success: true,
          message: `Simple submission "${submission.name}" approved - AI content generation will be triggered`
        };
      }

    } catch (error) {
      console.error('Error processing approved submission:', error);
      return { success: false, message: 'Internal server error' };
    }
  }
}

// Export singleton instance
export const submissionTransformer = new SubmissionTransformer();
