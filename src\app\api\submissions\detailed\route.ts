import { NextRequest, NextResponse } from 'next/server';
import { supabase } from '@/lib/supabase';
import { mediaUploadHandler } from '@/lib/media-upload';

export async function POST(request: NextRequest) {
  try {
    // Parse form data for file uploads
    const formData = await request.formData();
    
    // Extract text fields
    const submissionData = {
      name: formData.get('name') as string,
      url: formData.get('url') as string,
      description: formData.get('description') as string,
      detailedDescription: formData.get('detailedDescription') as string,
      category: formData.get('category') as string,
      subcategory: formData.get('subcategory') as string,
      features: formData.get('features') as string,
      pricingType: formData.get('pricingType') as string,
      pricingDetails: formData.get('pricingDetails') as string,
      pros: formData.get('pros') as string,
      cons: formData.get('cons') as string,
      metaTitle: formData.get('metaTitle') as string,
      metaDescription: formData.get('metaDescription') as string,
      metaKeywords: formData.get('metaKeywords') as string,
      company: formData.get('company') as string,
      useCases: formData.get('useCases') as string,
      targetAudience: formData.get('targetAudience') as string,
      tags: formData.get('tags') as string,
      integrations: formData.get('integrations') as string,
      faq: formData.get('faq') as string,
      submitterName: formData.get('submitterName') as string,
      submitterEmail: formData.get('submitterEmail') as string,
      logoUrl: formData.get('logoUrl') as string,
    };

    // Validate required fields
    const { name, url, description, category, submitterEmail, company } = submissionData;

    if (!name || !url || !description || !category || !submitterEmail || !company) {
      return NextResponse.json(
        { success: false, error: 'Missing required fields: name, url, description, category, submitterEmail, company' },
        { status: 400 }
      );
    }

    // Validate URL format
    try {
      new URL(url);
    } catch {
      return NextResponse.json(
        { success: false, error: 'Invalid URL format' },
        { status: 400 }
      );
    }

    // Validate email format
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!emailRegex.test(submitterEmail)) {
      return NextResponse.json(
        { success: false, error: 'Invalid email format' },
        { status: 400 }
      );
    }

    console.log(`📝 Processing detailed submission with media for: ${name}`);

    // Generate temporary tool ID for media processing
    const tempToolId = `temp_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;

    // Process media uploads
    const mediaAssets = await mediaUploadHandler.processMediaAssets(formData, tempToolId);
    
    console.log('📸 Media assets processed:', {
      primaryImage: !!mediaAssets.primaryImage,
      primaryImageType: mediaAssets.primaryImageType,
      logo: !!mediaAssets.logo,
      logoSource: mediaAssets.logoSource
    });

    // Prepare submission data for database
    const submissionRecord = {
      name,
      url,
      description,
      detailed_description: submissionData.detailedDescription,
      category,
      subcategory: submissionData.subcategory || null,
      features: submissionData.features,
      pricing_type: submissionData.pricingType,
      pricing_details: submissionData.pricingDetails,
      pros: submissionData.pros,
      cons: submissionData.cons,
      meta_title: submissionData.metaTitle,
      meta_description: submissionData.metaDescription,
      meta_keywords: submissionData.metaKeywords || null,
      company: submissionData.company,
      use_cases: submissionData.useCases,
      target_audience: submissionData.targetAudience,
      tags: submissionData.tags || null,
      integrations: submissionData.integrations || null,
      faq: submissionData.faq || null,
      submitter_name: submissionData.submitterName,
      submitter_email: submitterData.submitterEmail,
      submission_type: 'detailed',
      status: 'pending',
      submitted_at: new Date().toISOString(),
      // Store media assets as JSON
      media_assets: Object.keys(mediaAssets).length > 0 ? mediaAssets : null,
    };

    // Insert submission into database
    const { data: submission, error: insertError } = await supabase
      .from('tool_submissions')
      .insert(submissionRecord)
      .select()
      .single();

    if (insertError) {
      console.error('Database insertion error:', insertError);
      return NextResponse.json(
        { success: false, error: 'Failed to save submission' },
        { status: 500 }
      );
    }

    console.log(`✅ Detailed submission saved with ID: ${submission.id}`);

    // Return success response
    return NextResponse.json({
      success: true,
      message: 'Detailed submission received successfully! Your comprehensive submission will be reviewed by our editorial team.',
      submissionId: submission.id,
      mediaAssetsCount: {
        primaryImage: mediaAssets.primaryImage ? 1 : 0,
        logo: mediaAssets.logo ? 1 : 0,
        total: (mediaAssets.primaryImage ? 1 : 0) + (mediaAssets.logo ? 1 : 0)
      }
    });

  } catch (error) {
    console.error('Detailed submission error:', error);
    return NextResponse.json(
      { success: false, error: 'Failed to process detailed submission' },
      { status: 500 }
    );
  }
}
