import { NextRequest, NextResponse } from 'next/server';
import { validate<PERSON><PERSON><PERSON><PERSON> } from '@/lib/auth';
import { supabase } from '@/lib/supabase';

/**
 * GET /api/admin/content/stats
 * Get content generation statistics for the admin dashboard
 */
export async function GET(request: NextRequest) {
  try {
    // Validate admin API key
    const isValid = await validateApiKey(request);
    if (!isValid) {
      return NextResponse.json(
        { success: false, error: 'Unauthorized' },
        { status: 401 }
      );
    }

    // Get tool statistics
    const { data: toolStats, error: toolError } = await supabase
      .from('tools')
      .select('ai_generation_status, content_status')
      .not('ai_generation_status', 'is', null);

    if (toolError) {
      console.error('Error fetching tool stats:', toolError);
      throw new Error('Failed to fetch tool statistics');
    }

    // Get job statistics
    const { data: jobStats, error: jobError } = await supabase
      .from('ai_generation_jobs')
      .select('status, job_type, created_at')
      .gte('created_at', new Date(Date.now() - 24 * 60 * 60 * 1000).toISOString()); // Last 24 hours

    if (jobError) {
      console.error('Error fetching job stats:', jobError);
      throw new Error('Failed to fetch job statistics');
    }

    // Calculate statistics
    const totalTools = toolStats?.length || 0;
    const generatedContent = toolStats?.filter(tool => 
      tool.ai_generation_status === 'completed' || tool.ai_generation_status === 'approved'
    ).length || 0;
    
    const pendingGeneration = toolStats?.filter(tool => 
      tool.ai_generation_status === 'pending' || tool.ai_generation_status === 'processing'
    ).length || 0;
    
    const failedGeneration = toolStats?.filter(tool => 
      tool.ai_generation_status === 'failed'
    ).length || 0;

    // Job-based statistics
    const contentJobs = jobStats?.filter(job => 
      job.job_type === 'generate' || job.job_type === 'bulk'
    ) || [];
    
    const queueSize = contentJobs.filter(job => 
      job.status === 'pending' || job.status === 'processing'
    ).length;
    
    const activeJobs = contentJobs.filter(job => 
      job.status === 'processing'
    ).length;

    const stats = {
      totalTools,
      generatedContent,
      pendingGeneration,
      failedGeneration,
      queueSize,
      activeJobs,
      // Additional metrics
      completedToday: contentJobs.filter(job => 
        job.status === 'completed' && 
        new Date(job.created_at).toDateString() === new Date().toDateString()
      ).length,
      failedToday: contentJobs.filter(job => 
        job.status === 'failed' && 
        new Date(job.created_at).toDateString() === new Date().toDateString()
      ).length
    };

    return NextResponse.json({
      success: true,
      stats
    });

  } catch (error: any) {
    console.error('Content stats API error:', error);
    return NextResponse.json(
      { 
        success: false, 
        error: error.message || 'Failed to fetch content statistics' 
      },
      { status: 500 }
    );
  }
}
