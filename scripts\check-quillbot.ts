#!/usr/bin/env tsx

import * as dotenv from 'dotenv';
import { createClient } from '@supabase/supabase-js';

// Load environment variables
dotenv.config({ path: '.env.local' });

// Create Supabase client with service role
const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL!;
const supabaseKey = process.env.SUPABASE_SERVICE_ROLE_KEY!;
const supabase = createClient(supabaseUrl, supabaseKey);

async function checkQuillBot() {
  console.log('🔍 Checking for QuillBot tool specifically...');
  
  try {
    // Check if QuillBot exists
    const { data, error } = await supabase
      .from('tools')
      .select('*')
      .eq('id', 'quillbot')
      .single();
      
    if (error) {
      console.log('❌ QuillBot not found:', error.message);
      
      // Try to create it
      console.log('🔧 Creating QuillBot tool...');
      const { data: newTool, error: createError } = await supabase
        .from('tools')
        .insert({
          id: 'quillbot',
          name: 'QuillBot',
          slug: 'quillbot',
          link: '/tools/quillbot',
          description: 'AI-powered paraphrasing and writing assistant',
          short_description: 'AI writing assistant',
          website: 'https://quillbot.com',
          category_id: 'writing-assistant',
          company: 'QuillBot',
          content_status: 'draft',
          is_verified: true,
          is_claimed: false
        })
        .select()
        .single();
        
      if (createError) {
        console.log('❌ Failed to create QuillBot:', createError.message);
      } else {
        console.log('✅ Created QuillBot successfully!');
      }
    } else {
      console.log('✅ QuillBot found:');
      console.log(`   ID: ${data.id}`);
      console.log(`   Name: ${data.name}`);
      console.log(`   Status: ${data.content_status}`);
    }
    
    // Check total count
    const { count, error: countError } = await supabase
      .from('tools')
      .select('id', { count: 'exact', head: true });
      
    if (!countError) {
      console.log(`📊 Total tools in database: ${count}`);
    }
    
  } catch (error) {
    console.error('❌ Script error:', error);
  }
}

checkQuillBot()
  .then(() => {
    console.log('\n🎉 Check complete!');
    process.exit(0);
  })
  .catch((error) => {
    console.error('❌ Check failed:', error);
    process.exit(1);
  });
