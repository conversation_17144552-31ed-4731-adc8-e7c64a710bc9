import { NextRequest, NextResponse } from 'next/server';
import { validate<PERSON><PERSON><PERSON><PERSON> } from '@/lib/auth';
import { workflowManager } from '@/lib/workflow/workflow-manager';
import { supabase } from '@/lib/supabase';

/**
 * POST /api/admin/workflow/test
 * Test workflow system functionality
 */
export async function POST(request: NextRequest) {
  try {
    // Validate admin API key
    const isValid = await validateApiKey(request);
    if (!isValid) {
      return NextResponse.json(
        { success: false, error: 'Unauthorized' },
        { status: 401 }
      );
    }

    const body = await request.json();
    const { action, toolId } = body;

    const results: any = {
      action,
      toolId,
      timestamp: new Date().toISOString(),
      results: {}
    };

    switch (action) {
      case 'initialize_workflow':
        if (!toolId) {
          throw new Error('Tool ID is required for workflow initialization');
        }

        // Initialize workflow for the tool
        const workflowState = await workflowManager.initializeWorkflow(toolId, 'draft');
        results.results.workflowState = workflowState;
        break;

      case 'test_transitions':
        if (!toolId) {
          throw new Error('Tool ID is required for transition testing');
        }

        // Test workflow transitions
        const transitions = [];
        
        // Initialize if not exists
        let currentState = await workflowManager.getWorkflowState(toolId);
        if (!currentState) {
          currentState = await workflowManager.initializeWorkflow(toolId, 'draft');
          transitions.push({ action: 'initialize', result: currentState });
        }

        // Test transition to content generation
        const contentGenState = await workflowManager.transitionTo(
          toolId, 
          'content_generation', 
          'test_system'
        );
        transitions.push({ action: 'transition_to_content_generation', result: contentGenState });

        // Test transition to content review
        const reviewState = await workflowManager.transitionTo(
          toolId, 
          'content_review', 
          'test_system'
        );
        transitions.push({ action: 'transition_to_content_review', result: reviewState });

        results.results.transitions = transitions;
        break;

      case 'test_content_generation':
        if (!toolId) {
          throw new Error('Tool ID is required for content generation testing');
        }

        // Test content generation workflow
        const jobId = await workflowManager.startContentGeneration(toolId, {
          priority: 'high',
          triggeredBy: 'test_system'
        });
        
        results.results.contentGeneration = {
          jobId,
          status: 'started'
        };
        break;

      case 'test_editorial_review':
        if (!toolId) {
          throw new Error('Tool ID is required for editorial review testing');
        }

        // Test editorial review workflow
        const reviewId = await workflowManager.startEditorialReview(toolId, 'test_system');
        
        results.results.editorialReview = {
          reviewId,
          status: 'started'
        };
        break;

      case 'get_workflow_data':
        if (!toolId) {
          throw new Error('Tool ID is required for workflow data retrieval');
        }

        // Get comprehensive workflow data
        const [state, history] = await Promise.all([
          workflowManager.getWorkflowState(toolId),
          workflowManager.getWorkflowHistory(toolId)
        ]);

        results.results.workflowData = {
          state,
          history,
          historyCount: history.length
        };
        break;

      case 'test_database_tables':
        // Test database table existence and structure
        const tableTests = [];

        // Test workflow_states table
        const { data: statesData, error: statesError } = await supabase
          .from('workflow_states')
          .select('*')
          .limit(1);
        
        tableTests.push({
          table: 'workflow_states',
          exists: !statesError,
          error: statesError?.message,
          sampleCount: statesData?.length || 0
        });

        // Test workflow_transitions table
        const { data: transitionsData, error: transitionsError } = await supabase
          .from('workflow_transitions')
          .select('*')
          .limit(1);
        
        tableTests.push({
          table: 'workflow_transitions',
          exists: !transitionsError,
          error: transitionsError?.message,
          sampleCount: transitionsData?.length || 0
        });

        results.results.databaseTests = tableTests;
        break;

      case 'cleanup_test_data':
        if (!toolId) {
          throw new Error('Tool ID is required for cleanup');
        }

        // Clean up test workflow data
        const cleanupResults = [];

        // Remove workflow states
        const { error: stateCleanupError } = await supabase
          .from('workflow_states')
          .delete()
          .eq('tool_id', toolId);

        cleanupResults.push({
          table: 'workflow_states',
          success: !stateCleanupError,
          error: stateCleanupError?.message
        });

        // Remove workflow transitions
        const { error: transitionCleanupError } = await supabase
          .from('workflow_transitions')
          .delete()
          .eq('tool_id', toolId);

        cleanupResults.push({
          table: 'workflow_transitions',
          success: !transitionCleanupError,
          error: transitionCleanupError?.message
        });

        results.results.cleanup = cleanupResults;
        break;

      default:
        throw new Error(`Unknown test action: ${action}`);
    }

    return NextResponse.json({
      success: true,
      data: results
    });

  } catch (error: any) {
    console.error('Workflow test API error:', error);
    return NextResponse.json(
      { 
        success: false, 
        error: error.message || 'Workflow test failed' 
      },
      { status: 500 }
    );
  }
}

/**
 * GET /api/admin/workflow/test
 * Get workflow test status and available actions
 */
export async function GET(request: NextRequest) {
  try {
    // Validate admin API key
    const isValid = await validateApiKey(request);
    if (!isValid) {
      return NextResponse.json(
        { success: false, error: 'Unauthorized' },
        { status: 401 }
      );
    }

    const availableActions = [
      'initialize_workflow',
      'test_transitions',
      'test_content_generation',
      'test_editorial_review',
      'get_workflow_data',
      'test_database_tables',
      'cleanup_test_data'
    ];

    return NextResponse.json({
      success: true,
      data: {
        message: 'Workflow test API is ready',
        availableActions,
        usage: 'POST with { "action": "action_name", "toolId": "tool_id" }'
      }
    });

  } catch (error: any) {
    console.error('Workflow test API error:', error);
    return NextResponse.json(
      { 
        success: false, 
        error: error.message || 'Failed to get test status' 
      },
      { status: 500 }
    );
  }
}
