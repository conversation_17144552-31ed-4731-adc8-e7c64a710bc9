#!/usr/bin/env tsx

import * as dotenv from 'dotenv';
import { createClient } from '@supabase/supabase-js';

// Load environment variables
dotenv.config({ path: '.env.local' });

// Create Supabase client
const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL!;
const supabaseKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!;
const supabase = createClient(supabaseUrl, supabaseKey);

async function debugEditorialData() {
  console.log('🔍 Debugging Editorial Data...\n');

  try {
    // 1. Check AI generation jobs
    console.log('1️⃣ AI Generation Jobs:');
    const { data: aiJobs, error: aiJobsError } = await supabase
      .from('ai_generation_jobs')
      .select('id, tool_id, status, job_type, created_at')
      .order('created_at', { ascending: false })
      .limit(10);

    if (aiJobsError) {
      console.error('❌ Error fetching AI jobs:', aiJobsError);
    } else {
      console.log(`📊 Found ${aiJobs?.length || 0} AI generation jobs:`);
      aiJobs?.forEach((job, index) => {
        console.log(`  ${index + 1}. ID: ${job.id}`);
        console.log(`     Tool ID: ${job.tool_id}`);
        console.log(`     Status: ${job.status}`);
        console.log(`     Type: ${job.job_type}`);
        console.log(`     Created: ${job.created_at}`);
        console.log('');
      });
    }

    // 2. Check tools table
    console.log('\n2️⃣ Tools:');
    const { data: tools, error: toolsError } = await supabase
      .from('tools')
      .select('id, name, editorial_review_id, ai_generation_job_id')
      .limit(10);

    if (toolsError) {
      console.error('❌ Error fetching tools:', toolsError);
    } else {
      console.log(`📊 Found ${tools?.length || 0} tools:`);
      tools?.forEach((tool, index) => {
        console.log(`  ${index + 1}. ID: ${tool.id}`);
        console.log(`     Name: ${tool.name}`);
        console.log(`     Editorial Review ID: ${tool.editorial_review_id || 'None'}`);
        console.log(`     AI Job ID: ${tool.ai_generation_job_id || 'None'}`);
        console.log('');
      });
    }

    // 3. Check editorial reviews
    console.log('\n3️⃣ Editorial Reviews:');
    const { data: reviews, error: reviewsError } = await supabase
      .from('editorial_reviews')
      .select('id, tool_id, review_status, quality_score, created_at')
      .order('created_at', { ascending: false })
      .limit(10);

    if (reviewsError) {
      console.error('❌ Error fetching editorial reviews:', reviewsError);
    } else {
      console.log(`📊 Found ${reviews?.length || 0} editorial reviews:`);
      reviews?.forEach((review, index) => {
        console.log(`  ${index + 1}. ID: ${review.id}`);
        console.log(`     Tool ID: ${review.tool_id}`);
        console.log(`     Status: ${review.review_status}`);
        console.log(`     Quality Score: ${review.quality_score}`);
        console.log(`     Created: ${review.created_at}`);
        console.log('');
      });
    }

    // 4. Check for orphaned AI jobs (AI jobs with invalid tool_id)
    console.log('\n4️⃣ Checking for orphaned AI jobs:');
    if (aiJobs && tools) {
      const toolIds = new Set(tools.map(t => t.id));
      const orphanedJobs = aiJobs.filter(job => !toolIds.has(job.tool_id));
      
      if (orphanedJobs.length > 0) {
        console.log(`⚠️  Found ${orphanedJobs.length} orphaned AI jobs:`);
        orphanedJobs.forEach((job, index) => {
          console.log(`  ${index + 1}. AI Job ID: ${job.id}`);
          console.log(`     Invalid Tool ID: ${job.tool_id}`);
          console.log('');
        });
      } else {
        console.log('✅ No orphaned AI jobs found');
      }
    }

    // 5. Check the specific AI job from the error
    console.log('\n5️⃣ Checking specific AI job from error:');
    const problemJobId = '7bd7b798-f0b8-4946-a96c-6aea14e0678d';
    const { data: problemJob, error: problemJobError } = await supabase
      .from('ai_generation_jobs')
      .select('*')
      .eq('id', problemJobId)
      .single();

    if (problemJobError) {
      console.log(`❌ Problem job ${problemJobId} not found:`, problemJobError);
    } else {
      console.log(`📋 Problem job details:`);
      console.log(`  ID: ${problemJob.id}`);
      console.log(`  Tool ID: ${problemJob.tool_id}`);
      console.log(`  Status: ${problemJob.status}`);
      console.log(`  Type: ${problemJob.job_type}`);
      
      // Check if the tool exists
      const { data: relatedTool, error: relatedToolError } = await supabase
        .from('tools')
        .select('id, name')
        .eq('id', problemJob.tool_id)
        .single();

      if (relatedToolError) {
        console.log(`  ❌ Related tool ${problemJob.tool_id} NOT FOUND:`, relatedToolError);
      } else {
        console.log(`  ✅ Related tool found: ${relatedTool.name} (${relatedTool.id})`);
      }
    }

  } catch (error) {
    console.error('❌ Script error:', error);
  }
}

// Run the debug script
debugEditorialData()
  .then(() => {
    console.log('\n🎉 Debug complete!');
    process.exit(0);
  })
  .catch((error) => {
    console.error('❌ Debug failed:', error);
    process.exit(1);
  });
