'use client';

import { useState } from 'react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { z } from 'zod';
import { apiClient } from '@/lib/api';

// Validation schema for simple submission
const simpleSubmissionSchema = z.object({
  name: z.string().min(1, 'Tool name is required').max(100, 'Tool name must be less than 100 characters'),
  url: z.string().url('Please enter a valid URL'),
  description: z.string().min(50, 'Description must be at least 50 characters').max(500, 'Description must be less than 500 characters'),
  category: z.string().min(1, 'Please select a category'),
  submitterName: z.string().min(1, 'Your name is required').max(100, 'Name must be less than 100 characters'),
  submitterEmail: z.string().email('Please enter a valid email address'),
});

type SimpleSubmissionData = z.infer<typeof simpleSubmissionSchema>;

interface SimpleSubmissionFormProps {
  onSuccess?: () => void;
  onBack?: () => void;
}

export function SimpleSubmissionForm({ onSuccess, onBack }: SimpleSubmissionFormProps) {
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [submitError, setSubmitError] = useState<string | null>(null);
  const [success, setSuccess] = useState(false);

  const {
    register,
    handleSubmit,
    formState: { errors },
    reset,
  } = useForm<SimpleSubmissionData>({
    resolver: zodResolver(simpleSubmissionSchema),
    mode: 'onChange'
  });

  const categories = [
    { value: 'writing-tools', label: 'Writing Tools' },
    { value: 'image-generators', label: 'Image Generators' },
    { value: 'chatbots', label: 'Chatbots' },
    { value: 'dev-tools', label: 'Developer Tools' },
    { value: 'video-ai', label: 'Video AI' },
    { value: 'music-generation', label: 'Music Generation' },
    { value: 'voice-ai', label: 'Voice AI' },
    { value: 'data-analysis', label: 'Data Analysis' },
    { value: 'productivity-ai', label: 'Productivity AI' },
    { value: 'design-ai', label: 'Design AI' },
    { value: 'marketing-ai', label: 'Marketing AI' },
    { value: 'education-ai', label: 'Education AI' },
    { value: 'healthcare-ai', label: 'Healthcare AI' },
    { value: 'finance-ai', label: 'Finance AI' },
  ];

  const onSubmit = async (data: SimpleSubmissionData) => {
    setIsSubmitting(true);
    setSubmitError(null);

    try {
      // Submit with submission_type flag for simple submission
      await apiClient.submitTool({
        ...data,
        submissionType: 'simple', // Flag for backend processing
        pricingType: 'unknown', // Will be determined by AI
      });
      setSuccess(true);
      reset();

      if (onSuccess) {
        setTimeout(onSuccess, 2000);
      }
    } catch (err) {
      setSubmitError(err instanceof Error ? err.message : 'Failed to submit tool');
    } finally {
      setIsSubmitting(false);
    }
  };

  if (success) {
    return (
      <div className="bg-zinc-800 p-8 rounded-lg border border-green-500">
        <div className="text-center">
          <div className="text-green-400 text-4xl mb-4">✅</div>
          <h3 className="text-2xl font-bold text-white mb-4">Simple Submission Received!</h3>
          <p className="text-gray-300 mb-6">
            Thank you for your submission! We'll review your tool and use our AI system to generate 
            comprehensive content including features, pricing, pros & cons, and more.
          </p>
          <div className="bg-zinc-700/50 rounded-lg p-4 mb-6">
            <h4 className="font-semibold mb-2 text-orange-400">What happens next:</h4>
            <ul className="text-sm text-gray-300 space-y-2 text-left">
              <li>• Admin reviews your submission (12-24 hours)</li>
              <li>• If approved, our AI generates detailed content</li>
              <li>• Editorial team reviews the generated content</li>
              <li>• Your tool goes live on our directory</li>
              <li>• You'll receive email updates at each step</li>
            </ul>
          </div>
          <p className="text-sm text-gray-400">
            Expected total time: 24-48 hours
          </p>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-8">
      {/* Header */}
      <div className="text-center">
        <button
          onClick={onBack}
          className="inline-flex items-center text-gray-400 hover:text-white mb-4 transition-colors"
        >
          ← Back to submission options
        </button>
        <h2 className="text-3xl font-bold text-white mb-4">⚡ Simple Submit</h2>
        <p className="text-gray-300">
          Just provide the basics - we'll handle the detailed content generation with AI
        </p>
      </div>

      {/* Form */}
      <div className="bg-zinc-800 p-6 rounded-lg border border-zinc-700">
        <form onSubmit={handleSubmit(onSubmit)} className="space-y-6">
          {submitError && (
            <div className="bg-red-900/20 border border-red-500 rounded-lg p-4">
              <p className="text-red-400 text-sm">{submitError}</p>
            </div>
          )}

          {/* Tool Name */}
          <div>
            <label htmlFor="name" className="block text-sm font-medium text-gray-300 mb-2">
              Tool Name *
            </label>
            <input
              type="text"
              id="name"
              {...register('name')}
              className={`w-full px-3 py-2 bg-zinc-700 border rounded-md text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-orange-500 focus:border-transparent ${
                errors.name ? 'border-red-500' : 'border-zinc-600'
              }`}
              placeholder="Enter the name of your AI tool"
            />
            {errors.name && (
              <p className="mt-1 text-sm text-red-400">{errors.name.message}</p>
            )}
          </div>

          {/* Website URL */}
          <div>
            <label htmlFor="url" className="block text-sm font-medium text-gray-300 mb-2">
              Website URL *
            </label>
            <input
              type="url"
              id="url"
              {...register('url')}
              className={`w-full px-3 py-2 bg-zinc-700 border rounded-md text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-orange-500 focus:border-transparent ${
                errors.url ? 'border-red-500' : 'border-zinc-600'
              }`}
              placeholder="https://your-ai-tool.com"
            />
            {errors.url && (
              <p className="mt-1 text-sm text-red-400">{errors.url.message}</p>
            )}
          </div>

          {/* Brief Description */}
          <div>
            <label htmlFor="description" className="block text-sm font-medium text-gray-300 mb-2">
              Brief Description *
            </label>
            <textarea
              id="description"
              {...register('description')}
              rows={4}
              className={`w-full px-3 py-2 bg-zinc-700 border rounded-md text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-orange-500 focus:border-transparent ${
                errors.description ? 'border-red-500' : 'border-zinc-600'
              }`}
              placeholder="Briefly describe what this tool does and its main purpose (50-500 characters)"
            />
            {errors.description && (
              <p className="mt-1 text-sm text-red-400">{errors.description.message}</p>
            )}
            <p className="mt-1 text-xs text-gray-400">
              Our AI will expand this into detailed descriptions, features, and more
            </p>
          </div>

          {/* Category */}
          <div>
            <label htmlFor="category" className="block text-sm font-medium text-gray-300 mb-2">
              Category *
            </label>
            <select
              id="category"
              {...register('category')}
              className={`w-full px-3 py-2 bg-zinc-700 border rounded-md text-white focus:outline-none focus:ring-2 focus:ring-orange-500 focus:border-transparent ${
                errors.category ? 'border-red-500' : 'border-zinc-600'
              }`}
            >
              <option value="">Select a category</option>
              {categories.map(cat => (
                <option key={cat.value} value={cat.value}>
                  {cat.label}
                </option>
              ))}
            </select>
            {errors.category && (
              <p className="mt-1 text-sm text-red-400">{errors.category.message}</p>
            )}
          </div>

          {/* Submitter Information */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div>
              <label htmlFor="submitterName" className="block text-sm font-medium text-gray-300 mb-2">
                Your Name *
              </label>
              <input
                type="text"
                id="submitterName"
                {...register('submitterName')}
                className={`w-full px-3 py-2 bg-zinc-700 border rounded-md text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-orange-500 focus:border-transparent ${
                  errors.submitterName ? 'border-red-500' : 'border-zinc-600'
                }`}
                placeholder="Your full name"
              />
              {errors.submitterName && (
                <p className="mt-1 text-sm text-red-400">{errors.submitterName.message}</p>
              )}
            </div>

            <div>
              <label htmlFor="submitterEmail" className="block text-sm font-medium text-gray-300 mb-2">
                Your Email *
              </label>
              <input
                type="email"
                id="submitterEmail"
                {...register('submitterEmail')}
                className={`w-full px-3 py-2 bg-zinc-700 border rounded-md text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-orange-500 focus:border-transparent ${
                  errors.submitterEmail ? 'border-red-500' : 'border-zinc-600'
                }`}
                placeholder="<EMAIL>"
              />
              {errors.submitterEmail && (
                <p className="mt-1 text-sm text-red-400">{errors.submitterEmail.message}</p>
              )}
            </div>
          </div>

          {/* AI Generation Info */}
          <div className="bg-blue-900/20 border border-blue-500 rounded-lg p-4">
            <h4 className="text-blue-400 font-medium mb-2">🤖 AI Content Generation</h4>
            <p className="text-sm text-gray-300 mb-3">
              After admin approval, our AI will automatically generate:
            </p>
            <div className="grid grid-cols-2 gap-4 text-xs text-gray-300">
              <ul className="space-y-1">
                <li>• Detailed descriptions</li>
                <li>• Feature lists</li>
                <li>• Pricing information</li>
              </ul>
              <ul className="space-y-1">
                <li>• Pros & cons analysis</li>
                <li>• SEO metadata</li>
                <li>• FAQ sections</li>
              </ul>
            </div>
          </div>

          {/* Action Buttons */}
          <div className="flex gap-4 pt-4">
            <button
              type="submit"
              disabled={isSubmitting}
              className="flex-1 px-4 py-3 rounded-lg font-medium transition-all duration-200 bg-orange-500 hover:bg-orange-400 text-white disabled:opacity-50 disabled:cursor-not-allowed"
            >
              {isSubmitting ? 'Submitting Tool...' : 'Submit for AI Processing'}
            </button>

            {onBack && (
              <button
                type="button"
                onClick={onBack}
                className="px-6 py-3 rounded-lg font-medium transition-all duration-200 border border-zinc-600 hover:border-zinc-500 bg-transparent hover:bg-zinc-800 text-gray-300"
              >
                Back
              </button>
            )}
          </div>
        </form>
      </div>
    </div>
  );
}
