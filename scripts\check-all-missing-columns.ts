#!/usr/bin/env tsx

import * as dotenv from 'dotenv';
import { createClient } from '@supabase/supabase-js';

// Load environment variables
dotenv.config({ path: '.env.local' });

// Create Supabase client with service role
const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL!;
const supabaseKey = process.env.SUPABASE_SERVICE_ROLE_KEY!;
const supabase = createClient(supabaseUrl, supabaseKey);

async function checkAllMissingColumns() {
  console.log('🔍 Checking ALL missing columns...\n');

  try {
    // Check tool_submissions table
    console.log('1️⃣ Checking tool_submissions table:');
    const submissionColumns = [
      'reviewed_by', 'reviewed_at', 'review_notes', 'priority'
    ];

    for (const column of submissionColumns) {
      try {
        const { data, error } = await supabase
          .from('tool_submissions')
          .select(column)
          .limit(1);
          
        if (error && error.message.includes(`column "${column}" does not exist`)) {
          console.log(`   ❌ tool_submissions.${column} - MISSING`);
        } else {
          console.log(`   ✅ tool_submissions.${column} - EXISTS`);
        }
      } catch (err) {
        console.log(`   ❌ tool_submissions.${column} - MISSING`);
      }
    }

    // Check tools table
    console.log('\n2️⃣ Checking tools table:');
    const toolsColumns = [
      'pricing_type', 'pricing_details', 'pros', 'cons', 'short_description'
    ];

    for (const column of toolsColumns) {
      try {
        const { data, error } = await supabase
          .from('tools')
          .select(column)
          .limit(1);
          
        if (error && error.message.includes(`column "${column}" does not exist`)) {
          console.log(`   ❌ tools.${column} - MISSING`);
        } else {
          console.log(`   ✅ tools.${column} - EXISTS`);
        }
      } catch (err) {
        console.log(`   ❌ tools.${column} - MISSING`);
      }
    }

    // Generate complete migration
    console.log('\n3️⃣ Complete migration script:');
    console.log(`
-- Complete migration for dual submission system
-- Run this in Supabase SQL editor

-- Add missing columns to tool_submissions
DO $$ BEGIN
  IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name='tool_submissions' AND column_name='reviewed_by') THEN
    ALTER TABLE tool_submissions ADD COLUMN reviewed_by VARCHAR(255);
  END IF;
END $$;

DO $$ BEGIN
  IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name='tool_submissions' AND column_name='reviewed_at') THEN
    ALTER TABLE tool_submissions ADD COLUMN reviewed_at TIMESTAMP;
  END IF;
END $$;

DO $$ BEGIN
  IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name='tool_submissions' AND column_name='review_notes') THEN
    ALTER TABLE tool_submissions ADD COLUMN review_notes TEXT;
  END IF;
END $$;

DO $$ BEGIN
  IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name='tool_submissions' AND column_name='priority') THEN
    ALTER TABLE tool_submissions ADD COLUMN priority VARCHAR(20) DEFAULT 'normal' CHECK (priority IN ('high', 'normal', 'low'));
  END IF;
END $$;

-- Add missing columns to tools
DO $$ BEGIN
  IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name='tools' AND column_name='pricing_type') THEN
    ALTER TABLE tools ADD COLUMN pricing_type VARCHAR(50);
  END IF;
END $$;

DO $$ BEGIN
  IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name='tools' AND column_name='pricing_details') THEN
    ALTER TABLE tools ADD COLUMN pricing_details TEXT;
  END IF;
END $$;

DO $$ BEGIN
  IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name='tools' AND column_name='pros') THEN
    ALTER TABLE tools ADD COLUMN pros TEXT;
  END IF;
END $$;

DO $$ BEGIN
  IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name='tools' AND column_name='cons') THEN
    ALTER TABLE tools ADD COLUMN cons TEXT;
  END IF;
END $$;

-- Add indexes
CREATE INDEX IF NOT EXISTS idx_tool_submissions_reviewed_by ON tool_submissions(reviewed_by);
CREATE INDEX IF NOT EXISTS idx_tool_submissions_priority ON tool_submissions(priority);
CREATE INDEX IF NOT EXISTS idx_tools_pricing_type ON tools(pricing_type);
    `);

  } catch (error) {
    console.error('❌ Check failed:', error);
  }
}

checkAllMissingColumns()
  .then(() => {
    console.log('\n🎉 Column check complete!');
    process.exit(0);
  })
  .catch((error) => {
    console.error('❌ Check failed:', error);
    process.exit(1);
  });
