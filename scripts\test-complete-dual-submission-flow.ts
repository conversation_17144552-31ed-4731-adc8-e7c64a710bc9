#!/usr/bin/env tsx

import * as dotenv from 'dotenv';
import { createClient } from '@supabase/supabase-js';

// Load environment variables
dotenv.config({ path: '.env.local' });

// Create Supabase client with service role
const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL!;
const supabaseKey = process.env.SUPABASE_SERVICE_ROLE_KEY!;
const supabase = createClient(supabaseUrl, supabaseKey);

async function testCompleteDualSubmissionFlow() {
  console.log('🧪 Testing Complete Dual Submission System Flow...\n');

  const testResults = {
    detailedSubmissionCreation: false,
    enhancedFieldMapping: false,
    mediaHandling: false,
    adminApproval: false,
    toolCreation: false,
    publicDisplay: false,
    adminManagement: false,
    cleanup: false
  };

  let submissionId: string | null = null;
  let toolId: string | null = null;

  try {
    // 1. Test Detailed Submission Creation
    console.log('1️⃣ Testing Detailed Submission Creation with Enhanced Fields:');
    
    const detailedSubmission = {
      name: 'Complete Flow Test Tool',
      url: 'https://complete-flow-test.com',
      description: 'End-to-end test tool for complete dual submission flow validation',
      detailed_description: 'This comprehensive test tool validates the entire dual submission system including enhanced field mapping, media handling, admin approval workflow, and public display integration.',
      category: 'productivity-ai',
      subcategory: 'Workflow Automation',
      features: '• Enhanced field mapping validation\n• Media asset management\n• Admin workflow integration\n• Public display optimization',
      pricing_type: 'freemium',
      pricing_details: 'Free tier: Basic features\nPro tier: $39/month for advanced features\nEnterprise: Custom pricing for teams',
      pros: '• Comprehensive testing coverage\n• Enhanced field support\n• Streamlined admin workflow\n• Optimized user experience',
      cons: '• Complex implementation\n• Requires thorough testing\n• Multiple integration points',
      meta_title: 'Complete Flow Test Tool - Dual Submission System Validator',
      meta_description: 'Validate your dual submission system with our comprehensive test tool featuring enhanced field mapping and media handling.',
      meta_keywords: 'dual submission, test tool, enhanced fields, media handling, admin workflow',
      use_cases: 'System validation, workflow testing, integration verification, performance benchmarking, user experience optimization',
      target_audience: 'System administrators, QA engineers, product managers, development teams, technical stakeholders',
      integrations: 'Supabase, Next.js, TypeScript, Tailwind CSS, React Hook Form, Zod validation',
      tags: 'testing, validation, dual-submission, enhanced-fields, media-handling, admin-workflow',
      faq: 'Q: How does the dual submission system work?\nA: Users can choose between simple submissions (AI-generated content) or detailed submissions (user-provided content).\n\nQ: What enhanced fields are supported?\nA: The system supports detailed descriptions, use cases, target audience, integrations, tags, and comprehensive media assets.',
      submitter_name: 'Complete Flow Test Company',
      submitter_email: '<EMAIL>',
      submission_type: 'detailed',
      status: 'pending',
      submitted_at: new Date().toISOString(),
      media_assets: {
        primaryImage: 'data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAA...',
        primaryImageType: 'screenshot',
        logo: 'https://complete-flow-test.com/logo.png',
        logoSource: 'url'
      }
    };

    const { data: submission, error: submissionError } = await supabase
      .from('tool_submissions')
      .insert(detailedSubmission)
      .select()
      .single();

    if (submissionError) {
      console.log(`   ❌ Detailed submission creation failed: ${submissionError.message}`);
    } else {
      submissionId = submission.id;
      console.log(`   ✅ Detailed submission created: ${submissionId}`);
      testResults.detailedSubmissionCreation = true;
    }

    // 2. Test Enhanced Field Mapping
    console.log('\n2️⃣ Testing Enhanced Field Mapping:');
    
    if (submissionId) {
      // Verify all enhanced fields are stored
      const { data: storedSubmission, error: fetchError } = await supabase
        .from('tool_submissions')
        .select('*')
        .eq('id', submissionId)
        .single();

      if (fetchError) {
        console.log(`   ❌ Failed to fetch stored submission: ${fetchError.message}`);
      } else {
        const enhancedFields = [
          'detailed_description', 'use_cases', 'target_audience', 
          'integrations', 'tags', 'meta_keywords', 'media_assets'
        ];
        
        const missingFields = enhancedFields.filter(field => 
          !storedSubmission[field] || storedSubmission[field] === null
        );

        if (missingFields.length === 0) {
          console.log(`   ✅ All enhanced fields stored successfully`);
          testResults.enhancedFieldMapping = true;
        } else {
          console.log(`   ❌ Missing enhanced fields: ${missingFields.join(', ')}`);
        }
      }
    }

    // 3. Test Media Handling
    console.log('\n3️⃣ Testing Media Asset Handling:');
    
    if (submissionId) {
      const { data: submissionWithMedia } = await supabase
        .from('tool_submissions')
        .select('media_assets')
        .eq('id', submissionId)
        .single();

      if (submissionWithMedia?.media_assets) {
        const mediaAssets = submissionWithMedia.media_assets;
        const hasRequiredMedia = mediaAssets.primaryImage && mediaAssets.logo;
        
        if (hasRequiredMedia) {
          console.log(`   ✅ Media assets stored correctly:`);
          console.log(`      Primary Image: ${mediaAssets.primaryImageType}`);
          console.log(`      Logo Source: ${mediaAssets.logoSource}`);
          testResults.mediaHandling = true;
        } else {
          console.log(`   ❌ Missing required media assets`);
        }
      } else {
        console.log(`   ❌ No media assets found`);
      }
    }

    // 4. Test Admin Approval Process
    console.log('\n4️⃣ Testing Admin Approval Process:');
    
    if (submissionId) {
      // Simulate admin approval
      const { error: approvalError } = await supabase
        .from('tool_submissions')
        .update({
          status: 'approved',
          reviewed_by: 'complete-flow-admin',
          reviewed_at: new Date().toISOString(),
          review_notes: 'Approved for complete flow testing'
        })
        .eq('id', submissionId);

      if (approvalError) {
        console.log(`   ❌ Admin approval failed: ${approvalError.message}`);
      } else {
        console.log(`   ✅ Admin approval successful`);
        testResults.adminApproval = true;
      }
    }

    // 5. Test Tool Creation with Enhanced Mapping
    console.log('\n5️⃣ Testing Tool Creation with Enhanced Field Mapping:');
    
    if (submissionId && testResults.adminApproval) {
      // Simulate the SubmissionTransformer process
      const { data: approvedSubmission } = await supabase
        .from('tool_submissions')
        .select('*')
        .eq('id', submissionId)
        .single();

      if (approvedSubmission) {
        const toolData = {
          name: approvedSubmission.name,
          slug: approvedSubmission.name.toLowerCase().replace(/[^a-z0-9]+/g, '-'),
          link: `/tools/${approvedSubmission.name.toLowerCase().replace(/[^a-z0-9]+/g, '-')}`,
          description: approvedSubmission.description,
          detailed_description: approvedSubmission.detailed_description,
          website: approvedSubmission.url,
          category_id: approvedSubmission.category,
          subcategory: approvedSubmission.subcategory,
          company: approvedSubmission.submitter_name,
          features: approvedSubmission.features,
          pricing_type: approvedSubmission.pricing_type,
          pricing_details: approvedSubmission.pricing_details,
          pros: approvedSubmission.pros,
          cons: approvedSubmission.cons,
          meta_title: approvedSubmission.meta_title,
          meta_description: approvedSubmission.meta_description,
          meta_keywords: approvedSubmission.meta_keywords,
          use_cases: approvedSubmission.use_cases,
          target_audience: approvedSubmission.target_audience,
          integrations: approvedSubmission.integrations,
          tags: approvedSubmission.tags?.split(',').map((t: string) => t.trim()),
          faqs: approvedSubmission.faq ? [{ 
            question: 'General FAQ', 
            answer: approvedSubmission.faq 
          }] : null,
          content_status: 'published',
          submission_type: 'detailed',
          is_verified: true,
          is_claimed: false,
          submission_id: approvedSubmission.id,
          submitted_by: approvedSubmission.submitter_email,
          submission_date: approvedSubmission.submitted_at,
          approved_by: 'complete-flow-admin',
          approved_at: new Date().toISOString(),
          primary_image: approvedSubmission.media_assets?.primaryImage,
          primary_image_type: approvedSubmission.media_assets?.primaryImageType,
          logo_url: approvedSubmission.media_assets?.logo,
          media_source: 'user_provided',
          media_updated_at: new Date().toISOString(),
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString(),
          published_at: new Date().toISOString()
        };

        const { data: createdTool, error: toolError } = await supabase
          .from('tools')
          .insert(toolData)
          .select()
          .single();

        if (toolError) {
          console.log(`   ❌ Tool creation failed: ${toolError.message}`);
        } else {
          toolId = createdTool.id;
          console.log(`   ✅ Tool created with enhanced fields: ${toolId}`);
          testResults.toolCreation = true;
        }
      }
    }

    // 6. Test Public Display Integration
    console.log('\n6️⃣ Testing Public Display Integration:');
    
    if (toolId) {
      const { data: publicTool, error: publicError } = await supabase
        .from('tools')
        .select(`
          id, name, slug, description, detailed_description,
          primary_image, logo_url, use_cases, target_audience,
          content_status, submission_type
        `)
        .eq('id', toolId)
        .eq('content_status', 'published')
        .single();

      if (publicError) {
        console.log(`   ❌ Public display test failed: ${publicError.message}`);
      } else {
        console.log(`   ✅ Tool available for public display:`);
        console.log(`      Name: ${publicTool.name}`);
        console.log(`      Status: ${publicTool.content_status}`);
        console.log(`      Type: ${publicTool.submission_type}`);
        console.log(`      Has Media: ${!!(publicTool.primary_image || publicTool.logo_url)}`);
        console.log(`      Enhanced Fields: ${!!(publicTool.detailed_description && publicTool.use_cases)}`);
        testResults.publicDisplay = true;
      }
    }

    // 7. Test Admin Management Interface
    console.log('\n7️⃣ Testing Admin Management Interface:');
    
    if (toolId) {
      // Test admin tools API
      const { data: adminTools, error: adminError } = await supabase
        .from('tools')
        .select(`
          id, name, content_status, submission_type, submitted_by,
          submission_date, approved_by, approved_at, primary_image, logo_url
        `)
        .eq('id', toolId);

      if (adminError) {
        console.log(`   ❌ Admin management test failed: ${adminError.message}`);
      } else {
        console.log(`   ✅ Admin management interface data available:`);
        const tool = adminTools[0];
        console.log(`      Submission Tracking: ${tool.submitted_by} → ${tool.approved_by}`);
        console.log(`      Media Assets: ${!!(tool.primary_image || tool.logo_url)}`);
        console.log(`      Status Management: ${tool.content_status}`);
        testResults.adminManagement = true;
      }
    }

    // 8. Cleanup Test Data
    console.log('\n8️⃣ Cleaning up test data:');
    
    if (toolId) {
      const { error: toolCleanupError } = await supabase
        .from('tools')
        .delete()
        .eq('id', toolId);

      if (toolCleanupError) {
        console.log(`   ❌ Tool cleanup failed: ${toolCleanupError.message}`);
      } else {
        console.log(`   🧹 Test tool cleaned up`);
      }
    }

    if (submissionId) {
      const { error: submissionCleanupError } = await supabase
        .from('tool_submissions')
        .delete()
        .eq('id', submissionId);

      if (submissionCleanupError) {
        console.log(`   ❌ Submission cleanup failed: ${submissionCleanupError.message}`);
      } else {
        console.log(`   🧹 Test submission cleaned up`);
        testResults.cleanup = true;
      }
    }

    // 9. Test Results Summary
    console.log('\n9️⃣ Complete Flow Test Results:');
    
    const passedTests = Object.values(testResults).filter(Boolean).length;
    const totalTests = Object.keys(testResults).length;
    const successRate = Math.round((passedTests / totalTests) * 100);

    console.log(`   📊 Tests Passed: ${passedTests}/${totalTests} (${successRate}%)`);
    
    Object.entries(testResults).forEach(([test, passed]) => {
      const status = passed ? '✅ PASS' : '❌ FAIL';
      console.log(`   ${status} ${test}`);
    });

    if (successRate === 100) {
      console.log('\n🎉 Complete dual submission system flow working perfectly!');
      console.log('\n✨ Verified Features:');
      console.log('   • Enhanced field mapping (20+ fields)');
      console.log('   • Simplified media handling (primary image + logo)');
      console.log('   • Admin approval workflow');
      console.log('   • Automatic tool creation with submission tracking');
      console.log('   • Public display integration');
      console.log('   • Admin management interface');
      return true;
    } else {
      console.log('\n❌ Some tests failed - system needs attention.');
      return false;
    }

  } catch (error) {
    console.error('❌ Complete flow test failed:', error);
    return false;
  }
}

// Run the test
testCompleteDualSubmissionFlow()
  .then((success) => {
    console.log('\n🎉 Complete dual submission flow test finished!');
    process.exit(success ? 0 : 1);
  })
  .catch((error) => {
    console.error('❌ Test failed:', error);
    process.exit(1);
  });
