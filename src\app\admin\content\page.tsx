'use client';

import React, { useState, useEffect } from 'react';
import {
  <PERSON><PERSON>,
  <PERSON>ting<PERSON>,
  Layers,
  FileText,
  Play,
  AlertCircle,
  CheckCircle,
  Clock,
  Zap
} from 'lucide-react';
import { LoadingSpinner } from '@/components/ui/LoadingSpinner';

interface ContentStats {
  totalTools: number;
  generatedContent: number;
  pendingGeneration: number;
  failedGeneration: number;
  queueSize: number;
  activeJobs: number;
}

interface QueueItem {
  id: string;
  toolName: string;
  url: string;
  status: 'pending' | 'processing' | 'completed' | 'failed';
  priority: 'low' | 'medium' | 'high';
  createdAt: string;
  estimatedTime?: string;
}

export default function ContentGenerationPage() {
  const [stats, setStats] = useState<ContentStats | null>(null);
  const [queue, setQueue] = useState<QueueItem[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [isGenerating, setIsGenerating] = useState(false);

  // Load real job data from the job management system
  useEffect(() => {
    const loadData = async () => {
      try {
        setIsLoading(true);
        setError(null);

        // Fetch real job data and stats
        const [jobsResponse, statsResponse] = await Promise.all([
          fetch('/api/automation/jobs?status=pending,processing,failed&limit=50', {
            headers: {
              'x-admin-api-key': process.env.NEXT_PUBLIC_ADMIN_API_KEY || 'aidude_admin_2024_secure_key_xyz789'
            }
          }),
          fetch('/api/admin/content/stats', {
            headers: {
              'x-admin-api-key': process.env.NEXT_PUBLIC_ADMIN_API_KEY || 'aidude_admin_2024_secure_key_xyz789'
            }
          })
        ]);

        if (!jobsResponse.ok) {
          throw new Error('Failed to fetch job data');
        }

        const jobsData = await jobsResponse.json();
        const jobs = jobsData.success ? jobsData.data : [];

        // Transform job data to queue format
        const transformedQueue: QueueItem[] = jobs
          .filter((job: any) => job.type === 'content_generation' || job.type === 'tool_submission')
          .map((job: any) => ({
            id: job.id,
            toolName: job.data?.name || job.data?.toolName || `Tool ${job.id.slice(0, 8)}`,
            url: job.data?.url || job.data?.toolUrl || 'Unknown URL',
            status: job.status === 'processing' ? 'processing' :
                   job.status === 'failed' ? 'failed' : 'pending',
            priority: job.priority === 4 ? 'high' :
                     job.priority === 3 ? 'high' :
                     job.priority === 2 ? 'medium' : 'low',
            createdAt: job.createdAt,
            estimatedTime: job.estimatedDuration ? `${Math.ceil(job.estimatedDuration / 60000)} min` : undefined
          }));

        setQueue(transformedQueue);

        // Load stats (with fallback to calculated stats)
        if (statsResponse.ok) {
          const statsData = await statsResponse.json();
          setStats(statsData.success ? statsData.stats : calculateStatsFromJobs(jobs));
        } else {
          setStats(calculateStatsFromJobs(jobs));
        }

      } catch (err) {
        console.error('Error loading content generation data:', err);
        setError('Failed to load content generation data. Please check your connection and try again.');

        // Set empty data on error
        setQueue([]);
        setStats({
          totalTools: 0,
          generatedContent: 0,
          pendingGeneration: 0,
          failedGeneration: 0,
          queueSize: 0,
          activeJobs: 0
        });
      } finally {
        setIsLoading(false);
      }
    };

    loadData();
  }, []);

  // Helper function to calculate stats from job data
  const calculateStatsFromJobs = (jobs: any[]) => {
    const contentJobs = jobs.filter(job =>
      job.type === 'content_generation' || job.type === 'tool_submission'
    );

    return {
      totalTools: jobs.length,
      generatedContent: contentJobs.filter(job => job.status === 'completed').length,
      pendingGeneration: contentJobs.filter(job => job.status === 'pending').length,
      failedGeneration: contentJobs.filter(job => job.status === 'failed').length,
      queueSize: contentJobs.filter(job => ['pending', 'processing'].includes(job.status)).length,
      activeJobs: contentJobs.filter(job => job.status === 'processing').length
    };
  };

  const handleStartGeneration = async () => {
    setIsGenerating(true);
    try {
      // Start content generation for pending tools
      const response = await fetch('/api/admin/content/generate', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'x-admin-api-key': process.env.NEXT_PUBLIC_ADMIN_API_KEY || 'aidude_admin_2024_secure_key_xyz789'
        },
        body: JSON.stringify({
          action: 'start_batch_generation',
          options: {
            priority: 'normal',
            maxJobs: 10
          }
        })
      });

      if (!response.ok) {
        throw new Error('Failed to start content generation');
      }

      const result = await response.json();
      console.log('Content generation started:', result);

      // Refresh data after starting
      window.location.reload();
    } catch (err) {
      console.error('Error starting content generation:', err);
      setError('Failed to start content generation. Please try again.');
    } finally {
      setIsGenerating(false);
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'processing':
        return <Clock className="w-4 h-4 text-yellow-400" />;
      case 'completed':
        return <CheckCircle className="w-4 h-4 text-green-400" />;
      case 'failed':
        return <AlertCircle className="w-4 h-4 text-red-400" />;
      default:
        return <Clock className="w-4 h-4 text-gray-400" />;
    }
  };

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case 'high':
        return 'text-red-400';
      case 'medium':
        return 'text-yellow-400';
      default:
        return 'text-gray-400';
    }
  };

  if (isLoading) {
    return (
      <div className="flex items-center justify-center min-h-96">
        <div className="text-center">
          <LoadingSpinner size="lg" />
          <p className="text-gray-400 mt-4">Loading content generation dashboard...</p>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="bg-red-900 border border-red-700 rounded-lg p-6">
        <div className="flex items-center space-x-2">
          <AlertCircle className="w-5 h-5 text-red-400" />
          <h3 className="text-lg font-semibold text-red-200">Error</h3>
        </div>
        <p className="text-red-300 mt-2">{error}</p>
        <button
          onClick={() => window.location.reload()}
          className="mt-4 bg-red-800 hover:bg-red-700 text-white px-4 py-2 rounded-lg transition-colors"
        >
          Retry
        </button>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-2xl font-bold text-white">Content Generation</h1>
          <p className="text-gray-400">AI-powered content generation and management</p>
        </div>
        
        <div className="flex items-center space-x-3">
          <button
            onClick={handleStartGeneration}
            disabled={isGenerating}
            className="flex items-center space-x-2 bg-green-700 hover:bg-green-600 disabled:bg-gray-600 text-white px-4 py-2 rounded-lg transition-colors"
          >
            {isGenerating ? (
              <>
                <LoadingSpinner size="sm" />
                <span>Starting...</span>
              </>
            ) : (
              <>
                <Play className="w-4 h-4" />
                <span>Start Generation</span>
              </>
            )}
          </button>
        </div>
      </div>

      {/* Stats Cards */}
      {stats && (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          <div className="bg-zinc-800 border border-black rounded-lg p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-gray-400 text-sm">Generated Content</p>
                <p className="text-2xl font-bold text-white">{stats.generatedContent}</p>
                <p className="text-xs text-gray-500">of {stats.totalTools} tools</p>
              </div>
              <Bot className="w-8 h-8 text-blue-400" />
            </div>
          </div>

          <div className="bg-zinc-800 border border-black rounded-lg p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-gray-400 text-sm">Queue Size</p>
                <p className="text-2xl font-bold text-white">{stats.queueSize}</p>
                <p className="text-xs text-gray-500">{stats.activeJobs} active jobs</p>
              </div>
              <Layers className="w-8 h-8 text-yellow-400" />
            </div>
          </div>

          <div className="bg-zinc-800 border border-black rounded-lg p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-gray-400 text-sm">Failed Generation</p>
                <p className="text-2xl font-bold text-white">{stats.failedGeneration}</p>
                <p className="text-xs text-gray-500">requires attention</p>
              </div>
              <AlertCircle className="w-8 h-8 text-red-400" />
            </div>
          </div>
        </div>
      )}

      {/* Quick Actions */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        <a
          href="/admin/content/ai-config"
          className="bg-zinc-800 border border-black rounded-lg p-4 hover:bg-zinc-700 transition-colors group"
        >
          <div className="flex items-center space-x-3">
            <Settings className="w-6 h-6 text-blue-400 group-hover:text-blue-300" />
            <div>
              <h3 className="font-medium text-white">AI Configuration</h3>
              <p className="text-sm text-gray-400">Manage AI providers</p>
            </div>
          </div>
        </a>

        <a
          href="/admin/content/queue"
          className="bg-zinc-800 border border-black rounded-lg p-4 hover:bg-zinc-700 transition-colors group"
        >
          <div className="flex items-center space-x-3">
            <Layers className="w-6 h-6 text-yellow-400 group-hover:text-yellow-300" />
            <div>
              <h3 className="font-medium text-white">Generation Queue</h3>
              <p className="text-sm text-gray-400">Monitor progress</p>
            </div>
          </div>
        </a>

        <a
          href="/admin/content/review"
          className="bg-zinc-800 border border-black rounded-lg p-4 hover:bg-zinc-700 transition-colors group"
        >
          <div className="flex items-center space-x-3">
            <FileText className="w-6 h-6 text-green-400 group-hover:text-green-300" />
            <div>
              <h3 className="font-medium text-white">Content Review</h3>
              <p className="text-sm text-gray-400">Editorial workflow</p>
            </div>
          </div>
        </a>

        <a
          href="/admin/content/prompts"
          className="bg-zinc-800 border border-black rounded-lg p-4 hover:bg-zinc-700 transition-colors group"
        >
          <div className="flex items-center space-x-3">
            <Zap className="w-6 h-6 text-purple-400 group-hover:text-purple-300" />
            <div>
              <h3 className="font-medium text-white">Prompt Management</h3>
              <p className="text-sm text-gray-400">Optimize prompts</p>
            </div>
          </div>
        </a>
      </div>

      {/* Generation Queue */}
      <div className="bg-zinc-800 border border-black rounded-lg">
        <div className="p-6 border-b border-zinc-700">
          <h2 className="text-lg font-semibold text-white">Generation Queue</h2>
          <p className="text-gray-400 text-sm">Current content generation jobs</p>
        </div>
        
        <div className="p-6">
          {queue.length === 0 ? (
            <div className="text-center py-8">
              <Layers className="w-12 h-12 text-gray-500 mx-auto mb-4" />
              <p className="text-gray-400">No items in queue</p>
            </div>
          ) : (
            <div className="space-y-4">
              {queue.map((item) => (
                <div
                  key={item.id}
                  className="flex items-center justify-between p-4 bg-zinc-700 rounded-lg"
                >
                  <div className="flex items-center space-x-4">
                    {getStatusIcon(item.status)}
                    <div>
                      <h3 className="font-medium text-white">{item.toolName}</h3>
                      <p className="text-sm text-gray-400">{item.url}</p>
                    </div>
                  </div>
                  
                  <div className="flex items-center space-x-4">
                    <span className={`text-sm font-medium ${getPriorityColor(item.priority)}`}>
                      {item.priority.toUpperCase()}
                    </span>
                    {item.estimatedTime && (
                      <span className="text-sm text-gray-400">{item.estimatedTime}</span>
                    )}
                    <span className="text-sm text-gray-500">
                      {new Date(item.createdAt).toLocaleTimeString()}
                    </span>
                  </div>
                </div>
              ))}
            </div>
          )}
        </div>
      </div>
    </div>
  );
}
