#!/usr/bin/env tsx

/**
 * Audit Trail API Endpoints Test
 * Tests the audit trail API endpoints for functionality
 */

import dotenv from 'dotenv';
dotenv.config({ path: '.env.local' });

const API_BASE_URL = process.env.NEXT_PUBLIC_SITE_URL || 'http://localhost:3000';
const API_KEY = process.env.ADMIN_API_KEY || 'aidude_admin_2024_secure_key_xyz789';

if (!API_KEY) {
  console.error('❌ ADMIN_API_KEY environment variable is required');
  process.exit(1);
}

async function testAuditAPIEndpoints() {
  console.log('🧪 Testing Audit Trail API Endpoints...');
  console.log('=' .repeat(60));
  console.log(`API Base URL: ${API_BASE_URL}`);
  console.log(`Using API Key: ${API_KEY!.substring(0, 8)}...`);
  console.log('');

  const headers: Record<string, string> = {
    'Content-Type': 'application/json',
    'Authorization': `Bearer ${API_KEY}`,
    'x-api-key': API_KEY
  };

  try {
    // Test 1: Create a manual audit log entry
    console.log('📝 Test 1: Creating manual audit log entry...');
    
    const auditLogData = {
      action: 'test_api_endpoint',
      resourceType: 'system',
      resourceId: 'api-test-001',
      resourceName: 'API Endpoint Test',
      performedBy: 'test-script',
      userRole: 'admin',
      sessionId: 'test-session-001',
      requestId: 'test-request-001',
      httpMethod: 'POST',
      endpoint: '/api/admin/audit',
      actionDetails: {
        test: true,
        timestamp: new Date().toISOString(),
        description: 'Testing audit trail API endpoints'
      },
      status: 'success',
      severity: 'low',
      category: 'system',
      tags: ['test', 'api'],
      isSensitive: false,
      complianceFlags: ['TEST']
    };

    const createResponse = await fetch(`${API_BASE_URL}/api/admin/audit`, {
      method: 'POST',
      headers,
      body: JSON.stringify(auditLogData)
    });

    if (!createResponse.ok) {
      throw new Error(`Create audit log failed: ${createResponse.status} ${createResponse.statusText}`);
    }

    const createResult = await createResponse.json();
    console.log(`✅ Audit log created with ID: ${createResult.data?.auditId}`);

    // Test 2: Retrieve audit logs
    console.log('\n📋 Test 2: Retrieving audit logs...');
    
    const getResponse = await fetch(`${API_BASE_URL}/api/admin/audit?page=1&limit=10&performedBy=test-script`, {
      method: 'GET',
      headers
    });

    if (!getResponse.ok) {
      throw new Error(`Get audit logs failed: ${getResponse.status} ${getResponse.statusText}`);
    }

    const getResult = await getResponse.json();
    console.log(`✅ Retrieved ${getResult.data?.logs?.length || 0} audit logs`);
    console.log(`   Total logs: ${getResult.data?.pagination?.total || 0}`);

    // Test 3: Search audit logs
    console.log('\n🔍 Test 3: Searching audit logs...');
    
    const searchResponse = await fetch(`${API_BASE_URL}/api/admin/audit/search?q=API Endpoint Test&resourceType=system&limit=5`, {
      method: 'GET',
      headers
    });

    if (!searchResponse.ok) {
      throw new Error(`Search audit logs failed: ${searchResponse.status} ${searchResponse.statusText}`);
    }

    const searchResult = await searchResponse.json();
    console.log(`✅ Found ${searchResult.data?.count || 0} matching audit logs`);

    // Test 4: Get audit statistics
    console.log('\n📊 Test 4: Getting audit statistics...');
    
    const statsResponse = await fetch(`${API_BASE_URL}/api/admin/audit/statistics?timeRange=24h&includeBreakdowns=true`, {
      method: 'GET',
      headers
    });

    if (!statsResponse.ok) {
      throw new Error(`Get audit statistics failed: ${statsResponse.status} ${statsResponse.statusText}`);
    }

    const statsResult = await statsResponse.json();
    console.log(`✅ Audit statistics retrieved:`);
    console.log(`   Total actions: ${statsResult.data?.totalActions || 0}`);
    console.log(`   Success rate: ${statsResult.data?.successRate || '0'}%`);
    console.log(`   Error rate: ${statsResult.data?.errorRate || 0}%`);

    // Test 5: Advanced search with POST
    console.log('\n🎯 Test 5: Advanced search with filters...');
    
    const advancedSearchData = {
      searchTerm: 'test',
      resourceType: 'system',
      status: 'success',
      severity: 'low',
      category: 'system',
      tags: ['test'],
      sortBy: 'performed_at',
      sortOrder: 'desc',
      page: 1,
      limit: 5
    };

    const advancedSearchResponse = await fetch(`${API_BASE_URL}/api/admin/audit/search`, {
      method: 'POST',
      headers,
      body: JSON.stringify(advancedSearchData)
    });

    if (!advancedSearchResponse.ok) {
      throw new Error(`Advanced search failed: ${advancedSearchResponse.status} ${advancedSearchResponse.statusText}`);
    }

    const advancedSearchResult = await advancedSearchResponse.json();
    console.log(`✅ Advanced search completed:`);
    console.log(`   Results: ${advancedSearchResult.data?.results?.length || 0}`);
    console.log(`   Total filtered: ${advancedSearchResult.data?.pagination?.total || 0}`);

    // Test 6: Session management (create session)
    console.log('\n👤 Test 6: Session management...');
    
    const sessionData = {
      userId: 'test-user',
      sessionId: 'test-session-api-001',
      loginTime: new Date().toISOString(),
      ipAddress: '127.0.0.1',
      userAgent: 'Test Script',
      loginMethod: 'api_key',
      status: 'active'
    };

    const sessionResponse = await fetch(`${API_BASE_URL}/api/admin/audit/sessions`, {
      method: 'POST',
      headers,
      body: JSON.stringify(sessionData)
    });

    if (!sessionResponse.ok) {
      throw new Error(`Session management failed: ${sessionResponse.status} ${sessionResponse.statusText}`);
    }

    const sessionResult = await sessionResponse.json();
    console.log(`✅ Session created/updated: ${sessionResult.data?.sessionId}`);

    // Test 7: Get sessions
    console.log('\n📋 Test 7: Retrieving sessions...');
    
    const getSessionsResponse = await fetch(`${API_BASE_URL}/api/admin/audit/sessions?page=1&limit=5&userId=test-user`, {
      method: 'GET',
      headers
    });

    if (!getSessionsResponse.ok) {
      throw new Error(`Get sessions failed: ${getSessionsResponse.status} ${getSessionsResponse.statusText}`);
    }

    const getSessionsResult = await getSessionsResponse.json();
    console.log(`✅ Retrieved ${getSessionsResult.data?.sessions?.length || 0} sessions`);

    // Test 8: Generate custom report
    console.log('\n📈 Test 8: Generating custom report...');
    
    const reportData = {
      timeRange: '24h',
      reportType: 'security',
      requestedBy: 'test-script'
    };

    const reportResponse = await fetch(`${API_BASE_URL}/api/admin/audit/statistics`, {
      method: 'POST',
      headers,
      body: JSON.stringify(reportData)
    });

    if (!reportResponse.ok) {
      throw new Error(`Custom report failed: ${reportResponse.status} ${reportResponse.statusText}`);
    }

    const reportResult = await reportResponse.json();
    console.log(`✅ Custom security report generated:`);
    console.log(`   Report type: ${reportResult.data?.reportType}`);
    console.log(`   Time range: ${reportResult.data?.timeRange}`);

    console.log('');
    console.log('🎉 All Audit Trail API Tests Passed!');
    console.log('');
    console.log('📋 Test Summary:');
    console.log('   ✅ Manual audit log creation');
    console.log('   ✅ Audit log retrieval with pagination');
    console.log('   ✅ Audit log search functionality');
    console.log('   ✅ Audit statistics generation');
    console.log('   ✅ Advanced search with filters');
    console.log('   ✅ Session management');
    console.log('   ✅ Session retrieval');
    console.log('   ✅ Custom report generation');
    console.log('');
    console.log('🔧 Audit Trail API Status: FULLY OPERATIONAL');

  } catch (error) {
    console.error('❌ Audit trail API test failed:', error);
    
    if (error instanceof Error) {
      console.error('Error details:', error.message);
    }
    
    console.log('');
    console.log('🔧 Troubleshooting:');
    console.log('   1. Ensure the development server is running (npm run dev)');
    console.log('   2. Check that ADMIN_API_KEY is set correctly');
    console.log('   3. Verify database connection and audit trail tables exist');
    console.log('   4. Check API endpoint implementations');
    
    process.exit(1);
  }
}

// Run the test
testAuditAPIEndpoints();
