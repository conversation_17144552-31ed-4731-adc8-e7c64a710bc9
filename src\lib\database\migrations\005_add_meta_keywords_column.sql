-- Migration 005: Add meta_keywords column to tools table
-- This migration adds the meta_keywords field that is required for the AI content generation system
-- as documented in the AI prompts implementation guide

-- Add meta_keywords column to tools table
ALTER TABLE tools ADD COLUMN IF NOT EXISTS meta_keywords TEXT;

-- Add comment for documentation
COMMENT ON COLUMN tools.meta_keywords IS 'SEO meta keywords for the tool page, comma-separated list of relevant keywords';

-- Create index for meta_keywords for search performance
CREATE INDEX IF NOT EXISTS idx_tools_meta_keywords ON tools USING gin(to_tsvector('english', meta_keywords));

-- Update existing tools with sample meta keywords based on their name and description
-- This is optional and can be removed in production
UPDATE tools 
SET meta_keywords = CASE 
  WHEN meta_keywords IS NULL OR meta_keywords = '' THEN
    LOWER(name) || ', ai tool, artificial intelligence, ' || 
    CASE 
      WHEN category_id IS NOT NULL THEN 
        (SELECT LOWER(name) FROM categories WHERE id = tools.category_id LIMIT 1)
      ELSE 'productivity'
    END || ', automation'
  ELSE meta_keywords
END
WHERE content_status = 'published' 
  AND name IS NOT NULL 
  AND (meta_keywords IS NULL OR meta_keywords = '');
