/**
 * Content Processor for Enhanced Scrape.do Integration
 * Main orchestrator for scraping, media collection, and content processing
 */

import {
  EnhancedScrapeRequest,
  EnhancedScrapeResult,
  ScrapeResult,
  ImageCollection,
  MultiPageScrapingConfig
} from './types';
import { scrapeDoClient } from './scrape-do-client';
import { costOptimizer } from './cost-optimizer';
import { ContentAnalyzer } from './content-analyzer';
import { mediaExtractor } from './media-extractor';
import { multiPageScraper } from './multi-page-scraper';
import { dataStorage } from './data-storage';
import { supabase } from '@/lib/supabase';

export class ContentProcessor {
  private contentAnalyzer: ContentAnalyzer;

  constructor() {
    this.contentAnalyzer = new ContentAnalyzer();
  }

  /**
   * Main enhanced scraping method that orchestrates all components
   */
  async processEnhancedScrape(request: EnhancedScrapeRequest): Promise<EnhancedScrapeResult> {
    const startTime = Date.now();
    console.log(`🚀 Starting enhanced scraping for: ${request.url}`);

    // Handle timeout if specified
    const timeout = request.options?.timeout;
    if (timeout) {
      return Promise.race([
        this.executeEnhancedScrape(request, startTime),
        new Promise<EnhancedScrapeResult>((_, reject) =>
          setTimeout(() => reject(new Error(`Request timeout after ${timeout}ms`)), timeout)
        )
      ]).catch(error => {
        if (error.message.includes('timeout')) {
          return this.createFailedResult(request.url, `Request timeout after ${timeout}ms`);
        }
        throw error;
      });
    }

    return this.executeEnhancedScrape(request, startTime);
  }

  /**
   * Execute the enhanced scraping workflow
   */
  private async executeEnhancedScrape(request: EnhancedScrapeRequest, startTime: number): Promise<EnhancedScrapeResult> {
    try {
      // Step 1: Main page scraping with cost optimization
      const mainResult = await this.scrapeMainPage(request);
      
      if (!mainResult.success) {
        return this.createFailedResult(request.url, mainResult.error || 'Main page scraping failed');
      }

      // Step 2: Content analysis
      const contentAnalysis = this.contentAnalyzer.analyzeContentQuality(mainResult.content, request.url);
      
      // Step 3: Media collection (if enabled)
      let mediaAssets: ImageCollection | undefined;
      if (request.mediaCollection !== false) {
        mediaAssets = await this.collectMediaAssets(request.url, mainResult.content);
      }

      // Step 4: Multi-page scraping (enabled by default unless explicitly disabled)
      let additionalPages: ScrapeResult[] = [];
      if (request.multiPageConfig?.enabled !== false) {
        const config = request.multiPageConfig || {
          enabled: true,
          mode: 'conditional' as const,
          maxPagesPerTool: 3,
          creditThreshold: 10,
          pageTypes: {
            pricing: { enabled: true, priority: 'high' as const, patterns: ['/pricing', '/plans'], selectors: [], required: false },
            faq: { enabled: true, priority: 'medium' as const, patterns: ['/faq', '/help'], selectors: [], required: false },
            features: { enabled: true, priority: 'medium' as const, patterns: ['/features'], selectors: [], required: false },
            about: { enabled: true, priority: 'low' as const, patterns: ['/about'], selectors: [], required: false }
          },
          fallbackStrategy: { searchInMainPage: true, useNavigation: true, useSitemap: false }
        };
        additionalPages = await this.processMultiPageScraping(request.url, mainResult.content, config);
      }

      // Step 5: Content validation
      const validation = this.contentAnalyzer.validateContentQuality(mainResult.content, request.url);
      
      // Step 6: Calculate cost analysis
      const costAnalysis = this.calculateCostAnalysis(mainResult, additionalPages);

      const processingTime = Date.now() - startTime;
      console.log(`✅ Enhanced scraping completed in ${processingTime}ms`);

      const result: EnhancedScrapeResult = {
        ...mainResult,
        mediaAssets,
        additionalPages,
        costAnalysis,
        contentAnalysis,
        metadata: {
          ...mainResult.metadata,
          processingTime,
          validation: validation.isValid,
          qualityScore: validation.qualityScore
        }
      };

      // Step 7: Store scraped content for future AI processing (if enabled)
      if (request.persistentStorage !== false) {
        try {
          // Store in database
          await this.storeInDatabase(result);

          // Store in file system
          await this.storeInFileSystem(result);

          // Also use the data storage service
          const storageResult = await dataStorage.storeScrapedContent(result);
          if (storageResult.success) {
            console.log(`📁 Content stored: ${storageResult.filePath}`);
          } else {
            console.warn(`⚠️ Storage failed: ${storageResult.error}`);
          }
        } catch (error) {
          console.warn('Storage error:', error);
        }
      }

      return result;

    } catch (error) {
      console.error('Enhanced scraping failed:', error);
      return this.createFailedResult(request.url, (error as Error).message);
    }
  }

  /**
   * Scrape main page with intelligent cost optimization
   */
  private async scrapeMainPage(request: EnhancedScrapeRequest): Promise<ScrapeResult> {
    if (request.costOptimization !== false) {
      // Use cost optimizer for intelligent scraping
      return await costOptimizer.scrapeWithMaxCostOptimization(request.url);
    } else {
      // Direct scraping with provided options
      return await scrapeDoClient.scrapePage(request.url, request.options);
    }
  }

  /**
   * Collect media assets (favicon, OG images, screenshots)
   */
  private async collectMediaAssets(url: string, content: string): Promise<ImageCollection | undefined> {
    try {
      return await mediaExtractor.collectImagesWithPriority(url, content);
    } catch (error) {
      console.error('Media collection failed:', error);
      // Return undefined when media collection fails (as expected by tests)
      return undefined;
    }
  }

  /**
   * Process multi-page scraping if configured
   */
  private async processMultiPageScraping(
    mainUrl: string,
    mainContent: string,
    config: MultiPageScrapingConfig
  ): Promise<ScrapeResult[]> {
    try {
      // Update multi-page scraper configuration
      multiPageScraper.updateConfig(config);

      // Discover and plan scraping
      const decision = await multiPageScraper.discoverAndPlanScraping(mainUrl, mainContent);
      
      console.log(`Multi-page decision: ${decision.reason}`);
      
      // Execute scraping for immediate pages
      if (decision.scrapeNow.length > 0) {
        return await multiPageScraper.executeMultiPageScraping(decision);
      }

      return [];
    } catch (error) {
      console.error('Multi-page scraping failed:', error);
      return [];
    }
  }

  /**
   * Calculate cost analysis for the scraping operation
   */
  private calculateCostAnalysis(mainResult: ScrapeResult, additionalPages: ScrapeResult[]) {
    const mainCredits = mainResult.metadata?.creditsUsed || 0;
    const additionalCredits = additionalPages.reduce((sum, page) => sum + (page.metadata?.creditsUsed || 0), 0);
    const totalCredits = mainCredits + additionalCredits;

    // Determine optimization strategy based on request type and proxy type
    const requestType = mainResult.metadata?.requestType || 'Unknown';
    const proxyType = mainResult.metadata?.proxyType || 'datacenter';
    const browserEnabled = mainResult.metadata?.browserEnabled || false;

    let optimizationStrategy = 'Basic Datacenter';

    if (browserEnabled && proxyType === 'residential') {
      optimizationStrategy = 'Residential + Browser (Premium)';
    } else if (browserEnabled && proxyType === 'datacenter') {
      optimizationStrategy = 'Datacenter + Browser';
    } else if (proxyType === 'residential') {
      optimizationStrategy = 'Residential Proxy';
    } else {
      optimizationStrategy = 'Datacenter Proxy (Cost-Optimized)';
    }

    // Estimate savings based on optimization strategy
    const estimatedSavings = this.estimateSavings(mainResult, additionalPages);

    return {
      creditsUsed: totalCredits,
      estimatedSavings,
      optimizationStrategy,
      requestType,
      mainPageCredits: mainCredits,
      additionalPagesCredits: additionalCredits
    };
  }

  /**
   * Estimate cost savings from optimization
   */
  private estimateSavings(mainResult: ScrapeResult, additionalPages: ScrapeResult[]): number {
    // Calculate savings based on optimization patterns used
    let savings = 0;

    // Main page savings
    if (mainResult.metadata?.requestType === 'Datacenter Proxy') {
      savings += 4; // Saved 4 credits by not using enhanced scraping
    }

    // Additional pages savings
    additionalPages.forEach(page => {
      if (page.metadata?.requestType === 'Datacenter Proxy') {
        savings += 4;
      }
    });

    return savings;
  }

  /**
   * Store scraped content in database
   */
  private async storeInDatabase(result: EnhancedScrapeResult): Promise<void> {
    try {
      const { data, error } = await supabase
        .from('scraped_content')
        .insert({
          url: result.url,
          content: result.content,
          success: result.success,
          timestamp: result.timestamp,
          credits_used: result.costAnalysis?.creditsUsed || 0,
          metadata: result.metadata
        })
        .single();

      if (error) {
        console.warn('Database storage failed:', error);
      } else {
        console.log('Content stored in database:', data);
      }
    } catch (error) {
      console.warn('Database storage error:', error);
    }
  }

  /**
   * Store scraped content in file system
   */
  private async storeInFileSystem(result: EnhancedScrapeResult): Promise<void> {
    try {
      const fs = await import('fs/promises');
      const path = await import('path');

      const sanitizedUrl = result.url?.replace(/[^a-zA-Z0-9]/g, '_') || 'unknown';
      const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
      const filename = `scraped_${sanitizedUrl}_${timestamp}.md`;

      const filePath = path.join(process.cwd(), 'data', 'scraped-content', filename);

      // Ensure directory exists
      await fs.mkdir(path.dirname(filePath), { recursive: true });

      // Prepare content with metadata
      const fileContent = `# Scraped Content: ${result.url}

## Metadata
- Scraped at: ${result.timestamp}
- Success: ${result.success}
- Credits used: ${result.costAnalysis?.creditsUsed || 0}

## Content
${result.content}
`;

      await fs.writeFile(filePath, fileContent, 'utf-8');
      console.log(`Content stored in file system: ${filePath}`);
    } catch (error) {
      console.warn('File system storage error:', error);
    }
  }

  /**
   * Create a failed result object
   */
  private createFailedResult(url: string, error: string): EnhancedScrapeResult {
    return {
      success: false,
      content: '',
      error,
      timestamp: new Date().toISOString(),
      url,
      metadata: {
        creditsUsed: 0,
        requestType: 'failed'
      }
    };
  }

  /**
   * Process content for AI consumption
   */
  optimizeContentForAI(content: string): string {
    // Remove excessive whitespace and navigation elements
    content = content.replace(/\n{3,}/g, '\n\n');
    content = content.replace(/^(Navigation|Menu|Footer|Header)[\s\S]*?(?=\n#|\n\n|$)/gm, '');

    // Clean HTML artifacts and normalize headers
    content = content.replace(/<[^>]*>/g, '');
    content = content.replace(/^#{4,}/gm, '###');

    // Ensure content fits within token limits (reserve 20% for response)
    const maxLength = 50000; // ~12-15K tokens for Gemini input
    if (content.length > maxLength) {
      content = content.substring(0, maxLength) + '\n\n[Content truncated for AI processing]';
    }

    return content.trim();
  }

  /**
   * Batch processing for multiple URLs
   */
  async processBatch(urls: string[], options: {
    costOptimization?: boolean;
    mediaCollection?: boolean;
    multiPageScraping?: boolean;
    batchSize?: number;
  } = {}): Promise<EnhancedScrapeResult[]> {
    const {
      costOptimization = true,
      mediaCollection = false, // Disabled by default for batch processing
      multiPageScraping = false, // Disabled by default for batch processing
      batchSize = 5
    } = options;

    const results: EnhancedScrapeResult[] = [];
    
    // Process URLs in batches to avoid overwhelming the API
    for (let i = 0; i < urls.length; i += batchSize) {
      const batch = urls.slice(i, i + batchSize);
      console.log(`Processing batch ${Math.floor(i / batchSize) + 1}/${Math.ceil(urls.length / batchSize)}`);

      const batchPromises = batch.map(url => 
        this.processEnhancedScrape({
          url,
          options: {},
          costOptimization,
          mediaCollection,
          multiPageConfig: multiPageScraping ? undefined : {
            enabled: false,
            mode: 'conditional' as const,
            maxPagesPerTool: 0,
            creditThreshold: 0,
            pageTypes: {
              pricing: { enabled: false, priority: 'low' as const, patterns: [], selectors: [], required: false },
              faq: { enabled: false, priority: 'low' as const, patterns: [], selectors: [], required: false },
              features: { enabled: false, priority: 'low' as const, patterns: [], selectors: [], required: false },
              about: { enabled: false, priority: 'low' as const, patterns: [], selectors: [], required: false }
            },
            fallbackStrategy: { searchInMainPage: false, useNavigation: false, useSitemap: false }
          }
        })
      );

      const batchResults = await Promise.allSettled(batchPromises);
      
      batchResults.forEach((result, index) => {
        if (result.status === 'fulfilled') {
          results.push(result.value);
        } else {
          console.error(`Batch processing failed for ${batch[index]}:`, result.reason);
          results.push(this.createFailedResult(batch[index], result.reason));
        }
      });

      // Add delay between batches to respect rate limits
      if (i + batchSize < urls.length) {
        await new Promise(resolve => setTimeout(resolve, 2000));
      }
    }

    return results;
  }

  /**
   * Save scraped content to file system (for later AI processing)
   */
  async saveScrapedContent(result: EnhancedScrapeResult, filename?: string): Promise<string> {
    const fs = await import('fs/promises');
    const path = await import('path');

    const sanitizedUrl = result.url?.replace(/[^a-zA-Z0-9]/g, '_') || 'unknown';
    const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
    const finalFilename = filename || `scraped_${sanitizedUrl}_${timestamp}.md`;
    
    const filePath = path.join(process.cwd(), 'data', 'scraped-content', finalFilename);
    
    // Ensure directory exists
    await fs.mkdir(path.dirname(filePath), { recursive: true });
    
    // Prepare content with metadata
    const fileContent = `# Scraped Content: ${result.url}

## Metadata
- Scraped at: ${result.timestamp}
- Success: ${result.success}
- Credits used: ${result.costAnalysis?.creditsUsed || 0}
- Quality score: ${result.metadata?.qualityScore || 'N/A'}

## Content Analysis
- Has substantial content: ${result.contentAnalysis?.hasSubstantialContent}
- Has structure: ${result.contentAnalysis?.hasStructure}
- Scenario: ${result.contentAnalysis?.scenario}

## Main Content
${result.content}

${result.additionalPages?.length ? `
## Additional Pages
${result.additionalPages.map(page => `
### ${page.metadata?.pageType || 'Unknown'} Page
${page.content}
`).join('\n')}
` : ''}
`;

    await fs.writeFile(filePath, fileContent, 'utf-8');
    console.log(`Scraped content saved to: ${filePath}`);
    
    return filePath;
  }
}

// Export singleton instance
export const contentProcessor = new ContentProcessor();
