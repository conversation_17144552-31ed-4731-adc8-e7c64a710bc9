module.exports = {
  presets: [
    'next/babel',
    ['@babel/preset-env', { targets: { node: 'current' } }],
    ['@babel/preset-typescript', { jsx: 'preserve' }],
    ['@babel/preset-react', { runtime: 'automatic' }]
  ],
  plugins: [
    '@babel/plugin-transform-class-properties',
    '@babel/plugin-transform-object-rest-spread'
  ],
  env: {
    test: {
      presets: [
        'next/babel',
        ['@babel/preset-env', { targets: { node: 'current' } }],
        ['@babel/preset-typescript', { jsx: 'preserve' }],
        ['@babel/preset-react', { runtime: 'automatic' }]
      ]
    }
  }
}
