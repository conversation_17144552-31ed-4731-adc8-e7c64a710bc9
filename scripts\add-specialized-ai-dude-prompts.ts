/**
 * Add Specialized AI Dude Prompt Templates
 * 
 * This script adds the specialized prompt templates for features, pricing, 
 * pros/cons, SEO, and FAQ generation as specified in the implementation guide.
 */

import { createClient } from '@supabase/supabase-js';
import { config } from 'dotenv';

// Load environment variables
config({ path: '.env.local' });

// Initialize Supabase client
const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL!;
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY!;

if (!supabaseUrl || !supabaseServiceKey) {
  throw new Error('Missing required Supabase environment variables');
}

const supabase = createClient(supabaseUrl, supabaseServiceKey, {
  auth: {
    autoRefreshToken: false,
    persistSession: false
  }
});

async function addSpecializedPrompts() {
  console.log('🚀 Adding specialized AI Dude prompt templates...');

  try {
    const specializedTemplates = [
      {
        config_key: 'prompt_ai_dude_features',
        config_value: {
          name: 'AI Dude Features Generation with Context',
          description: 'Generate enhanced features section with existing tool context',
          category: 'features',
          promptType: 'user',
          template: `You are "AI Dude." Generate 3-8 key features for this tool in your snarky style.

**Existing Tool Data:**
{existingToolData}

**New Scraped Content:**
{scrapedContent}

**Tool URL:** {toolUrl}

**Instructions:**
- Generate comprehensive features list (3-8 items)
- Use existing tool context for consistency
- Make each feature specific and actionable
- Keep the irreverent AI Dude tone
- Focus on what makes this tool unique

Output JSON: {"features": ["feature1", "feature2", ...]}`,
          variables: ['existingToolData', 'scrapedContent', 'toolUrl'],
          validationRules: ['3-8 features required', 'Specific and actionable', 'Consistent tone'],
          formatRequirements: 'JSON object with features array',
          usage: 0
        },
        config_type: 'prompt_template',
        description: 'AI Dude features generation with context'
      },
      {
        config_key: 'prompt_ai_dude_pricing',
        config_value: {
          name: 'AI Dude Pricing Generation with Context',
          description: 'Generate comprehensive pricing section with existing tool context',
          category: 'pricing',
          promptType: 'user',
          template: `You are "AI Dude." Analyze and generate pricing information for this tool.

**Existing Tool Data:**
{existingToolData}

**New Scraped Content:**
{scrapedContent}

**Tool URL:** {toolUrl}

**Instructions:**
- Determine pricing type: Free, Paid, Freemium, or Open Source
- Create detailed pricing plans if available
- Include pricing description in AI Dude style
- Be accurate but entertaining
- Note any free trials or special offers

Output JSON: {"pricing": {"type": "Free|Paid|Freemium|Open Source", "plans": [...], "details": "..."}}`,
          variables: ['existingToolData', 'scrapedContent', 'toolUrl'],
          validationRules: ['Accurate pricing type', 'Detailed plans when available', 'AI Dude style'],
          formatRequirements: 'JSON object with pricing structure',
          usage: 0
        },
        config_type: 'prompt_template',
        description: 'AI Dude pricing generation with context'
      },
      {
        config_key: 'prompt_ai_dude_pros_cons',
        config_value: {
          name: 'AI Dude Pros/Cons Generation with Context',
          description: 'Generate balanced pros and cons with existing tool context',
          category: 'pros_cons',
          promptType: 'user',
          template: `You are "AI Dude." Generate 3-10 pros and cons for this tool in your irreverent style.

**Existing Tool Data:**
{existingToolData}

**New Scraped Content:**
{scrapedContent}

**Tool URL:** {toolUrl}

**Instructions:**
- Generate 3-10 honest pros (what's genuinely good)
- Generate 3-10 honest cons (what could be better)
- Balance positive and negative aspects
- Use specific, actionable points
- Maintain snarky but fair AI Dude tone
- Consider user experience insights

Output JSON: {"pros_and_cons": {"pros": [...], "cons": [...]}}`,
          variables: ['existingToolData', 'scrapedContent', 'toolUrl'],
          validationRules: ['3-10 pros and cons each', 'Balanced assessment', 'Specific points'],
          formatRequirements: 'JSON object with pros_and_cons structure',
          usage: 0
        },
        config_type: 'prompt_template',
        description: 'AI Dude pros/cons generation with context'
      },
      {
        config_key: 'prompt_ai_dude_seo',
        config_value: {
          name: 'AI Dude SEO Content Generation with Context',
          description: 'Generate SEO-optimized meta content with existing tool context',
          category: 'seo',
          promptType: 'user',
          template: `You are "AI Dude." Generate SEO-optimized meta content for this tool.

**Existing Tool Data:**
{existingToolData}

**New Scraped Content:**
{scrapedContent}

**Tool URL:** {toolUrl}

**Instructions:**
- Create compelling meta_title (max 60 characters, SEO-optimized)
- Write engaging meta_description (150-160 characters, includes CTA)
- Maintain AI Dude personality while being search-friendly
- Include relevant keywords naturally
- Make it click-worthy but accurate

Output JSON: {"meta_title": "...", "meta_description": "..."}`,
          variables: ['existingToolData', 'scrapedContent', 'toolUrl'],
          validationRules: ['60 char title limit', '150-160 char description', 'SEO optimized'],
          formatRequirements: 'JSON object with meta fields',
          usage: 0
        },
        config_type: 'prompt_template',
        description: 'AI Dude SEO generation with context'
      },
      {
        config_key: 'prompt_ai_dude_faqs',
        config_value: {
          name: 'AI Dude FAQ Generation with Context',
          description: 'Generate comprehensive FAQs with existing tool context',
          category: 'faqs',
          promptType: 'user',
          template: `You are "AI Dude." Generate 3-5 relevant FAQs for this tool in your signature style.

**Existing Tool Data:**
{existingToolData}

**New Scraped Content:**
{scrapedContent}

**Tool URL:** {toolUrl}

**Instructions:**
- Create 3-5 frequently asked questions
- Cover different categories: general, pricing, features, support
- Write answers in AI Dude's irreverent but helpful style
- Make questions realistic and useful
- Include proper FAQ metadata structure

Output JSON: {"faqs": [{"id": "uuid", "question": "...", "answer": "...", "category": "general|pricing|features|support", "displayOrder": 0, "priority": 5, "isActive": true, "source": "ai_generated", "sourceMetadata": {"aiModel": "ai_dude", "confidence": 0.9}}]}`,
          variables: ['existingToolData', 'scrapedContent', 'toolUrl'],
          validationRules: ['3-5 FAQs required', 'Complete metadata structure', 'Realistic questions'],
          formatRequirements: 'JSON object with complete FAQ structure',
          usage: 0
        },
        config_type: 'prompt_template',
        description: 'AI Dude FAQ generation with context'
      }
    ];

    // Insert specialized templates
    for (const template of specializedTemplates) {
      const result = await supabase
        .from('system_configuration')
        .insert(template);

      if (result.error) {
        console.error(`❌ Error inserting ${template.config_key}:`, result.error);
      } else {
        console.log(`✅ ${template.config_key} inserted successfully`);
      }
    }

    // Verify all templates were inserted
    const { data: templates, error: fetchError } = await supabase
      .from('system_configuration')
      .select('config_key, config_value')
      .like('config_key', 'prompt_ai_dude%')
      .order('config_key');

    if (fetchError) {
      console.error('❌ Error fetching templates:', fetchError);
    } else {
      console.log('\n📋 All AI Dude templates:');
      templates?.forEach(template => {
        console.log(`  - ${template.config_key}: ${template.config_value.name}`);
      });
    }

    console.log('\n🎉 Specialized AI Dude prompt templates added successfully!');

  } catch (error: any) {
    console.error('❌ Error adding specialized prompts:', error.message);
    throw error;
  }
}

// Run the script
addSpecializedPrompts()
  .then(() => {
    console.log('✅ Script completed successfully');
    process.exit(0);
  })
  .catch((error) => {
    console.error('❌ Script failed:', error);
    process.exit(1);
  });
