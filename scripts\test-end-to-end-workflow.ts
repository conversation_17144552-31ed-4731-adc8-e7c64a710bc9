#!/usr/bin/env tsx

import * as dotenv from 'dotenv';
import { createClient } from '@supabase/supabase-js';
import { submissionTransformer } from '../src/lib/submission-transformer';

// Load environment variables
dotenv.config({ path: '.env.local' });

// Create Supabase client with service role
const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL!;
const supabaseKey = process.env.SUPABASE_SERVICE_ROLE_KEY!;
const supabase = createClient(supabaseUrl, supabaseKey);

async function testEndToEndWorkflow() {
  console.log('🧪 Testing End-to-End Dual Submission Workflow...\n');

  const testResults = {
    simpleSubmission: false,
    detailedSubmission: false,
    transformation: false,
    cleanup: false
  };

  try {
    // 1. Test Simple Submission Flow
    console.log('1️⃣ Testing Simple Submission Flow:');
    
    const simpleSubmission = {
      name: 'E2E Simple Test Tool',
      url: 'https://e2e-simple-test.com',
      description: 'End-to-end test tool for simple submission workflow validation',
      category: 'productivity-ai',
      submitter_name: 'E2E Tester',
      submitter_email: '<EMAIL>',
      submission_type: 'simple',
      status: 'pending',
      submitted_at: new Date().toISOString()
    };

    const { data: simpleData, error: simpleError } = await supabase
      .from('tool_submissions')
      .insert(simpleSubmission)
      .select()
      .single();

    if (simpleError) {
      console.log(`   ❌ Simple submission creation failed: ${simpleError.message}`);
    } else {
      console.log(`   ✅ Simple submission created: ${simpleData.id}`);
      
      // Simulate admin approval
      const { error: approvalError } = await supabase
        .from('tool_submissions')
        .update({
          status: 'approved',
          reviewed_by: 'e2e-admin',
          reviewed_at: new Date().toISOString()
        })
        .eq('id', simpleData.id);

      if (approvalError) {
        console.log(`   ❌ Simple approval failed: ${approvalError.message}`);
      } else {
        console.log(`   ✅ Simple submission approved`);
        testResults.simpleSubmission = true;
      }
    }

    // 2. Test Detailed Submission Flow
    console.log('\n2️⃣ Testing Detailed Submission Flow:');
    
    const detailedSubmission = {
      name: 'E2E Detailed Test Tool',
      url: 'https://e2e-detailed-test.com',
      description: 'End-to-end test tool for detailed submission workflow',
      detailed_description: 'This is a comprehensive test tool designed to validate the detailed submission workflow. It includes all necessary fields for immediate publication.',
      category: 'writing-tools',
      subcategory: 'Content Generation',
      features: '• Advanced AI algorithms\n• Real-time processing\n• Multi-language support\n• User-friendly interface',
      pricing_type: 'freemium',
      pricing_details: 'Free tier: 100 requests/month\nPro tier: $29/month for unlimited requests',
      pros: '• Fast and accurate\n• Easy to use\n• Great customer support\n• Regular updates',
      cons: '• Limited free tier\n• Requires internet connection\n• Learning curve for advanced features',
      meta_title: 'E2E Detailed Test Tool - AI-Powered Content Assistant',
      meta_description: 'Test your content creation with our AI-powered detailed test tool. Features advanced algorithms and real-time processing.',
      faq: 'Q: How does it work?\nA: Our AI processes your input and provides intelligent suggestions.\n\nQ: Is there a free trial?\nA: Yes, we offer a free tier with 100 requests per month.',
      submitter_name: 'E2E Detailed Tester',
      submitter_email: '<EMAIL>',
      submission_type: 'detailed',
      status: 'pending',
      submitted_at: new Date().toISOString()
    };

    const { data: detailedData, error: detailedError } = await supabase
      .from('tool_submissions')
      .insert(detailedSubmission)
      .select()
      .single();

    if (detailedError) {
      console.log(`   ❌ Detailed submission creation failed: ${detailedError.message}`);
    } else {
      console.log(`   ✅ Detailed submission created: ${detailedData.id}`);
      testResults.detailedSubmission = true;
    }

    // 3. Test Transformation Service
    console.log('\n3️⃣ Testing Transformation Service:');
    
    if (detailedData) {
      // Approve the detailed submission
      const { error: detailedApprovalError } = await supabase
        .from('tool_submissions')
        .update({
          status: 'approved',
          reviewed_by: 'e2e-admin',
          reviewed_at: new Date().toISOString()
        })
        .eq('id', detailedData.id);

      if (detailedApprovalError) {
        console.log(`   ❌ Detailed approval failed: ${detailedApprovalError.message}`);
      } else {
        console.log(`   ✅ Detailed submission approved`);
        
        // Test transformation
        const transformResult = await submissionTransformer.createToolFromSubmission(detailedData.id);
        
        if (transformResult.success) {
          console.log(`   ✅ Tool created from detailed submission: ${transformResult.toolId}`);
          testResults.transformation = true;
          
          // Verify the tool was created correctly
          const { data: createdTool, error: toolError } = await supabase
            .from('tools')
            .select('*')
            .eq('id', transformResult.toolId)
            .single();

          if (toolError) {
            console.log(`   ❌ Failed to verify created tool: ${toolError.message}`);
          } else {
            console.log(`   ✅ Tool verification successful:`);
            console.log(`      Name: ${createdTool.name}`);
            console.log(`      Status: ${createdTool.content_status}`);
            console.log(`      Submission Type: ${createdTool.submission_type}`);
            console.log(`      Features: ${createdTool.features ? 'Present' : 'Missing'}`);
            console.log(`      Pricing: ${createdTool.pricing_type || 'Not set'}`);
            console.log(`      FAQs: ${createdTool.faqs ? 'Present' : 'Missing'}`);
          }
        } else {
          console.log(`   ❌ Transformation failed: ${transformResult.error}`);
        }
      }
    }

    // 4. Test Workflow Differentiation
    console.log('\n4️⃣ Testing Workflow Differentiation:');
    
    if (simpleData && detailedData) {
      const result = await submissionTransformer.processApprovedSubmission(simpleData.id);
      console.log(`   📋 Simple submission processing: ${result.message}`);
      
      const detailedResult = await submissionTransformer.processApprovedSubmission(detailedData.id);
      console.log(`   📋 Detailed submission processing: ${detailedResult.message}`);
    }

    // 5. Cleanup Test Data
    console.log('\n5️⃣ Cleaning up test data:');
    
    const testIds = [simpleData?.id, detailedData?.id].filter(Boolean);
    if (testIds.length > 0) {
      const { error: submissionCleanupError } = await supabase
        .from('tool_submissions')
        .delete()
        .in('id', testIds);

      if (submissionCleanupError) {
        console.log(`   ❌ Submission cleanup failed: ${submissionCleanupError.message}`);
      } else {
        console.log(`   🧹 Test submissions cleaned up`);
      }
    }

    // Cleanup test tools
    const { error: toolCleanupError } = await supabase
      .from('tools')
      .delete()
      .in('name', ['E2E Simple Test Tool', 'E2E Detailed Test Tool']);

    if (toolCleanupError) {
      console.log(`   ❌ Tool cleanup failed: ${toolCleanupError.message}`);
    } else {
      console.log(`   🧹 Test tools cleaned up`);
      testResults.cleanup = true;
    }

    // 6. Test Results Summary
    console.log('\n6️⃣ Test Results Summary:');
    
    const passedTests = Object.values(testResults).filter(Boolean).length;
    const totalTests = Object.keys(testResults).length;
    const successRate = Math.round((passedTests / totalTests) * 100);

    console.log(`   📊 Tests Passed: ${passedTests}/${totalTests} (${successRate}%)`);
    
    Object.entries(testResults).forEach(([test, passed]) => {
      const status = passed ? '✅ PASS' : '❌ FAIL';
      console.log(`   ${status} ${test}`);
    });

    if (successRate === 100) {
      console.log('\n🎉 All tests passed! Dual submission system is working correctly.');
      return true;
    } else if (successRate >= 75) {
      console.log('\n⚠️  Most tests passed, but some issues need attention.');
      return false;
    } else {
      console.log('\n❌ Multiple test failures - system needs significant fixes.');
      return false;
    }

  } catch (error) {
    console.error('❌ End-to-end test failed:', error);
    return false;
  }
}

// Run the test
testEndToEndWorkflow()
  .then((success) => {
    console.log('\n🎉 End-to-end workflow test complete!');
    process.exit(success ? 0 : 1);
  })
  .catch((error) => {
    console.error('❌ Test failed:', error);
    process.exit(1);
  });
