'use client';

import React, { useState, useEffect } from 'react';

interface PartialContentGenerationProps {
  toolId: string;
  toolName: string;
  existingContent: any;
  onGenerationComplete: (updatedContent: any, sections: string[]) => void;
  onError: (error: string) => void;
  className?: string;
  bulkMode?: boolean;
  selectedTools?: string[];
  onBulkComplete?: (results: any[]) => void;
}

interface ContentSection {
  key: string;
  label: string;
  description: string;
  templateType: string;
  selected: boolean;
  hasExistingContent: boolean;
  preview?: string;
}

interface GenerationProgress {
  isGenerating: boolean;
  currentSection?: string;
  progress: number;
  completedSections: string[];
  error?: string;
}

const CONTENT_SECTIONS: Omit<ContentSection, 'selected' | 'hasExistingContent' | 'preview'>[] = [
  {
    key: 'features',
    label: 'Features',
    description: 'Key features and capabilities',
    templateType: 'ai_dude_partial_features'
  },
  {
    key: 'pricing',
    label: 'Pricing',
    description: 'Pricing plans and cost information',
    templateType: 'ai_dude_partial_pricing'
  },
  {
    key: 'pros_and_cons',
    label: 'Pros & Cons',
    description: 'Advantages and disadvantages',
    templateType: 'ai_dude_partial_pros_cons'
  },
  {
    key: 'meta_title,meta_description,meta_keywords',
    label: 'SEO Content',
    description: 'SEO title, description, and keywords',
    templateType: 'ai_dude_partial_seo'
  },
  {
    key: 'faqs',
    label: 'FAQs',
    description: 'Frequently asked questions',
    templateType: 'ai_dude_partial_faq'
  },
  {
    key: 'detailed_description',
    label: 'General Content',
    description: 'General descriptions and content',
    templateType: 'ai_dude_partial_general'
  },
  {
    key: 'releases',
    label: 'Release History',
    description: 'Version history and release notes',
    templateType: 'ai_dude_releases'
  }
];

export function PartialContentGeneration({
  toolId,
  toolName,
  existingContent,
  onGenerationComplete,
  onError,
  className = '',
  bulkMode = false,
  selectedTools = [],
  onBulkComplete
}: PartialContentGenerationProps) {
  const [sections, setSections] = useState<ContentSection[]>([]);
  const [progress, setProgress] = useState<GenerationProgress>({
    isGenerating: false,
    progress: 0,
    completedSections: []
  });
  const [previewMode, setPreviewMode] = useState(false);
  const [generatedContent, setGeneratedContent] = useState<any>(null);

  useEffect(() => {
    // Initialize sections with existing content status
    const initializedSections = CONTENT_SECTIONS.map(section => ({
      ...section,
      selected: false,
      hasExistingContent: checkHasExistingContent(section.key, existingContent),
      preview: getContentPreview(section.key, existingContent)
    }));
    setSections(initializedSections);
  }, [existingContent]);

  const checkHasExistingContent = (sectionKey: string, content: any): boolean => {
    const keys = sectionKey.split(',');
    return keys.some(key => {
      const value = content[key];
      if (Array.isArray(value)) {
        return value.length > 0;
      }
      if (typeof value === 'object' && value !== null) {
        return Object.keys(value).length > 0;
      }
      return value && value.toString().trim().length > 0;
    });
  };

  const getContentPreview = (sectionKey: string, content: any): string => {
    const keys = sectionKey.split(',');
    const previews = keys.map(key => {
      const value = content[key];
      if (Array.isArray(value)) {
        return `${value.length} items`;
      }
      if (typeof value === 'object' && value !== null) {
        return 'Object data';
      }
      if (value && typeof value === 'string') {
        return value.length > 50 ? `${value.substring(0, 50)}...` : value;
      }
      return 'No content';
    });
    return previews.join(', ');
  };

  const handleSectionToggle = (sectionKey: string) => {
    setSections(prev => prev.map(section => 
      section.key === sectionKey 
        ? { ...section, selected: !section.selected }
        : section
    ));
  };

  const handleSelectAll = () => {
    const hasUnselected = sections.some(s => !s.selected);
    setSections(prev => prev.map(section => ({
      ...section,
      selected: hasUnselected
    })));
  };

  const handleStartGeneration = async () => {
    const selectedSections = sections.filter(s => s.selected);

    if (selectedSections.length === 0) {
      onError('Please select at least one section to regenerate');
      return;
    }

    try {
      setProgress({
        isGenerating: true,
        progress: 0,
        completedSections: []
      });

      if (bulkMode && selectedTools && selectedTools.length > 0) {
        // Bulk processing mode
        await handleBulkGeneration(selectedSections);
      } else {
        // Single tool mode
        await handleSingleToolGeneration(selectedSections);
      }

    } catch (error) {
      console.error('Error generating partial content:', error);
      const errorMessage = error instanceof Error ? error.message : 'Failed to generate content';
      setProgress(prev => ({
        ...prev,
        error: errorMessage
      }));
      onError(errorMessage);
    }
  };

  const handleSingleToolGeneration = async (selectedSections: ContentSection[]) => {
    const results: any = { ...existingContent };

    for (let i = 0; i < selectedSections.length; i++) {
      const section = selectedSections[i];

      setProgress(prev => ({
        ...prev,
        currentSection: section.label,
        progress: Math.round((i / selectedSections.length) * 100)
      }));

      // Generate content for this section
      const sectionResult = await generateSectionContent(section, existingContent);

      // Merge results
      const sectionKeys = section.key.split(',');
      sectionKeys.forEach(key => {
        if (sectionResult[key] !== undefined) {
          results[key] = sectionResult[key];
        }
      });

      setProgress(prev => ({
        ...prev,
        completedSections: [...prev.completedSections, section.key]
      }));
    }

    setProgress(prev => ({
      ...prev,
      progress: 100,
      currentSection: undefined
    }));

    setGeneratedContent(results);
    setPreviewMode(true);
  };

  const handleBulkGeneration = async (selectedSections: ContentSection[]) => {
    const bulkResults = [];
    const totalOperations = selectedTools!.length * selectedSections.length;
    let completedOperations = 0;

    for (const toolId of selectedTools!) {
      setProgress(prev => ({
        ...prev,
        currentSection: `Processing tool ${toolId}...`,
        progress: Math.round((completedOperations / totalOperations) * 100)
      }));

      try {
        // Get tool data
        const toolResponse = await fetch(`/api/admin/tools/${toolId}`, {
          headers: {
            'x-admin-api-key': process.env.NEXT_PUBLIC_ADMIN_API_KEY || 'aidude_admin_2024_secure_key_xyz789'
          }
        });

        if (!toolResponse.ok) {
          throw new Error(`Failed to get tool data for ${toolId}`);
        }

        const toolData = await toolResponse.json();
        const tool = toolData.data;

        // Generate sections for this tool
        const toolResults: any = { ...tool.generated_content };

        for (const section of selectedSections) {
          setProgress(prev => ({
            ...prev,
            currentSection: `${tool.name}: ${section.label}`,
            progress: Math.round((completedOperations / totalOperations) * 100)
          }));

          const sectionResult = await generateSectionContent(section, tool.generated_content);

          // Merge results
          const sectionKeys = section.key.split(',');
          sectionKeys.forEach(key => {
            if (sectionResult[key] !== undefined) {
              toolResults[key] = sectionResult[key];
            }
          });

          completedOperations++;
        }

        bulkResults.push({
          toolId,
          toolName: tool.name,
          success: true,
          updatedContent: toolResults,
          sections: selectedSections.map(s => s.key)
        });

      } catch (error) {
        console.error(`Error processing tool ${toolId}:`, error);
        bulkResults.push({
          toolId,
          success: false,
          error: error instanceof Error ? error.message : 'Unknown error'
        });
        completedOperations += selectedSections.length; // Skip remaining sections for this tool
      }
    }

    setProgress(prev => ({
      ...prev,
      progress: 100,
      currentSection: undefined
    }));

    // Call bulk completion handler
    if (onBulkComplete) {
      onBulkComplete(bulkResults);
    }

    setPreviewMode(true);
  };

  const generateSectionContent = async (section: ContentSection, existingContent: any) => {
    const response = await fetch('/api/generate-content', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'x-api-key': process.env.NEXT_PUBLIC_ADMIN_API_KEY || 'aidude_admin_2024_secure_key_xyz789'
      },
      body: JSON.stringify({
        url: existingContent.website || '',
        scrapedData: {
          title: existingContent.name,
          description: existingContent.short_description,
          content: existingContent.detailed_description
        },
        existingToolData: existingContent,
        sectionType: section.templateType
      })
    });

    if (!response.ok) {
      const errorData = await response.json();
      throw new Error(errorData.error || `Failed to generate ${section.label}`);
    }

    const result = await response.json();
    return result.content;
  };

  const handleApproveGeneration = () => {
    if (generatedContent) {
      const selectedSectionKeys = sections
        .filter(s => s.selected)
        .map(s => s.key)
        .join(',')
        .split(',');
      
      onGenerationComplete(generatedContent, selectedSectionKeys);
      setPreviewMode(false);
      setProgress({
        isGenerating: false,
        progress: 0,
        completedSections: []
      });
    }
  };

  const handleCancelGeneration = () => {
    setPreviewMode(false);
    setGeneratedContent(null);
    setProgress({
      isGenerating: false,
      progress: 0,
      completedSections: []
    });
  };

  const selectedCount = sections.filter(s => s.selected).length;

  return (
    <div className={`partial-content-generation bg-zinc-800 border border-zinc-700 rounded-lg p-6 ${className}`}>
      <div className="flex items-center justify-between mb-6">
        <div>
          <h3 className="text-lg font-semibold text-white">Partial Content Regeneration</h3>
          <p className="text-gray-400 text-sm">
            Regenerate specific sections for {toolName}
          </p>
        </div>
        
        {!progress.isGenerating && !previewMode && (
          <div className="flex items-center space-x-3">
            <button
              onClick={handleSelectAll}
              className="px-3 py-1 bg-zinc-600 hover:bg-zinc-500 text-white rounded text-sm"
            >
              {sections.every(s => s.selected) ? 'Deselect All' : 'Select All'}
            </button>
            <button
              onClick={handleStartGeneration}
              disabled={selectedCount === 0}
              className="px-4 py-2 bg-orange-600 hover:bg-orange-700 disabled:bg-orange-800 disabled:opacity-50 text-white rounded-md transition-colors"
            >
              Regenerate ({selectedCount})
            </button>
          </div>
        )}
      </div>

      {/* Section Selection */}
      {!progress.isGenerating && !previewMode && (
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-6">
          {sections.map((section) => (
            <div
              key={section.key}
              className={`border rounded-lg p-4 cursor-pointer transition-colors ${
                section.selected 
                  ? 'border-orange-500 bg-orange-500/10' 
                  : 'border-zinc-600 hover:border-zinc-500'
              }`}
              onClick={() => handleSectionToggle(section.key)}
            >
              <div className="flex items-start justify-between">
                <div className="flex-1">
                  <div className="flex items-center space-x-2">
                    <input
                      type="checkbox"
                      checked={section.selected}
                      onChange={() => handleSectionToggle(section.key)}
                      className="rounded border-zinc-600 text-orange-500 focus:ring-orange-500"
                    />
                    <h4 className="font-medium text-white">{section.label}</h4>
                    {section.hasExistingContent && (
                      <span className="px-2 py-1 bg-green-600 text-white text-xs rounded">
                        Has Content
                      </span>
                    )}
                  </div>
                  <p className="text-gray-400 text-sm mt-1">{section.description}</p>
                  {section.preview && (
                    <p className="text-gray-500 text-xs mt-2 truncate">
                      Current: {section.preview}
                    </p>
                  )}
                </div>
              </div>
            </div>
          ))}
        </div>
      )}

      {/* Generation Progress */}
      {progress.isGenerating && (
        <div className="mb-6">
          <div className="flex items-center justify-between mb-2">
            <span className="text-sm font-medium text-white">
              {progress.currentSection ? `Generating ${progress.currentSection}...` : 'Preparing...'}
            </span>
            <span className="text-sm text-gray-400">{progress.progress}%</span>
          </div>
          <div className="w-full bg-zinc-700 rounded-full h-2">
            <div 
              className="bg-orange-500 h-2 rounded-full transition-all duration-300"
              style={{ width: `${progress.progress}%` }}
            />
          </div>
          {progress.completedSections.length > 0 && (
            <p className="text-xs text-gray-400 mt-2">
              Completed: {progress.completedSections.length} sections
            </p>
          )}
        </div>
      )}

      {/* Preview Mode */}
      {previewMode && generatedContent && (
        <div className="space-y-4">
          <div className="bg-zinc-700 rounded-lg p-4">
            <h4 className="font-medium text-white mb-2">Generated Content Preview</h4>
            <p className="text-gray-400 text-sm mb-4">
              Review the generated content before applying changes.
            </p>
            
            <div className="space-y-3 max-h-60 overflow-y-auto">
              {sections.filter(s => s.selected).map(section => (
                <div key={section.key} className="border-l-2 border-orange-500 pl-3">
                  <h5 className="text-sm font-medium text-white">{section.label}</h5>
                  <p className="text-gray-300 text-sm">
                    {getContentPreview(section.key, generatedContent)}
                  </p>
                </div>
              ))}
            </div>
          </div>
          
          <div className="flex items-center justify-end space-x-3">
            <button
              onClick={handleCancelGeneration}
              className="px-4 py-2 bg-zinc-600 hover:bg-zinc-500 text-white rounded-md transition-colors"
            >
              Cancel
            </button>
            <button
              onClick={handleApproveGeneration}
              className="px-4 py-2 bg-green-600 hover:bg-green-700 text-white rounded-md transition-colors"
            >
              Apply Changes
            </button>
          </div>
        </div>
      )}

      {/* Error Display */}
      {progress.error && (
        <div className="bg-red-900/20 border border-red-500 rounded-lg p-3">
          <div className="flex items-center space-x-2">
            <svg className="w-5 h-5 text-red-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
            </svg>
            <span className="text-red-400 text-sm">{progress.error}</span>
          </div>
        </div>
      )}
    </div>
  );
}
