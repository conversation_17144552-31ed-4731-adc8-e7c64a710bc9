'use client';

import React, { useState, useEffect } from 'react';
import { WorkflowStage, WorkflowState, WorkflowTransition } from '@/lib/workflow/workflow-manager';

interface WorkflowStatusIndicatorProps {
  toolId: string;
  currentStage?: WorkflowStage;
  progress?: number;
  showHistory?: boolean;
  onStageClick?: (stage: WorkflowStage) => void;
  className?: string;
}

interface StageInfo {
  stage: WorkflowStage;
  label: string;
  icon: string;
  color: string;
  description: string;
}

const WORKFLOW_STAGES: StageInfo[] = [
  {
    stage: 'draft',
    label: 'Draft',
    icon: '📝',
    color: 'text-gray-400',
    description: 'Tool created, awaiting content generation'
  },
  {
    stage: 'content_generation',
    label: 'AI Generation',
    icon: '🤖',
    color: 'text-blue-400',
    description: 'AI Dude is generating content'
  },
  {
    stage: 'content_review',
    label: 'Content Review',
    icon: '👀',
    color: 'text-yellow-400',
    description: 'Content ready for admin review'
  },
  {
    stage: 'editorial_review',
    label: 'Editorial Review',
    icon: '✏️',
    color: 'text-purple-400',
    description: 'Under editorial review'
  },
  {
    stage: 'approved',
    label: 'Approved',
    icon: '✅',
    color: 'text-green-400',
    description: 'Approved for publication'
  },
  {
    stage: 'published',
    label: 'Published',
    icon: '🚀',
    color: 'text-green-500',
    description: 'Live on the platform'
  }
];

export function WorkflowStatusIndicator({
  toolId,
  currentStage = 'draft',
  progress = 0,
  showHistory = false,
  onStageClick,
  className = ''
}: WorkflowStatusIndicatorProps) {
  const [workflowState, setWorkflowState] = useState<WorkflowState | null>(null);
  const [workflowHistory, setWorkflowHistory] = useState<WorkflowTransition[]>([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    if (showHistory) {
      loadWorkflowData();
    }
  }, [toolId, showHistory]);

  const loadWorkflowData = async () => {
    try {
      setLoading(true);
      setError(null);

      const [stateResponse, historyResponse] = await Promise.all([
        fetch(`/api/admin/workflow/${toolId}/state`, {
          headers: {
            'x-admin-api-key': process.env.NEXT_PUBLIC_ADMIN_API_KEY || 'aidude_admin_2024_secure_key_xyz789'
          }
        }),
        fetch(`/api/admin/workflow/${toolId}/history`, {
          headers: {
            'x-admin-api-key': process.env.NEXT_PUBLIC_ADMIN_API_KEY || 'aidude_admin_2024_secure_key_xyz789'
          }
        })
      ]);

      if (stateResponse.ok) {
        const stateData = await stateResponse.json();
        setWorkflowState(stateData.success ? stateData.data : null);
      }

      if (historyResponse.ok) {
        const historyData = await historyResponse.json();
        setWorkflowHistory(historyData.success ? historyData.data : []);
      }

    } catch (err) {
      console.error('Error loading workflow data:', err);
      setError('Failed to load workflow data');
    } finally {
      setLoading(false);
    }
  };

  const getCurrentStageIndex = () => {
    const stage = workflowState?.currentStage || currentStage;
    return WORKFLOW_STAGES.findIndex(s => s.stage === stage);
  };

  const getStageStatus = (stageIndex: number) => {
    const currentIndex = getCurrentStageIndex();
    const stage = WORKFLOW_STAGES[stageIndex];
    
    if (stageIndex < currentIndex) {
      return 'completed';
    } else if (stageIndex === currentIndex) {
      return 'current';
    } else {
      return 'pending';
    }
  };

  const getProgressPercentage = () => {
    return workflowState?.progress || progress;
  };

  const formatTimestamp = (timestamp: string) => {
    return new Date(timestamp).toLocaleString();
  };

  return (
    <div className={`workflow-status-indicator ${className}`}>
      {/* Progress Bar */}
      <div className="mb-6">
        <div className="flex justify-between items-center mb-2">
          <span className="text-sm font-medium text-white">Workflow Progress</span>
          <span className="text-sm text-gray-400">{getProgressPercentage()}%</span>
        </div>
        <div className="w-full bg-zinc-700 rounded-full h-2">
          <div 
            className="bg-orange-500 h-2 rounded-full transition-all duration-300"
            style={{ width: `${getProgressPercentage()}%` }}
          />
        </div>
      </div>

      {/* Stage Indicators */}
      <div className="flex items-center justify-between mb-6">
        {WORKFLOW_STAGES.map((stage, index) => {
          const status = getStageStatus(index);
          const isClickable = onStageClick && status !== 'pending';
          
          return (
            <div key={stage.stage} className="flex flex-col items-center">
              {/* Stage Icon */}
              <div 
                className={`
                  w-10 h-10 rounded-full flex items-center justify-center text-lg
                  ${status === 'completed' ? 'bg-green-600 text-white' :
                    status === 'current' ? 'bg-orange-500 text-white' :
                    'bg-zinc-700 text-gray-400'}
                  ${isClickable ? 'cursor-pointer hover:opacity-80' : ''}
                  transition-all duration-200
                `}
                onClick={() => isClickable && onStageClick(stage.stage)}
                title={stage.description}
              >
                {status === 'completed' ? '✓' : stage.icon}
              </div>
              
              {/* Stage Label */}
              <span className={`
                text-xs mt-2 text-center max-w-16
                ${status === 'current' ? 'text-orange-400 font-medium' :
                  status === 'completed' ? 'text-green-400' :
                  'text-gray-500'}
              `}>
                {stage.label}
              </span>
              
              {/* Connection Line */}
              {index < WORKFLOW_STAGES.length - 1 && (
                <div className={`
                  absolute h-0.5 w-16 mt-5 ml-10
                  ${status === 'completed' ? 'bg-green-600' : 'bg-zinc-700'}
                `} />
              )}
            </div>
          );
        })}
      </div>

      {/* Current Stage Info */}
      {workflowState && (
        <div className="bg-zinc-800 border border-zinc-700 rounded-lg p-4 mb-4">
          <div className="flex items-center justify-between">
            <div>
              <h4 className="text-sm font-medium text-white">Current Stage</h4>
              <p className="text-gray-300">
                {WORKFLOW_STAGES.find(s => s.stage === workflowState.currentStage)?.description}
              </p>
            </div>
            <div className="text-right">
              <p className="text-xs text-gray-400">Last Updated</p>
              <p className="text-sm text-white">{formatTimestamp(workflowState.updatedAt)}</p>
            </div>
          </div>
        </div>
      )}

      {/* Workflow History */}
      {showHistory && workflowHistory.length > 0 && (
        <div className="bg-zinc-800 border border-zinc-700 rounded-lg p-4">
          <h4 className="text-sm font-medium text-white mb-3">Workflow History</h4>
          <div className="space-y-2 max-h-40 overflow-y-auto">
            {workflowHistory.map((transition, index) => (
              <div key={index} className="flex items-center justify-between text-xs">
                <div className="flex items-center space-x-2">
                  <span className="text-gray-400">
                    {WORKFLOW_STAGES.find(s => s.stage === transition.from)?.label} →
                  </span>
                  <span className="text-white">
                    {WORKFLOW_STAGES.find(s => s.stage === transition.to)?.label}
                  </span>
                  <span className="text-gray-500">by {transition.triggeredBy}</span>
                </div>
                <span className="text-gray-400">{formatTimestamp(transition.timestamp)}</span>
              </div>
            ))}
          </div>
        </div>
      )}

      {/* Error State */}
      {error && (
        <div className="bg-red-900/20 border border-red-500 rounded-lg p-3">
          <p className="text-red-400 text-sm">{error}</p>
        </div>
      )}

      {/* Loading State */}
      {loading && (
        <div className="flex items-center justify-center py-4">
          <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-orange-500"></div>
        </div>
      )}
    </div>
  );
}
