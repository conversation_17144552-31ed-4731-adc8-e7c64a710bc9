#!/usr/bin/env tsx

import * as dotenv from 'dotenv';
import { createClient } from '@supabase/supabase-js';

// Load environment variables
dotenv.config({ path: '.env.local' });

// Create Supabase client with service role
const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL!;
const supabaseKey = process.env.SUPABASE_SERVICE_ROLE_KEY!;
const supabase = createClient(supabaseUrl, supabaseKey);

async function verifySchemaCompleteness() {
  console.log('🔍 Verifying Dual Submission System Schema Completeness...\n');

  try {
    // Required columns for tool_submissions table
    const requiredSubmissionColumns = [
      'id', 'name', 'url', 'description', 'detailed_description', 'category', 'subcategory',
      'features', 'pricing_type', 'pricing_details', 'pros', 'cons', 'meta_title', 
      'meta_description', 'meta_keywords', 'use_cases', 'target_audience', 'integrations',
      'tags', 'faq', 'submitter_name', 'submitter_email', 'submission_type', 'status',
      'submitted_at', 'reviewed_by', 'reviewed_at', 'review_notes', 'media_assets'
    ];

    // Required columns for tools table
    const requiredToolsColumns = [
      'id', 'name', 'slug', 'link', 'description', 'detailed_description', 'website',
      'category_id', 'subcategory', 'company', 'features', 'pricing_type', 'pricing_details',
      'pros', 'cons', 'meta_title', 'meta_description', 'meta_keywords', 'use_cases',
      'target_audience', 'integrations', 'tags', 'faqs', 'content_status', 'submission_type',
      'is_verified', 'is_claimed', 'created_at', 'updated_at', 'published_at',
      'submission_id', 'submitted_by', 'submission_date', 'approved_by', 'approved_at',
      'editorial_notes', 'primary_image', 'primary_image_type', 'logo_url', 'media_source',
      'media_updated_at'
    ];

    // Check tool_submissions table
    console.log('1️⃣ Checking tool_submissions table:');
    const submissionResults = await checkTableColumns('tool_submissions', requiredSubmissionColumns);
    
    // Check tools table
    console.log('\n2️⃣ Checking tools table:');
    const toolsResults = await checkTableColumns('tools', requiredToolsColumns);

    // Summary
    console.log('\n3️⃣ Schema Completeness Summary:');
    
    const totalSubmissionColumns = requiredSubmissionColumns.length;
    const existingSubmissionColumns = submissionResults.existing.length;
    const submissionCompleteness = Math.round((existingSubmissionColumns / totalSubmissionColumns) * 100);

    const totalToolsColumns = requiredToolsColumns.length;
    const existingToolsColumns = toolsResults.existing.length;
    const toolsCompleteness = Math.round((existingToolsColumns / totalToolsColumns) * 100);

    console.log(`   📊 tool_submissions: ${existingSubmissionColumns}/${totalSubmissionColumns} (${submissionCompleteness}%)`);
    console.log(`   📊 tools: ${existingToolsColumns}/${totalToolsColumns} (${toolsCompleteness}%)`);

    if (submissionResults.missing.length > 0) {
      console.log(`\n   ❌ Missing tool_submissions columns:`);
      submissionResults.missing.forEach(col => console.log(`      • ${col}`));
    }

    if (toolsResults.missing.length > 0) {
      console.log(`\n   ❌ Missing tools columns:`);
      toolsResults.missing.forEach(col => console.log(`      • ${col}`));
    }

    const overallCompleteness = Math.round(((existingSubmissionColumns + existingToolsColumns) / (totalSubmissionColumns + totalToolsColumns)) * 100);
    
    console.log(`\n   🎯 Overall Schema Completeness: ${overallCompleteness}%`);

    if (overallCompleteness === 100) {
      console.log('\n✅ Schema is complete! Ready for dual submission system.');
      return true;
    } else {
      console.log('\n❌ Schema incomplete. Please run the migration:');
      console.log('   supabase/migrations/006_complete_dual_submission_schema.sql');
      return false;
    }

  } catch (error) {
    console.error('❌ Schema verification failed:', error);
    return false;
  }
}

async function checkTableColumns(tableName: string, requiredColumns: string[]) {
  const existing: string[] = [];
  const missing: string[] = [];

  for (const column of requiredColumns) {
    try {
      const { data, error } = await supabase
        .from(tableName)
        .select(column)
        .limit(1);
        
      if (error) {
        if (error.message.includes(`column "${column}" does not exist`)) {
          missing.push(column);
          console.log(`   ❌ ${column}`);
        } else {
          console.log(`   ⚠️  ${column} - ERROR: ${error.message}`);
          missing.push(column);
        }
      } else {
        existing.push(column);
        console.log(`   ✅ ${column}`);
      }
    } catch (err) {
      missing.push(column);
      console.log(`   ❌ ${column} - ERROR: ${err}`);
    }
  }

  return { existing, missing };
}

// Run verification
verifySchemaCompleteness()
  .then((success) => {
    console.log('\n🎉 Schema verification complete!');
    process.exit(success ? 0 : 1);
  })
  .catch((error) => {
    console.error('❌ Verification failed:', error);
    process.exit(1);
  });
