#!/usr/bin/env tsx

import * as dotenv from 'dotenv';
import { createClient } from '@supabase/supabase-js';

// Load environment variables
dotenv.config({ path: '.env.local' });

// Create Supabase client with service role for admin operations
const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL!;
const supabaseKey = process.env.SUPABASE_SERVICE_ROLE_KEY!;
const supabase = createClient(supabaseUrl, supabaseKey);

async function createMissingTool() {
  console.log('🔧 Creating missing QuillBot tool...');
  
  try {
    const { data, error } = await supabase
      .from('tools')
      .insert({
        id: 'quillbot',
        name: 'QuillBot',
        slug: 'quillbot',
        link: '/tools/quillbot',
        description: 'AI-powered paraphrasing and writing assistant that helps improve your writing',
        short_description: 'AI writing assistant',
        website: 'https://quillbot.com',
        category_id: 'writing-assistant',
        company: 'QuillBot',
        content_status: 'draft',
        is_verified: true,
        is_claimed: false
      })
      .select()
      .single();
      
    if (error) {
      console.error('❌ Error creating tool:', error);
      return;
    }
    
    console.log('✅ Created QuillBot tool successfully!');
    console.log(`   ID: ${data.id}`);
    console.log(`   Name: ${data.name}`);
    console.log(`   Status: ${data.content_status}`);
    
  } catch (error) {
    console.error('❌ Script error:', error);
  }
}

createMissingTool()
  .then(() => {
    console.log('\n🎉 Tool creation complete!');
    process.exit(0);
  })
  .catch((error) => {
    console.error('❌ Creation failed:', error);
    process.exit(1);
  });
