-- Workflow Management Tables
-- Creates tables for tracking tool workflow states and transitions

-- Workflow States Table
CREATE TABLE IF NOT EXISTS workflow_states (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    tool_id UUID NOT NULL REFERENCES tools(id) ON DELETE CASCADE,
    current_stage TEXT NOT NULL CHECK (current_stage IN (
        'draft', 'content_generation', 'content_review', 
        'editorial_review', 'approved', 'published', 'rejected', 'error'
    )),
    progress INTEGER NOT NULL DEFAULT 0 CHECK (progress >= 0 AND progress <= 100),
    started_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    updated_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    completed_stages JSONB DEFAULT '[]'::jsonb,
    metadata JSONB DEFAULT '{}'::jsonb,
    
    -- Constraints
    UNIQUE(tool_id),
    
    -- Indexes
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at_index TIMESTAMPTZ DEFAULT NOW()
);

-- Workflow Transitions Table
CREATE TABLE IF NOT EXISTS workflow_transitions (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    tool_id UUID NOT NULL REFERENCES tools(id) ON DELETE CASCADE,
    from_stage TEXT NOT NULL,
    to_stage TEXT NOT NULL,
    triggered_by TEXT NOT NULL,
    timestamp TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    notes TEXT,
    job_id TEXT,
    
    -- Indexes for performance
    created_at TIMESTAMPTZ DEFAULT NOW()
);

-- Indexes for performance
CREATE INDEX IF NOT EXISTS idx_workflow_states_tool_id ON workflow_states(tool_id);
CREATE INDEX IF NOT EXISTS idx_workflow_states_current_stage ON workflow_states(current_stage);
CREATE INDEX IF NOT EXISTS idx_workflow_states_updated_at ON workflow_states(updated_at);

CREATE INDEX IF NOT EXISTS idx_workflow_transitions_tool_id ON workflow_transitions(tool_id);
CREATE INDEX IF NOT EXISTS idx_workflow_transitions_timestamp ON workflow_transitions(timestamp);
CREATE INDEX IF NOT EXISTS idx_workflow_transitions_from_stage ON workflow_transitions(from_stage);
CREATE INDEX IF NOT EXISTS idx_workflow_transitions_to_stage ON workflow_transitions(to_stage);

-- Update triggers for workflow_states
CREATE OR REPLACE FUNCTION update_workflow_states_updated_at()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER trigger_workflow_states_updated_at
    BEFORE UPDATE ON workflow_states
    FOR EACH ROW
    EXECUTE FUNCTION update_workflow_states_updated_at();

-- RLS Policies (if RLS is enabled)
-- Note: These policies assume admin-only access for workflow management
-- Adjust as needed based on your security requirements

-- Enable RLS
ALTER TABLE workflow_states ENABLE ROW LEVEL SECURITY;
ALTER TABLE workflow_transitions ENABLE ROW LEVEL SECURITY;

-- Admin access policies
CREATE POLICY "Admin can manage workflow states" ON workflow_states
    FOR ALL USING (true);

CREATE POLICY "Admin can manage workflow transitions" ON workflow_transitions
    FOR ALL USING (true);

-- Grant permissions to service role
GRANT ALL ON workflow_states TO service_role;
GRANT ALL ON workflow_transitions TO service_role;

-- Grant permissions to authenticated users (read-only for non-admins)
GRANT SELECT ON workflow_states TO authenticated;
GRANT SELECT ON workflow_transitions TO authenticated;

-- Comments for documentation
COMMENT ON TABLE workflow_states IS 'Tracks the current workflow state for each tool';
COMMENT ON TABLE workflow_transitions IS 'Records all workflow state transitions for audit trail';

COMMENT ON COLUMN workflow_states.current_stage IS 'Current stage in the workflow process';
COMMENT ON COLUMN workflow_states.progress IS 'Progress percentage (0-100) through the workflow';
COMMENT ON COLUMN workflow_states.completed_stages IS 'Array of completed workflow stages';
COMMENT ON COLUMN workflow_states.metadata IS 'Additional workflow metadata (job IDs, etc.)';

COMMENT ON COLUMN workflow_transitions.from_stage IS 'Previous workflow stage';
COMMENT ON COLUMN workflow_transitions.to_stage IS 'New workflow stage';
COMMENT ON COLUMN workflow_transitions.triggered_by IS 'User or system that triggered the transition';
COMMENT ON COLUMN workflow_transitions.job_id IS 'Associated job ID if applicable';

-- Sample data for testing (optional - remove in production)
-- INSERT INTO workflow_states (tool_id, current_stage, progress) 
-- SELECT id, 'draft', 0 FROM tools WHERE id NOT IN (SELECT tool_id FROM workflow_states) LIMIT 5;

-- Verification queries
-- SELECT 'workflow_states' as table_name, COUNT(*) as row_count FROM workflow_states
-- UNION ALL
-- SELECT 'workflow_transitions' as table_name, COUNT(*) as row_count FROM workflow_transitions;
