#!/bin/bash

# Editorial Reviews Cleanup Runner
# 
# This script provides an easy way to run the editorial cleanup scripts
# with proper error handling and logging.

set -e  # Exit on any error

echo "🚀 Editorial Reviews Cleanup Runner"
echo "==================================="
echo ""

# Check if we're in the right directory
if [ ! -f "package.json" ]; then
    echo "❌ Error: Please run this script from the project root directory"
    exit 1
fi

# Check if tsx is available
if ! command -v npx &> /dev/null; then
    echo "❌ Error: npx is not available. Please install Node.js"
    exit 1
fi

# Create logs directory if it doesn't exist
mkdir -p logs

# Set log file with timestamp
LOG_FILE="logs/editorial-cleanup-$(date +%Y%m%d-%H%M%S).log"

echo "📝 Logging to: $LOG_FILE"
echo ""

# Function to run script with logging
run_script() {
    local script_name=$1
    local description=$2
    
    echo "🔧 Running: $description"
    echo "📄 Script: $script_name"
    echo ""
    
    # Run the script and capture output
    if npx tsx "$script_name" 2>&1 | tee -a "$LOG_FILE"; then
        echo ""
        echo "✅ $description completed successfully"
        return 0
    else
        echo ""
        echo "❌ $description failed"
        return 1
    fi
}

# Main execution
main() {
    echo "⚠️  WARNING: This will delete editorial review data!"
    echo "📋 The following operations will be performed:"
    echo "   • Delete all editorial reviews"
    echo "   • Delete malformed AI jobs"
    echo "   • Clear tool references"
    echo "   • Verify cleanup"
    echo ""
    
    # Ask for confirmation
    read -p "🤔 Do you want to continue? (y/N): " -n 1 -r
    echo ""
    
    if [[ ! $REPLY =~ ^[Yy]$ ]]; then
        echo "❌ Operation cancelled by user"
        exit 0
    fi
    
    echo ""
    echo "🚀 Starting cleanup process..."
    echo "=============================="
    echo ""
    
    # Run the UUID fix script (more comprehensive)
    if run_script "scripts/fix-editorial-uuid-issues.ts" "UUID Issues Fix"; then
        echo ""
        echo "🎉 Editorial cleanup completed successfully!"
        echo ""
        echo "📊 Summary:"
        echo "   • All editorial reviews deleted"
        echo "   • Malformed AI jobs removed"
        echo "   • Tool references cleared"
        echo "   • UUID parsing issues resolved"
        echo ""
        echo "📝 Full log available at: $LOG_FILE"
        echo ""
        echo "✅ The editorial system should now work without errors"
    else
        echo ""
        echo "💥 Cleanup failed!"
        echo "📝 Check the log file for details: $LOG_FILE"
        echo ""
        echo "🔧 You can also try running the SQL script manually:"
        echo "   scripts/clear-editorial-reviews.sql"
        exit 1
    fi
}

# Trap to handle interruption
trap 'echo ""; echo "❌ Script interrupted by user"; exit 1' INT

# Run main function
main

echo ""
echo "🏁 Script completed"
