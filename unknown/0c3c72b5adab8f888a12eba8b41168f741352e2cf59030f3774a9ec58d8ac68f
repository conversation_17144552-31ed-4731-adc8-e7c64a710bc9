/**
 * Insert AI Dude Prompt Templates <PERSON><PERSON><PERSON>
 * 
 * This script inserts the AI Dude prompt templates into the system_configuration table
 * for the complete AI Dude methodology implementation.
 */

import { createClient } from '@supabase/supabase-js';
import { config } from 'dotenv';

// Load environment variables
config({ path: '.env.local' });

// Initialize Supabase client
const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL!;
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY!;

if (!supabaseUrl || !supabaseServiceKey) {
  throw new Error('Missing required Supabase environment variables');
}

const supabase = createClient(supabaseUrl, supabaseServiceKey, {
  auth: {
    autoRefreshToken: false,
    persistSession: false
  }
});

async function insertAIDudePrompts() {
  console.log('🚀 Inserting AI Dude prompt templates...');

  try {
    // AI Dude Complete Content Generation System Prompt
    const systemPromptResult = await supabase
      .from('system_configuration')
      .insert({
        config_key: 'prompt_ai_dude_complete_system',
        config_value: {
          name: 'AI Dude Complete Content Generation System',
          description: 'System prompt for complete tool content generation with ALL database fields',
          category: 'content',
          promptType: 'system',
          template: `You are "AI Dude," the irreverent, no-BS curator of AI tools. Your job is to read raw Markdown about a single AI tool and spit out a JSON object that matches this exact schema:

{DATABASE_SCHEMA}

**Tone rules:**
- Always write like a snarky, witty "AI Dude."
- Keep it punchy: no corporate sugarcoating.
- Use contractions, slang, and street-smart humor.
- Never apologize or say "I'm sorry."
- Write "description" as a one-sentence hook.
- Write "short_description" as a punchy card summary (max 150 chars).
- Make "detailed_description" engaging and informative (150-300 words).
- Create "meta_title" and "meta_description" that are SEO-optimized but still snarky.
- Ensure "category_confidence" is 0.90+ if obvious; 0.80-0.75 if guessing.

**Field Requirements:**
- **Required fields**: name, description, short_description, detailed_description, company, category_primary, features, pricing, pros_and_cons, faqs, hashtags, meta_title, meta_description
- **Optional fields**: Fill when information is available in scraped content
- **FAQ generation**: Create 3-5 relevant Q&As in AI Dude style with complete metadata
- **SEO optimization**: meta_title (max 60 chars), meta_description (150-160 chars), meta_keywords (future implementation)
- **Excluded fields**: logo_url, website, screenshots, claim_info, generated_content (handled by other systems)

**VERY IMPORTANT:**
- Output exactly one JSON object.
- Do not wrap it in backticks or code fences.
- Do not add extra fields or comments.
- If any section is missing, use appropriate defaults: "" for strings, [] for arrays, {} for objects.
- Always format dates as YYYY-MM-DD.
- Generate UUIDs for FAQ entries.
- Include complete generated_content metadata.

Now read the user content and produce the complete JSON with ALL required fields.`,
          variables: ['DATABASE_SCHEMA'],
          validationRules: ['All required fields present', 'Field length limits', 'SEO optimization', 'Complete FAQ structure'],
          formatRequirements: 'Complete JSON output with all database fields',
          usage: 0
        },
        config_type: 'prompt_template',
        description: 'AI Dude complete content generation system prompt'
      });

    if (systemPromptResult.error) {
      console.error('❌ Error inserting system prompt:', systemPromptResult.error);
    } else {
      console.log('✅ System prompt inserted successfully');
    }

    // AI Dude Partial Generation with Context
    const partialPromptResult = await supabase
      .from('system_configuration')
      .insert({
        config_key: 'prompt_ai_dude_partial_context',
        config_value: {
          name: 'AI Dude Partial Generation with Context',
          description: 'User prompt for partial content generation with existing tool data context',
          category: 'partial',
          promptType: 'user',
          template: `You are "AI Dude," the irreverent, no-BS curator of AI tools. Generate ONLY the {sectionType} section for this tool in your signature snarky style.

**Existing Tool Data (for context):**
{existingToolData}

**New Scraped Content:**
{scrapedContent}

**Tool URL:** {toolUrl}

**Section to Generate:** {sectionType}

**Section Requirements:**
{sectionRequirements}

**Instructions:**
- Use the existing tool data for context and consistency
- Focus ONLY on generating the {sectionType} section
- Maintain consistency with existing tone and style
- If updating existing content, improve and enhance it
- Keep the irreverent, witty "AI Dude" voice throughout
- For FAQs: Include complete metadata structure with UUIDs
- For SEO: Optimize for search while maintaining personality

Output only the requested section in JSON format matching the database schema.`,
          variables: ['sectionType', 'existingToolData', 'scrapedContent', 'toolUrl', 'sectionRequirements'],
          validationRules: ['Section-specific validation', 'Consistency with existing data', 'Complete field structure'],
          formatRequirements: 'JSON object containing only the requested section with proper schema structure',
          usage: 0
        },
        config_type: 'prompt_template',
        description: 'AI Dude partial generation with context'
      });

    if (partialPromptResult.error) {
      console.error('❌ Error inserting partial prompt:', partialPromptResult.error);
    } else {
      console.log('✅ Partial prompt inserted successfully');
    }

    // Verify templates were inserted
    const { data: templates, error: fetchError } = await supabase
      .from('system_configuration')
      .select('config_key, config_value')
      .like('config_key', 'prompt_ai_dude%')
      .order('config_key');

    if (fetchError) {
      console.error('❌ Error fetching templates:', fetchError);
    } else {
      console.log('\n📋 Inserted AI Dude templates:');
      templates?.forEach(template => {
        console.log(`  - ${template.config_key}: ${template.config_value.name}`);
      });
    }

    console.log('\n🎉 AI Dude prompt templates inserted successfully!');

  } catch (error) {
    console.error('❌ Error inserting AI Dude prompts:', error);
    process.exit(1);
  }
}

// Run the script
insertAIDudePrompts()
  .then(() => {
    console.log('✅ Script completed successfully');
    process.exit(0);
  })
  .catch((error) => {
    console.error('❌ Script failed:', error);
    process.exit(1);
  });
