const { createClient } = require('@supabase/supabase-js');

// Supabase configuration
const supabaseUrl = 'https://gvcdqspryxrvxadfpwux.supabase.co';
const supabaseServiceKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Imd2Y2Rxc3ByeXhydnhhZGZwd3V4Iiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImlhdCI6MTc0OTA0NDE0NiwiZXhwIjoyMDY0NjIwMTQ2fQ.s1w4ohjetf2iHfhpnvrBRZ0L5ORu53Oovpr6vyCPbhw';

const supabase = createClient(supabaseUrl, supabaseServiceKey);

async function checkTemplates() {
  console.log('🔍 Checking AI Dude Prompt Templates in Database...\n');

  try {
    // Get all AI Dude templates
    const { data: templates, error } = await supabase
      .from('system_configuration')
      .select('*')
      .like('config_key', 'prompt_ai_dude%')
      .order('config_key');

    if (error) {
      console.log(`❌ Error: ${error.message}`);
      return;
    }

    console.log(`✅ Found ${templates.length} AI Dude templates:\n`);
    
    templates.forEach((template, index) => {
      console.log(`${index + 1}. ${template.config_key}`);
      console.log(`   Name: ${template.config_value?.name || 'N/A'}`);
      console.log(`   Type: ${template.config_value?.promptType || 'N/A'}`);
      console.log(`   Category: ${template.config_value?.category || 'N/A'}`);
      console.log(`   Active: ${template.is_active}`);
      console.log(`   Created: ${template.created_at}`);
      console.log('');
    });

    // Check if templates have proper structure
    console.log('--- Template Structure Validation ---');
    templates.forEach(template => {
      const config = template.config_value;
      const hasTemplate = config?.template && config.template.length > 0;
      const hasVariables = config?.variables && Array.isArray(config.variables);
      const hasValidType = ['system', 'user'].includes(config?.promptType);
      
      console.log(`${template.config_key}:`);
      console.log(`  ✅ Has template: ${hasTemplate}`);
      console.log(`  ✅ Has variables: ${hasVariables}`);
      console.log(`  ✅ Valid type: ${hasValidType}`);
      
      if (hasTemplate) {
        console.log(`  📏 Template length: ${config.template.length} chars`);
      }
    });

    // Test prompt retrieval through the API
    console.log('\n--- Testing API Access ---');
    const http = require('http');
    
    const options = {
      hostname: 'localhost',
      port: 3000,
      path: '/api/admin/prompts',
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
        'x-api-key': 'aidude_admin_2024_secure_key_xyz789'
      }
    };

    const req = http.request(options, (res) => {
      let responseData = '';
      res.on('data', (chunk) => responseData += chunk);
      res.on('end', () => {
        try {
          const parsed = JSON.parse(responseData);
          const aiDudePrompts = parsed.data?.filter(p => p.config_key?.includes('ai_dude')) || [];
          console.log(`API Response Status: ${res.statusCode}`);
          console.log(`AI Dude prompts via API: ${aiDudePrompts.length}`);
          
          if (aiDudePrompts.length > 0) {
            console.log('✅ AI Dude templates accessible via API');
          } else {
            console.log('❌ AI Dude templates not accessible via API');
          }
        } catch (e) {
          console.log(`❌ API Error: ${e.message}`);
        }
      });
    });

    req.on('error', (error) => {
      console.log(`❌ API Request Error: ${error.message}`);
    });

    req.end();

  } catch (error) {
    console.error('Check failed:', error.message);
  }
}

checkTemplates();
