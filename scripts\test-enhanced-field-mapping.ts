#!/usr/bin/env tsx

// Test script for enhanced field mapping in SubmissionTransformer
// Tests all new fields from detailed submissions to tools table

interface TestSubmission {
  id: string;
  name: string;
  url: string;
  description: string;
  detailed_description?: string;
  category: string;
  subcategory?: string;
  features?: string;
  pricing_type?: string;
  pricing_details?: string;
  pros?: string;
  cons?: string;
  meta_title?: string;
  meta_description?: string;
  meta_keywords?: string;
  use_cases?: string;
  target_audience?: string;
  integrations?: string;
  tags?: string;
  faq?: string;
  submitter_name?: string;
  submitter_email: string;
  submission_type: 'simple' | 'detailed';
  status: string;
  submitted_at?: string;
  media_assets?: any;
}

class TestEnhancedFieldMapping {
  /**
   * Test tag transformation
   */
  testTagTransformation(): boolean {
    console.log('🏷️ Testing Tag Transformation...\n');

    const testCases = [
      {
        input: 'AI, productivity, automation, writing, content',
        expected: ['AI', 'productivity', 'automation', 'writing', 'content']
      },
      {
        input: ' AI , productivity,automation,  writing  , content ',
        expected: ['AI', 'productivity', 'automation', 'writing', 'content']
      },
      {
        input: '',
        expected: null
      },
      {
        input: 'single-tag',
        expected: ['single-tag']
      },
      {
        input: 'tag1,tag2,tag3,tag4,tag5,tag6,tag7,tag8,tag9,tag10,tag11,tag12',
        expected: ['tag1', 'tag2', 'tag3', 'tag4', 'tag5', 'tag6', 'tag7', 'tag8', 'tag9', 'tag10'] // Limited to 10
      }
    ];

    let passedTests = 0;
    testCases.forEach((testCase, index) => {
      const result = this.transformTags(testCase.input);
      const passed = JSON.stringify(result) === JSON.stringify(testCase.expected);
      
      console.log(`   ${index + 1}. Input: "${testCase.input}"`);
      console.log(`      Expected: ${JSON.stringify(testCase.expected)}`);
      console.log(`      Got: ${JSON.stringify(result)}`);
      console.log(`      Status: ${passed ? '✅ PASS' : '❌ FAIL'}\n`);
      
      if (passed) passedTests++;
    });

    return passedTests === testCases.length;
  }

  /**
   * Test complete field mapping
   */
  testCompleteFieldMapping(): boolean {
    console.log('🗺️ Testing Complete Field Mapping...\n');

    const detailedSubmission: TestSubmission = {
      id: 'test-submission-123',
      name: 'Enhanced Test Tool',
      url: 'https://enhanced-test-tool.com',
      description: 'A comprehensive test tool for field mapping validation',
      detailed_description: 'This is a detailed description that provides comprehensive information about the tool, its capabilities, and how it can benefit users in their daily workflows.',
      category: 'productivity-ai',
      subcategory: 'Task Automation',
      features: '• Advanced AI algorithms\n• Real-time processing\n• Multi-language support\n• API integrations',
      pricing_type: 'freemium',
      pricing_details: 'Free tier: 100 requests/month\nPro tier: $29/month\nEnterprise: Custom pricing',
      pros: '• Fast and accurate\n• Easy to use\n• Great customer support\n• Regular updates',
      cons: '• Limited free tier\n• Learning curve for advanced features\n• Requires internet connection',
      meta_title: 'Enhanced Test Tool - AI-Powered Productivity Assistant',
      meta_description: 'Boost your productivity with our enhanced AI test tool featuring advanced algorithms and real-time processing.',
      meta_keywords: 'AI, productivity, automation, test tool, enhanced features',
      use_cases: 'Content creation, data analysis, workflow automation, team collaboration, project management',
      target_audience: 'Content creators, data analysts, project managers, small business owners, freelancers',
      integrations: 'Slack, Google Workspace, Microsoft Teams, Zapier, Notion, Trello',
      tags: 'AI, productivity, automation, content, analysis, workflow',
      faq: 'Q: How does it work?\nA: Our AI processes your input and provides intelligent suggestions.\n\nQ: Is there a free trial?\nA: Yes, we offer a free tier with 100 requests per month.',
      submitter_name: 'Enhanced Test Company',
      submitter_email: '<EMAIL>',
      submission_type: 'detailed',
      status: 'approved',
      submitted_at: '2024-01-15T10:00:00Z',
      media_assets: {
        primaryImage: 'data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAA...',
        primaryImageType: 'screenshot',
        logo: 'https://enhanced-test-tool.com/logo.png',
        logoSource: 'url'
      }
    };

    const transformedTool = this.mockTransformSubmissionToTool(detailedSubmission, 'test-admin');

    // Check all enhanced fields are mapped
    const enhancedFields = [
      'detailed_description',
      'use_cases', 
      'target_audience',
      'integrations',
      'tags',
      'meta_keywords',
      'submission_id',
      'submitted_by',
      'submission_date',
      'approved_by',
      'approved_at'
    ];

    const missingFields = enhancedFields.filter(field => 
      transformedTool[field] === undefined || transformedTool[field] === null
    );

    console.log('   📋 Enhanced Field Mapping Results:');
    enhancedFields.forEach(field => {
      const value = transformedTool[field];
      const status = value !== undefined && value !== null ? '✅' : '❌';
      console.log(`      ${status} ${field}: ${this.formatValue(value)}`);
    });

    if (missingFields.length === 0) {
      console.log('\n   🎉 All enhanced fields mapped successfully!');
      return true;
    } else {
      console.log(`\n   ❌ Missing fields: ${missingFields.join(', ')}`);
      return false;
    }
  }

  /**
   * Test submission tracking fields
   */
  testSubmissionTracking(): boolean {
    console.log('\n📊 Testing Submission Tracking Fields...\n');

    const submission: TestSubmission = {
      id: 'track-test-456',
      name: 'Tracking Test Tool',
      url: 'https://tracking-test.com',
      description: 'Test tool for submission tracking',
      submitter_email: '<EMAIL>',
      submission_type: 'detailed',
      status: 'approved',
      submitted_at: '2024-01-15T10:00:00Z'
    };

    const transformed = this.mockTransformSubmissionToTool(submission, 'admin-user');

    const trackingFields = {
      submission_id: submission.id,
      submitted_by: submission.submitter_email,
      submission_date: submission.submitted_at,
      approved_by: 'admin-user',
      approved_at: transformed.approved_at
    };

    let allFieldsPresent = true;
    console.log('   📋 Submission Tracking Results:');
    Object.entries(trackingFields).forEach(([field, expectedValue]) => {
      const actualValue = transformed[field];
      const matches = field === 'approved_at' ? !!actualValue : actualValue === expectedValue;
      const status = matches ? '✅' : '❌';
      
      console.log(`      ${status} ${field}: ${actualValue}`);
      if (!matches) allFieldsPresent = false;
    });

    return allFieldsPresent;
  }

  /**
   * Test media field mapping
   */
  testMediaFieldMapping(): boolean {
    console.log('\n📸 Testing Media Field Mapping...\n');

    const submissionWithMedia: TestSubmission = {
      id: 'media-test-789',
      name: 'Media Test Tool',
      url: 'https://media-test.com',
      description: 'Test tool for media mapping',
      submitter_email: '<EMAIL>',
      submission_type: 'detailed',
      status: 'approved',
      media_assets: {
        primaryImage: 'data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAA...',
        primaryImageType: 'screenshot',
        logo: 'https://media-test.com/logo.png',
        logoSource: 'url'
      }
    };

    const transformed = this.mockTransformSubmissionToTool(submissionWithMedia);

    const mediaFields = {
      media_source: 'user_provided',
      media_updated_at: transformed.media_updated_at
    };

    console.log('   📋 Media Field Mapping Results:');
    Object.entries(mediaFields).forEach(([field, expectedValue]) => {
      const actualValue = transformed[field];
      const matches = field === 'media_updated_at' ? !!actualValue : actualValue === expectedValue;
      const status = matches ? '✅' : '❌';
      
      console.log(`      ${status} ${field}: ${actualValue}`);
    });

    return true;
  }

  // Helper methods
  private transformTags(tagsText?: string): string[] | null {
    if (!tagsText || !tagsText.trim()) return null;
    
    return tagsText
      .split(',')
      .map(tag => tag.trim())
      .filter(tag => tag.length > 0)
      .slice(0, 10);
  }

  private mockTransformSubmissionToTool(submission: TestSubmission, approvedBy?: string): any {
    const now = new Date().toISOString();
    const contentStatus = submission.submission_type === 'detailed' ? 'published' : 'draft';
    
    return {
      name: submission.name,
      slug: submission.name.toLowerCase().replace(/[^a-z0-9]+/g, '-'),
      description: submission.description,
      detailed_description: submission.detailed_description,
      use_cases: submission.use_cases,
      target_audience: submission.target_audience,
      integrations: submission.integrations,
      tags: this.transformTags(submission.tags),
      meta_keywords: submission.meta_keywords,
      content_status: contentStatus,
      submission_type: submission.submission_type,
      submission_id: submission.id,
      submitted_by: submission.submitter_email,
      submission_date: submission.submitted_at,
      approved_by: approvedBy || 'system',
      approved_at: now,
      media_source: submission.media_assets ? 'user_provided' : 'auto_collected',
      media_updated_at: now,
      created_at: now,
      updated_at: now
    };
  }

  private formatValue(value: any): string {
    if (value === null || value === undefined) return 'null';
    if (Array.isArray(value)) return `[${value.join(', ')}]`;
    if (typeof value === 'string' && value.length > 50) return value.substring(0, 50) + '...';
    return String(value);
  }
}

function runEnhancedFieldMappingTests() {
  console.log('🧪 Testing Enhanced Field Mapping in SubmissionTransformer\n');

  const tester = new TestEnhancedFieldMapping();
  
  const results = {
    tagTransformation: tester.testTagTransformation(),
    completeFieldMapping: tester.testCompleteFieldMapping(),
    submissionTracking: tester.testSubmissionTracking(),
    mediaFieldMapping: tester.testMediaFieldMapping()
  };

  const passedTests = Object.values(results).filter(Boolean).length;
  const totalTests = Object.keys(results).length;
  const successRate = Math.round((passedTests / totalTests) * 100);

  console.log('\n📊 Enhanced Field Mapping Test Results:');
  console.log(`   Tests Passed: ${passedTests}/${totalTests} (${successRate}%)`);
  
  Object.entries(results).forEach(([test, passed]) => {
    const status = passed ? '✅ PASS' : '❌ FAIL';
    console.log(`   ${status} ${test}`);
  });

  if (successRate === 100) {
    console.log('\n🎉 All enhanced field mapping tests passed!');
    console.log('\n✨ Enhanced Features Ready:');
    console.log('   • Complete field mapping from submissions to tools');
    console.log('   • Tag transformation with validation');
    console.log('   • Submission tracking and audit trail');
    console.log('   • Simplified media structure support');
    console.log('   • Enhanced SEO and content fields');
    return true;
  } else {
    console.log('\n❌ Some tests failed - field mapping needs fixes.');
    return false;
  }
}

// Run the tests
const success = runEnhancedFieldMappingTests();
process.exit(success ? 0 : 1);
