#!/usr/bin/env tsx

import * as dotenv from 'dotenv';
import { createClient } from '@supabase/supabase-js';

// Load environment variables
dotenv.config({ path: '.env.local' });

// Create Supabase client with service role
const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL!;
const supabaseKey = process.env.SUPABASE_SERVICE_ROLE_KEY!;
const supabase = createClient(supabaseUrl, supabaseKey);

async function auditDatabaseSchema() {
  console.log('🔍 Comprehensive Database Schema Audit...\n');

  try {
    // 1. Check tool_submissions table structure
    console.log('1️⃣ Auditing tool_submissions table structure:');
    
    const requiredColumns = [
      'submission_type',
      'detailed_description', 
      'features',
      'pricing_details',
      'pros',
      'cons',
      'meta_title',
      'meta_description',
      'faq'
    ];

    const existingColumns: string[] = [];
    const missingColumns: string[] = [];

    for (const column of requiredColumns) {
      try {
        const { data, error } = await supabase
          .from('tool_submissions')
          .select(column)
          .limit(1);
          
        if (error) {
          if (error.message.includes(`column "${column}" does not exist`)) {
            missingColumns.push(column);
            console.log(`   ❌ ${column} - MISSING`);
          } else {
            console.log(`   ⚠️  ${column} - ERROR: ${error.message}`);
          }
        } else {
          existingColumns.push(column);
          console.log(`   ✅ ${column} - EXISTS`);
        }
      } catch (err) {
        console.log(`   ❌ ${column} - ERROR: ${err}`);
        missingColumns.push(column);
      }
    }

    console.log(`\n📊 Summary: ${existingColumns.length}/${requiredColumns.length} columns exist`);

    // 2. Check tools table structure for mapping
    console.log('\n2️⃣ Auditing tools table structure for field mapping:');
    
    const toolsColumns = [
      'name', 'slug', 'link', 'description', 'short_description',
      'website', 'category_id', 'company', 'features', 'pricing_type',
      'pricing_details', 'pros', 'cons', 'meta_title', 'meta_description',
      'faqs', 'content_status', 'submission_type', 'ai_generation_job_id'
    ];

    const existingToolsColumns: string[] = [];
    const missingToolsColumns: string[] = [];

    for (const column of toolsColumns) {
      try {
        const { data, error } = await supabase
          .from('tools')
          .select(column)
          .limit(1);
          
        if (error) {
          if (error.message.includes(`column "${column}" does not exist`)) {
            missingToolsColumns.push(column);
            console.log(`   ❌ ${column} - MISSING`);
          } else {
            console.log(`   ⚠️  ${column} - ERROR: ${error.message}`);
          }
        } else {
          existingToolsColumns.push(column);
          console.log(`   ✅ ${column} - EXISTS`);
        }
      } catch (err) {
        console.log(`   ❌ ${column} - ERROR: ${err}`);
        missingToolsColumns.push(column);
      }
    }

    // 3. Test data insertion
    console.log('\n3️⃣ Testing data insertion capabilities:');
    
    const testSubmission = {
      name: 'Test Tool',
      url: 'https://test.com',
      description: 'Test description',
      category: 'test',
      submitter_email: '<EMAIL>',
      submission_type: 'detailed',
      detailed_description: 'Test detailed description',
      features: 'Test features',
      pricing_details: 'Test pricing',
      pros: 'Test pros',
      cons: 'Test cons',
      meta_title: 'Test meta title',
      meta_description: 'Test meta description',
      faq: 'Test FAQ',
      status: 'pending'
    };

    try {
      const { data, error } = await supabase
        .from('tool_submissions')
        .insert(testSubmission)
        .select()
        .single();

      if (error) {
        console.log(`   ❌ Test insertion failed: ${error.message}`);
      } else {
        console.log(`   ✅ Test insertion successful: ${data.id}`);
        
        // Clean up test data
        await supabase
          .from('tool_submissions')
          .delete()
          .eq('id', data.id);
        console.log(`   🧹 Test data cleaned up`);
      }
    } catch (err) {
      console.log(`   ❌ Test insertion error: ${err}`);
    }

    // 4. Generate migration script if needed
    if (missingColumns.length > 0) {
      console.log('\n4️⃣ Generating safe migration script:');
      console.log('```sql');
      console.log('-- Safe migration script for missing columns');
      
      for (const column of missingColumns) {
        const columnDef = getColumnDefinition(column);
        console.log(`DO $$ BEGIN`);
        console.log(`  IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name='tool_submissions' AND column_name='${column}') THEN`);
        console.log(`    ALTER TABLE tool_submissions ADD COLUMN ${column} ${columnDef};`);
        console.log(`  END IF;`);
        console.log(`END $$;`);
        console.log('');
      }
      console.log('```');
    } else {
      console.log('\n✅ All required columns exist - no migration needed!');
    }

    // 5. Check existing data
    console.log('\n5️⃣ Checking existing submission data:');
    const { data: submissions, error: submissionsError } = await supabase
      .from('tool_submissions')
      .select('id, name, submission_type, status, submitted_at')
      .limit(10);

    if (submissionsError) {
      console.log(`   ❌ Error fetching submissions: ${submissionsError.message}`);
    } else {
      console.log(`   📊 Found ${submissions?.length || 0} existing submissions`);
      submissions?.forEach((sub, index) => {
        console.log(`     ${index + 1}. ${sub.name} - Type: ${sub.submission_type || 'NULL'} - Status: ${sub.status}`);
      });
    }

  } catch (error) {
    console.error('❌ Audit failed:', error);
  }
}

function getColumnDefinition(column: string): string {
  const definitions: Record<string, string> = {
    'submission_type': "VARCHAR(20) DEFAULT 'simple' CHECK (submission_type IN ('simple', 'detailed'))",
    'detailed_description': 'TEXT',
    'features': 'TEXT',
    'pricing_details': 'TEXT',
    'pros': 'TEXT',
    'cons': 'TEXT',
    'meta_title': 'VARCHAR(255)',
    'meta_description': 'TEXT',
    'faq': 'TEXT'
  };
  
  return definitions[column] || 'TEXT';
}

auditDatabaseSchema()
  .then(() => {
    console.log('\n🎉 Database audit complete!');
    process.exit(0);
  })
  .catch((error) => {
    console.error('❌ Audit failed:', error);
    process.exit(1);
  });
