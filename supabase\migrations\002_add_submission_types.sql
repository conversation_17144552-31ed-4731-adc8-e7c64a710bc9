-- Add submission type and detailed submission fields to tool_submissions table
-- Migration: 002_add_submission_types.sql

-- Add submission_type column to distinguish between simple and detailed submissions
ALTER TABLE tool_submissions 
ADD COLUMN submission_type VARCHAR(20) DEFAULT 'simple' CHECK (submission_type IN ('simple', 'detailed'));

-- Add detailed submission fields
ALTER TABLE tool_submissions 
ADD COLUMN detailed_description TEXT,
ADD COLUMN features TEXT,
ADD COLUMN pricing_details TEXT,
ADD COLUMN pros TEXT,
ADD COLUMN cons TEXT,
ADD COLUMN meta_title VARCHAR(255),
ADD COLUMN meta_description TEXT,
ADD COLUMN faq TEXT;

-- Add indexes for better query performance
CREATE INDEX idx_tool_submissions_type ON tool_submissions(submission_type);
CREATE INDEX idx_tool_submissions_status_type ON tool_submissions(status, submission_type);

-- Add comments for documentation
COMMENT ON COLUMN tool_submissions.submission_type IS 'Type of submission: simple (AI-generated content) or detailed (user-provided content)';
COMMENT ON COLUMN tool_submissions.detailed_description IS 'Comprehensive description for detailed submissions';
COMMENT ON COLUMN tool_submissions.features IS 'Feature list for detailed submissions';
COMMENT ON COLUMN tool_submissions.pricing_details IS 'Detailed pricing information for detailed submissions';
COMMENT ON COLUMN tool_submissions.pros IS 'Advantages/pros for detailed submissions';
COMMENT ON COLUMN tool_submissions.cons IS 'Disadvantages/cons for detailed submissions';
COMMENT ON COLUMN tool_submissions.meta_title IS 'SEO meta title for detailed submissions';
COMMENT ON COLUMN tool_submissions.meta_description IS 'SEO meta description for detailed submissions';
COMMENT ON COLUMN tool_submissions.faq IS 'FAQ content for detailed submissions';

-- Update existing submissions to have 'simple' type (for backward compatibility)
UPDATE tool_submissions 
SET submission_type = 'simple' 
WHERE submission_type IS NULL;
