/**
 * AI Dude Content Generator Tests
 * Tests the new AI Dude methodology for content generation
 */

import { AIContentGenerator } from '@/lib/ai/content-generator';
import { PromptManager } from '@/lib/ai/prompt-manager';
import { ModelSelector } from '@/lib/ai/model-selector';

// Mock the AI providers
jest.mock('@/lib/ai/providers/openai-client');
jest.mock('@/lib/ai/providers/openrouter-client');

// Mock PromptManager
jest.mock('@/lib/ai/prompt-manager', () => ({
  PromptManager: {
    buildAIDudeSystemPrompt: jest.fn(),
    buildAIDudeUserPrompt: jest.fn(),
    buildPartialSystemPrompt: jest.fn(),
    buildPartialUserPrompt: jest.fn(),
    getAIDudeDatabaseSchema: jest.fn(),
    getSectionRequirements: jest.fn(),
    extractJsonFromResponse: jest.fn(),
    processAIDudeResponse: jest.fn(),
    processPartialAIDudeResponse: jest.fn(),
    calculateTokenCount: jest.fn()
  }
}));

// Mock ModelSelector
jest.mock('@/lib/ai/model-selector', () => ({
  ModelSelector: {
    selectOptimalModel: jest.fn()
  }
}));

describe('AIContentGenerator - AI Dude Methodology', () => {
  let generator: AIContentGenerator;
  let mockOpenAIClient: any;
  let mockOpenRouterClient: any;

  beforeEach(() => {
    // Reset all mocks
    jest.clearAllMocks();

    // Create generator instance
    generator = new AIContentGenerator();

    // Mock AI clients
    mockOpenAIClient = {
      generateContent: jest.fn()
    };
    mockOpenRouterClient = {
      generateContent: jest.fn()
    };

    // Set up the generator with mocked clients
    (generator as any).openaiClient = mockOpenAIClient;
    (generator as any).openrouterClient = mockOpenRouterClient;

    // Mock PromptManager methods
    (PromptManager.buildAIDudeSystemPrompt as jest.Mock).mockResolvedValue(
      'You are AI Dude, the irreverent curator of AI tools...'
    );
    (PromptManager.buildAIDudeUserPrompt as jest.Mock).mockResolvedValue(
      'Tool URL: https://example.com\n\nScraped Content:\nTest content'
    );
    (PromptManager.getAIDudeDatabaseSchema as jest.Mock).mockReturnValue({
      name: 'string',
      description: 'string',
      features: 'array'
    });
    (PromptManager.extractJsonFromResponse as jest.Mock).mockReturnValue({
      name: 'Test Tool',
      description: 'A test tool for AI Dude methodology'
    });
    (PromptManager.processAIDudeResponse as jest.Mock).mockReturnValue({
      name: 'Test Tool',
      description: 'A test tool for AI Dude methodology',
      detailed_description: 'Detailed description',
      features: ['Feature 1', 'Feature 2'],
      pros_and_cons: { pros: ['Pro 1'], cons: ['Con 1'] }
    });
    (PromptManager.calculateTokenCount as jest.Mock).mockReturnValue(100);
    (PromptManager.processPartialAIDudeResponse as jest.Mock).mockReturnValue({
      name: 'Test Tool',
      description: 'A test tool for AI Dude methodology',
      features: ['New Feature 1', 'New Feature 2']
    });

    // Mock ModelSelector
    (ModelSelector.selectOptimalModel as jest.Mock).mockReturnValue({
      provider: 'openai',
      model: 'gpt-4',
      maxTokens: 4000
    });
  });

  describe('generateContent (AI Dude)', () => {
    it('should generate content using AI Dude methodology', async () => {
      // Mock successful AI response
      mockOpenAIClient.generateContent.mockResolvedValue({
        content: JSON.stringify({
          name: 'Test Tool',
          description: 'A test tool for AI Dude methodology'
        }),
        tokenUsage: { total: 150 }
      });

      const result = await generator.generateContent(
        'Test scraped content',
        'https://example.com'
      );

      expect(result.success).toBe(true);
      expect(result.methodology).toBe('ai_dude');
      expect(result.content).toBeDefined();
      expect(PromptManager.buildAIDudeSystemPrompt).toHaveBeenCalled();
      expect(PromptManager.buildAIDudeUserPrompt).toHaveBeenCalledWith(
        'Test scraped content',
        'https://example.com'
      );
    });

    it('should handle AI generation errors gracefully', async () => {
      // Mock AI error
      mockOpenAIClient.generateContent.mockRejectedValue(
        new Error('AI generation failed')
      );

      const result = await generator.generateContent(
        'Test content',
        'https://example.com'
      );

      expect(result.success).toBe(false);
      expect(result.error).toContain('AI generation failed');
      expect(result.methodology).toBe('ai_dude');
    });

    it('should use OpenRouter as fallback when OpenAI fails', async () => {
      // Mock OpenAI failure and OpenRouter success
      mockOpenAIClient.generateContent.mockRejectedValue(
        new Error('OpenAI failed')
      );
      mockOpenRouterClient.generateContent.mockResolvedValue({
        content: JSON.stringify({
          name: 'Test Tool',
          description: 'Generated with OpenRouter'
        }),
        tokenUsage: { total: 200 }
      });

      // Mock model selector to return OpenRouter
      (ModelSelector.selectOptimalModel as jest.Mock).mockReturnValue({
        provider: 'openrouter',
        model: 'google/gemini-2.0-flash-exp',
        maxTokens: 8000
      });

      const result = await generator.generateContent(
        'Test content',
        'https://example.com'
      );

      expect(result.success).toBe(true);
      expect(mockOpenRouterClient.generateContent).toHaveBeenCalled();
    });
  });

  describe('generatePartialContentAIDude', () => {
    beforeEach(() => {
      (PromptManager.buildPartialSystemPrompt as jest.Mock).mockResolvedValue(
        'Generate only the features section...'
      );
      (PromptManager.buildPartialUserPrompt as jest.Mock).mockResolvedValue(
        'Existing tool data: {...}\nNew content: test'
      );
      (PromptManager.getSectionRequirements as jest.Mock).mockReturnValue(
        'Features should be concise and actionable'
      );
    });

    it('should generate partial content for specific sections', async () => {
      mockOpenAIClient.generateContent.mockResolvedValue({
        content: JSON.stringify({
          features: ['New Feature 1', 'New Feature 2']
        }),
        tokenUsage: { total: 100 }
      });

      const existingData = {
        name: 'Existing Tool',
        description: 'Existing description'
      };

      const result = await generator.generatePartialContentAIDude(
        'features',
        existingData,
        'New scraped content',
        'https://example.com'
      );

      expect(result.success).toBe(true);
      expect(result.methodology).toBe('ai_dude_partial');
      expect(PromptManager.buildPartialSystemPrompt).toHaveBeenCalledWith(
        'features',
        'Features should be concise and actionable'
      );
      expect(PromptManager.buildPartialUserPrompt).toHaveBeenCalledWith(
        existingData,
        'New scraped content',
        'https://example.com',
        'features'
      );
    });

    it('should handle different section types', async () => {
      const sections = ['features', 'pricing', 'pros_cons', 'seo', 'faqs', 'releases'];

      for (const section of sections) {
        mockOpenAIClient.generateContent.mockResolvedValue({
          content: JSON.stringify({ [section]: 'Generated content' }),
          tokenUsage: { total: 50 }
        });

        const result = await generator.generatePartialContentAIDude(
          section,
          {},
          'Test content',
          'https://example.com'
        );

        expect(result.success).toBe(true);
        expect(PromptManager.getSectionRequirements).toHaveBeenCalledWith(section);
      }
    });

    it('should handle partial generation errors', async () => {
      mockOpenAIClient.generateContent.mockRejectedValue(
        new Error('Partial generation failed')
      );

      const result = await generator.generatePartialContentAIDude(
        'features',
        {},
        'Test content',
        'https://example.com'
      );

      expect(result.success).toBe(false);
      expect(result.error).toContain('Partial generation failed');
    });
  });

  describe('AI Dude Content Validation', () => {
    it('should validate generated content structure', async () => {
      const mockContent = {
        name: 'Test Tool',
        description: 'Test description with AI Dude\'s snarky tone and witty commentary',
        short_description: 'Short test description with AI Dude style',
        detailed_description: 'This is a detailed test description with enough content to meet the minimum 150 character requirement for AI Dude methodology validation. It includes snarky commentary and witty observations.',
        company: 'Test Company',
        category_id: 'ai-tools',
        features: ['Feature 1', 'Feature 2', 'Feature 3', 'Feature 4'],
        pricing: { type: 'Freemium', plans: [], details: 'Free with premium options' },
        pros_and_cons: {
          pros: ['Pro 1', 'Pro 2', 'Pro 3', 'Pro 4'],
          cons: ['Con 1', 'Con 2', 'Con 3', 'Con 4']
        },
        hashtags: ['#ai', '#tool', '#test', '#dude', '#snarky', '#witty'],
        meta_title: 'Test Tool - AI Tool Directory',
        meta_description: 'Test tool for AI Dude methodology testing with comprehensive validation and quality assurance for content generation systems and workflows.'
      };

      // Test the private validation method through reflection
      const validationResult = await (generator as any).validateAIDudeContent(mockContent);

      expect(validationResult.isValid).toBe(true);
      expect(validationResult.score).toBeGreaterThan(0);
      expect(validationResult.issues).toHaveLength(0);
    });

    it('should detect missing required fields', async () => {
      const incompleteContent = {
        name: 'Test Tool'
        // Missing required fields
      };

      const validationResult = await (generator as any).validateAIDudeContent(incompleteContent);

      expect(validationResult.isValid).toBe(false);
      expect(validationResult.issues.length).toBeGreaterThan(0);
      expect(validationResult.issues.some((issue: any) => 
        issue.includes('description')
      )).toBe(true);
    });
  });
});
