#!/usr/bin/env tsx

import * as dotenv from 'dotenv';
import { createClient } from '@supabase/supabase-js';

// Load environment variables
dotenv.config({ path: '.env.local' });

// Create Supabase client with service role
const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL!;
const supabaseKey = process.env.SUPABASE_SERVICE_ROLE_KEY!;
const supabase = createClient(supabaseUrl, supabaseKey);

async function testRawSQL() {
  console.log('🧪 Testing with Raw SQL (bypassing schema cache)...\n');

  try {
    // 1. Test tool_submissions insert with all fields
    console.log('1️⃣ Testing tool_submissions insert:');
    
    const { data: insertResult, error: insertError } = await supabase.rpc('exec_sql', {
      sql: `
        INSERT INTO tool_submissions (
          name, url, description, category, submitter_name, submitter_email,
          submission_type, detailed_description, features, pricing_type, pricing_details,
          pros, cons, meta_title, meta_description, faq, status, priority,
          submitted_at
        ) VALUES (
          'Raw SQL Test Tool',
          'https://rawsql-test.com',
          'Testing with raw SQL',
          'test-category',
          'SQL Tester',
          '<EMAIL>',
          'detailed',
          'Detailed description via raw SQL',
          'Feature 1, Feature 2, Feature 3',
          'freemium',
          'Free: Basic features, Pro: $10/month',
          'Fast, Reliable, Easy to use',
          'Limited free tier, Requires signup',
          'Raw SQL Test Tool - Best Testing Tool',
          'Test your SQL queries with our amazing tool',
          'Q: How to use? A: Just run your queries!',
          'pending',
          'normal',
          NOW()
        ) RETURNING id;
      `
    });

    if (insertError) {
      console.log(`   ❌ Insert failed: ${insertError.message}`);
    } else {
      console.log(`   ✅ Insert successful via raw SQL`);
    }

    // 2. Test tools table insert
    console.log('\n2️⃣ Testing tools table insert:');
    
    const { data: toolInsertResult, error: toolInsertError } = await supabase.rpc('exec_sql', {
      sql: `
        INSERT INTO tools (
          name, slug, link, description, short_description, website, category_id,
          company, features, pricing_type, pricing_details, pros, cons,
          meta_title, meta_description, content_status, submission_type,
          is_verified, is_claimed, created_at, updated_at, published_at
        ) VALUES (
          'Raw SQL Tool',
          'raw-sql-tool',
          '/tools/raw-sql-tool',
          'A tool created via raw SQL',
          'Raw SQL tool',
          'https://rawsql-tool.com',
          'test-category',
          'SQL Company',
          'SQL Features, Raw Queries, Direct Access',
          'paid',
          'Pro: $20/month, Enterprise: $100/month',
          'Direct SQL access, No ORM overhead, Fast queries',
          'Requires SQL knowledge, No GUI, Complex setup',
          'Raw SQL Tool - Direct Database Access',
          'Access your database directly with raw SQL queries',
          'published',
          'detailed',
          true,
          false,
          NOW(),
          NOW(),
          NOW()
        ) RETURNING id;
      `
    });

    if (toolInsertError) {
      console.log(`   ❌ Tool insert failed: ${toolInsertError.message}`);
    } else {
      console.log(`   ✅ Tool insert successful via raw SQL`);
    }

    // 3. Test update operations
    console.log('\n3️⃣ Testing update operations:');
    
    const { data: updateResult, error: updateError } = await supabase.rpc('exec_sql', {
      sql: `
        UPDATE tool_submissions 
        SET 
          status = 'approved',
          reviewed_by = 'sql-admin',
          reviewed_at = NOW(),
          review_notes = 'Approved via raw SQL test'
        WHERE name = 'Raw SQL Test Tool'
        RETURNING id, status, reviewed_by;
      `
    });

    if (updateError) {
      console.log(`   ❌ Update failed: ${updateError.message}`);
    } else {
      console.log(`   ✅ Update successful via raw SQL`);
    }

    // 4. Test data retrieval
    console.log('\n4️⃣ Testing data retrieval:');
    
    const { data: selectResult, error: selectError } = await supabase.rpc('exec_sql', {
      sql: `
        SELECT 
          ts.name,
          ts.submission_type,
          ts.status,
          ts.reviewed_by,
          t.content_status
        FROM tool_submissions ts
        LEFT JOIN tools t ON t.name LIKE '%Raw SQL%'
        WHERE ts.name LIKE '%Raw SQL%'
        ORDER BY ts.submitted_at DESC;
      `
    });

    if (selectError) {
      console.log(`   ❌ Select failed: ${selectError.message}`);
    } else {
      console.log(`   ✅ Select successful via raw SQL`);
      console.log(`   📊 Found records:`, selectResult);
    }

    // 5. Cleanup
    console.log('\n5️⃣ Cleaning up test data:');
    
    const { error: cleanupError } = await supabase.rpc('exec_sql', {
      sql: `
        DELETE FROM tools WHERE name LIKE '%Raw SQL%';
        DELETE FROM tool_submissions WHERE name LIKE '%Raw SQL%';
      `
    });

    if (cleanupError) {
      console.log(`   ❌ Cleanup failed: ${cleanupError.message}`);
    } else {
      console.log(`   🧹 Cleanup successful`);
    }

  } catch (error) {
    console.error('❌ Raw SQL test failed:', error);
  }
}

testRawSQL()
  .then(() => {
    console.log('\n🎉 Raw SQL test complete!');
    process.exit(0);
  })
  .catch((error) => {
    console.error('❌ Test failed:', error);
    process.exit(1);
  });
