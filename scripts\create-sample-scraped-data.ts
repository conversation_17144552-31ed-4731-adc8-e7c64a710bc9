/**
 * Create Sample Scraped Data for Testing
 * This script adds sample scraped data to existing tools for testing the bulk processing system
 */

import { createClient } from '@supabase/supabase-js';
import * as dotenv from 'dotenv';

// Load environment variables
dotenv.config({ path: '.env.local' });

const supabase = createClient(
  process.env.NEXT_PUBLIC_SUPABASE_URL!,
  process.env.SUPABASE_SERVICE_ROLE_KEY!
);

const sampleScrapedData = {
  title: "AI Tool - Advanced Features",
  description: "A comprehensive AI tool with advanced features for content generation and analysis.",
  content: `# AI Tool Overview

This is a powerful AI tool that provides comprehensive features for content generation, analysis, and optimization.

## Key Features

- **Advanced AI Models**: Utilizes state-of-the-art language models
- **Real-time Processing**: Fast and efficient content generation
- **Multi-language Support**: Supports over 50 languages
- **API Integration**: Easy integration with existing workflows
- **Custom Training**: Ability to train on custom datasets

## Pricing

### Free Plan
- 1,000 requests per month
- Basic features
- Community support

### Pro Plan - $29/month
- 50,000 requests per month
- Advanced features
- Priority support
- Custom integrations

### Enterprise Plan - Custom
- Unlimited requests
- White-label solution
- Dedicated support
- Custom development

## Use Cases

1. **Content Creation**: Generate blog posts, articles, and marketing copy
2. **Data Analysis**: Analyze large datasets and extract insights
3. **Customer Support**: Automated responses and ticket routing
4. **Translation**: Real-time translation services
5. **Code Generation**: Generate and optimize code snippets

## Getting Started

1. Sign up for an account
2. Choose your plan
3. Get your API key
4. Start building with our comprehensive documentation

## Support

Our support team is available 24/7 to help you get the most out of our platform.

Contact <NAME_EMAIL> or visit our help center.`,
  url: "https://example.com",
  metadata: {
    scrapedAt: new Date().toISOString(),
    contentLength: 1500,
    hasStructure: true,
    quality: "high"
  }
};

async function createSampleData() {
  try {
    console.log('🔍 Creating sample scraped data for testing...');

    // Get first 3 tools to add sample data
    const { data: tools, error: toolsError } = await supabase
      .from('tools')
      .select('id, name, website')
      .limit(3);

    if (toolsError) {
      throw new Error(`Failed to fetch tools: ${toolsError.message}`);
    }

    if (!tools || tools.length === 0) {
      throw new Error('No tools found in database');
    }

    console.log(`📊 Found ${tools.length} tools to update`);

    // Update each tool with sample scraped data
    for (const tool of tools) {
      const customScrapedData = {
        ...sampleScrapedData,
        title: `${tool.name} - Advanced Features`,
        url: tool.website || `https://${tool.name.toLowerCase().replace(/\s+/g, '-')}.com`,
        content: sampleScrapedData.content.replace('AI Tool', tool.name)
      };

      const { error: updateError } = await supabase
        .from('tools')
        .update({
          scraped_data: customScrapedData,
          last_scraped_at: new Date().toISOString(),
          ai_generation_status: 'pending'
        })
        .eq('id', tool.id);

      if (updateError) {
        console.error(`❌ Failed to update tool ${tool.name}:`, updateError);
      } else {
        console.log(`✅ Updated tool: ${tool.name}`);
      }
    }

    // Verify the updates
    const { data: updatedTools, error: verifyError } = await supabase
      .from('tools')
      .select('id, name, scraped_data, ai_generation_status, last_scraped_at')
      .not('scraped_data', 'is', null);

    if (verifyError) {
      throw new Error(`Failed to verify updates: ${verifyError.message}`);
    }

    console.log(`🎉 Successfully created sample data for ${updatedTools?.length || 0} tools`);
    
    if (updatedTools && updatedTools.length > 0) {
      updatedTools.forEach(tool => {
        console.log(`- ${tool.name}: status=${tool.ai_generation_status}, scraped=${!!tool.scraped_data}`);
      });
    }

  } catch (error) {
    console.error('❌ Error creating sample data:', error);
    process.exit(1);
  }
}

// Run the script
createSampleData()
  .then(() => {
    console.log('✅ Sample data creation completed');
    process.exit(0);
  })
  .catch((error) => {
    console.error('❌ Script failed:', error);
    process.exit(1);
  });
