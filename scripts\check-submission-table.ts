#!/usr/bin/env tsx

import * as dotenv from 'dotenv';
import { createClient } from '@supabase/supabase-js';

// Load environment variables
dotenv.config({ path: '.env.local' });

// Create Supabase client with service role
const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL!;
const supabaseKey = process.env.SUPABASE_SERVICE_ROLE_KEY!;
const supabase = createClient(supabaseUrl, supabaseKey);

async function checkSubmissionTable() {
  console.log('🔍 Checking tool_submissions table structure...');
  
  try {
    // Check if submission_type column exists
    const { data, error } = await supabase
      .from('tool_submissions')
      .select('submission_type')
      .limit(1);
      
    if (error) {
      if (error.message.includes('column "submission_type" does not exist')) {
        console.log('❌ submission_type column does not exist - migration needed');
        console.log('📋 Please run the migration: supabase/migrations/002_add_submission_types.sql');
      } else {
        console.log('❌ Error:', error.message);
      }
    } else {
      console.log('✅ submission_type column exists');
      
      // Check existing submissions
      const { data: submissions, error: submissionsError } = await supabase
        .from('tool_submissions')
        .select('id, name, submission_type, status')
        .limit(5);
        
      if (!submissionsError && submissions) {
        console.log(`📊 Found ${submissions.length} existing submissions:`);
        submissions.forEach((sub, index) => {
          console.log(`  ${index + 1}. ${sub.name} - Type: ${sub.submission_type || 'NULL'} - Status: ${sub.status}`);
        });
      }
    }
    
  } catch (error) {
    console.error('❌ Script error:', error);
  }
}

checkSubmissionTable()
  .then(() => {
    console.log('\n🎉 Check complete!');
    process.exit(0);
  })
  .catch((error) => {
    console.error('❌ Check failed:', error);
    process.exit(1);
  });
