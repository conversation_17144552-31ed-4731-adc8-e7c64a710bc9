#!/usr/bin/env tsx

/**
 * Check Meta Keywords Column Script
 *
 * This script checks if the meta_keywords column exists in the tools table
 * and provides information about its current state.
 */

import { config } from 'dotenv';
import { createClient } from '@supabase/supabase-js';

// Load environment variables
config({ path: '.env.local' });

const supabase = createClient(
  process.env.NEXT_PUBLIC_SUPABASE_URL!,
  process.env.SUPABASE_SERVICE_ROLE_KEY!
);

async function checkMetaKeywordsColumn() {
  console.log('🔍 Checking meta_keywords column status...\n');

  try {
    // Try to query the meta_keywords column directly to see if it exists
    const { data: testQuery, error: testError } = await supabase
      .from('tools')
      .select('id, meta_keywords')
      .limit(1);

    if (testError) {
      if (testError.message.includes('column "meta_keywords" does not exist')) {
        console.log('❌ meta_keywords column NOT found in tools table');
        console.log('   Migration needs to be applied');
        console.log('   Run: npx tsx scripts/apply-meta-keywords-migration.ts');
        return false;
      } else {
        console.error('❌ Error checking column:', testError.message);
        return false;
      }
    }

    console.log('✅ meta_keywords column exists in tools table');
    console.log('   Test query successful');

    // Check for any tools with meta_keywords
    const { data: toolsWithKeywords, error: toolsError } = await supabase
      .from('tools')
      .select('id, name, meta_keywords')
      .not('meta_keywords', 'is', null)
      .neq('meta_keywords', '')
      .limit(5);

    if (toolsError) {
      console.error('❌ Error querying tools with meta_keywords:', toolsError.message);
      return false;
    }

    if (toolsWithKeywords && toolsWithKeywords.length > 0) {
      console.log(`\nℹ️  Found ${toolsWithKeywords.length} tools with meta_keywords`);
      toolsWithKeywords.forEach((tool: any) => {
        const keywords = tool.meta_keywords || '';
        const keywordCount = keywords.split(',').filter((k: string) => k.trim()).length;
        console.log(`   - ${tool.name}: ${keywordCount} keyword(s)`);
      });
    } else {
      console.log('\nℹ️  No tools found with meta_keywords');
      console.log('   You may want to populate meta_keywords for existing tools');
    }

    // Check total tools count
    const { count: totalTools, error: countError } = await supabase
      .from('tools')
      .select('*', { count: 'exact', head: true });

    if (!countError && totalTools !== null) {
      console.log(`\n📊 Total tools in database: ${totalTools}`);
    }

    return true;

  } catch (error) {
    console.error('❌ Unexpected error:', error);
    return false;
  }
}

// Execute the check
if (require.main === module) {
  checkMetaKeywordsColumn()
    .then((success) => {
      if (success) {
        console.log('\n🎉 meta_keywords column check completed successfully!');
      } else {
        console.log('\n⚠️  meta_keywords column check failed');
        process.exit(1);
      }
    })
    .catch(console.error);
}

export { checkMetaKeywordsColumn };
