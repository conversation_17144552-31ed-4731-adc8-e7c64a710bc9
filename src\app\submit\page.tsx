'use client';

import { useState } from 'react';
import { SimpleSubmissionForm } from '@/components/forms/SimpleSubmissionForm';
import { DetailedSubmissionForm } from '@/components/forms/DetailedSubmissionForm';

export default function SubmitToolPage() {
  const [submissionType, setSubmissionType] = useState<'simple' | 'detailed' | null>(null);

  const handleSuccess = () => {
    // Redirect to success page
    window.location.href = '/submit/success';
  };

  const handleBack = () => {
    setSubmissionType(null);
  };

  if (submissionType === 'simple') {
    return (
      <div className="min-h-screen bg-zinc-900 text-white">
        <div className="container mx-auto px-4 py-8">
          <div className="max-w-4xl mx-auto">
            <SimpleSubmissionForm onSuccess={handleSuccess} onBack={handleBack} />
          </div>
        </div>
      </div>
    );
  }

  if (submissionType === 'detailed') {
    return (
      <div className="min-h-screen bg-zinc-900 text-white">
        <div className="container mx-auto px-4 py-8">
          <div className="max-w-4xl mx-auto">
            <DetailedSubmissionForm onSuccess={handleSuccess} onBack={handleBack} />
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-zinc-900 text-white">
      <div className="container mx-auto px-4 py-8">
        <div className="max-w-4xl mx-auto">
          {/* Header */}
          <div className="text-center mb-12">
            <h1 className="text-4xl font-bold mb-4">Submit Your AI Tool</h1>
            <p className="text-xl text-gray-300 mb-8">
              Choose how you'd like to submit your AI tool to our directory
            </p>
          </div>

          {/* Submission Type Selection */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-8 mb-12">
            {/* Simple Submit Option */}
            <div className="bg-zinc-800 border border-zinc-700 rounded-lg p-8 hover:border-orange-500 transition-all duration-200 cursor-pointer group"
                 onClick={() => setSubmissionType('simple')}>
              <div className="text-center">
                <div className="text-4xl mb-4">⚡</div>
                <h3 className="text-2xl font-bold mb-4 group-hover:text-orange-400">Simple Submit</h3>
                <p className="text-gray-300 mb-6">
                  Quick and easy! Just provide your tool's URL and basic info.
                  We'll handle the rest with our AI content generation system.
                </p>

                <div className="bg-zinc-700/50 rounded-lg p-4 mb-6">
                  <h4 className="font-semibold mb-2 text-orange-400">What you provide:</h4>
                  <ul className="text-sm text-gray-300 space-y-1 text-left">
                    <li>• Website URL</li>
                    <li>• Tool name</li>
                    <li>• Brief description</li>
                    <li>• Category & contact info</li>
                  </ul>
                </div>

                <div className="bg-zinc-700/50 rounded-lg p-4 mb-6">
                  <h4 className="font-semibold mb-2 text-blue-400">What we generate:</h4>
                  <ul className="text-sm text-gray-300 space-y-1 text-left">
                    <li>• Detailed descriptions</li>
                    <li>• Feature lists</li>
                    <li>• Pricing information</li>
                    <li>• Pros & cons</li>
                    <li>• SEO content</li>
                    <li>• FAQ section</li>
                  </ul>
                </div>

                <div className="text-sm text-gray-400">
                  ⏱️ Review time: 24-48 hours
                </div>
              </div>
            </div>

            {/* Detailed Submit Option */}
            <div className="bg-zinc-800 border border-zinc-700 rounded-lg p-8 hover:border-orange-500 transition-all duration-200 cursor-pointer group"
                 onClick={() => setSubmissionType('detailed')}>
              <div className="text-center">
                <div className="text-4xl mb-4">📝</div>
                <h3 className="text-2xl font-bold mb-4 group-hover:text-orange-400">Detailed Submission</h3>
                <p className="text-gray-300 mb-6">
                  Complete control! Fill out all the details yourself for
                  maximum accuracy and customization.
                </p>

                <div className="bg-zinc-700/50 rounded-lg p-4 mb-6">
                  <h4 className="font-semibold mb-2 text-green-400">What you provide:</h4>
                  <ul className="text-sm text-gray-300 space-y-1 text-left">
                    <li>• All basic information</li>
                    <li>• Detailed descriptions</li>
                    <li>• Complete feature lists</li>
                    <li>• Pricing details</li>
                    <li>• Pros & cons</li>
                    <li>• SEO metadata</li>
                    <li>• FAQ content</li>
                  </ul>
                </div>

                <div className="bg-zinc-700/50 rounded-lg p-4 mb-6">
                  <h4 className="font-semibold mb-2 text-purple-400">Benefits:</h4>
                  <ul className="text-sm text-gray-300 space-y-1 text-left">
                    <li>• Complete accuracy</li>
                    <li>• Custom messaging</li>
                    <li>• Faster approval</li>
                    <li>• Your exact wording</li>
                  </ul>
                </div>

                <div className="text-sm text-gray-400">
                  ⏱️ Review time: 12-24 hours
                </div>
              </div>
            </div>
          </div>

          {/* Process Overview */}
          <div className="bg-zinc-800 border border-zinc-700 rounded-lg p-6">
            <h3 className="text-lg font-semibold mb-4 text-center">📋 Submission Process</h3>
            <div className="grid grid-cols-1 md:grid-cols-4 gap-4 text-center">
              <div className="flex flex-col items-center">
                <div className="w-8 h-8 bg-orange-500 rounded-full flex items-center justify-center text-white font-bold mb-2">1</div>
                <div className="text-sm text-gray-300">Submit Tool</div>
              </div>
              <div className="flex flex-col items-center">
                <div className="w-8 h-8 bg-blue-500 rounded-full flex items-center justify-center text-white font-bold mb-2">2</div>
                <div className="text-sm text-gray-300">Admin Review</div>
              </div>
              <div className="flex flex-col items-center">
                <div className="w-8 h-8 bg-purple-500 rounded-full flex items-center justify-center text-white font-bold mb-2">3</div>
                <div className="text-sm text-gray-300">Content Generation<br/><span className="text-xs">(Simple only)</span></div>
              </div>
              <div className="flex flex-col items-center">
                <div className="w-8 h-8 bg-green-500 rounded-full flex items-center justify-center text-white font-bold mb-2">4</div>
                <div className="text-sm text-gray-300">Published</div>
              </div>
            </div>
          </div>

          {/* Guidelines */}
          <div className="mt-8 bg-zinc-800 border border-zinc-700 rounded-lg p-6">
            <h3 className="text-lg font-semibold mb-3">📋 Submission Guidelines</h3>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div>
                <h4 className="font-medium mb-2 text-orange-400">Requirements</h4>
                <ul className="space-y-1 text-sm text-gray-300">
                  <li>• Tool must be publicly accessible</li>
                  <li>• Working website URL required</li>
                  <li>• Accurate information only</li>
                  <li>• No duplicate submissions</li>
                </ul>
              </div>
              <div>
                <h4 className="font-medium mb-2 text-blue-400">Review Process</h4>
                <ul className="space-y-1 text-sm text-gray-300">
                  <li>• Manual editorial review</li>
                  <li>• Quality and accuracy check</li>
                  <li>• Email notifications sent</li>
                  <li>• Feedback provided if rejected</li>
                </ul>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
