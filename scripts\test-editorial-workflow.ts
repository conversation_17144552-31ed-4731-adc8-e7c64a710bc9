#!/usr/bin/env tsx

import * as dotenv from 'dotenv';
import { createClient } from '@supabase/supabase-js';

// Load environment variables
dotenv.config({ path: '.env.local' });

// Create Supabase client
const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL!;
const supabaseKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!;
const supabase = createClient(supabaseUrl, supabaseKey);

async function testEditorialWorkflow() {
  console.log('🧪 Testing Editorial Workflow...\n');

  try {
    // 1. Find a valid AI job for testing
    console.log('1️⃣ Finding valid AI job for testing...');
    
    const { data: aiJobs, error: aiJobsError } = await supabase
      .from('ai_generation_jobs')
      .select(`
        id, 
        tool_id, 
        status, 
        job_type,
        tools!ai_generation_jobs_tool_id_fkey (
          id,
          name,
          content_status,
          editorial_review_id
        )
      `)
      .eq('status', 'completed')
      .eq('job_type', 'generate')
      .limit(5);

    if (aiJobsError) {
      throw new Error(`Failed to fetch AI jobs: ${aiJobsError.message}`);
    }

    // Find an AI job without an editorial review
    const testJob = aiJobs.find(job => {
      const tool = job.tools as any;
      return tool && !tool.editorial_review_id;
    });

    if (!testJob) {
      console.log('❌ No suitable AI job found for testing');
      console.log('💡 All AI jobs already have editorial reviews');
      return;
    }

    const tool = testJob.tools as any;
    console.log(`✅ Found test job: ${testJob.id}`);
    console.log(`   Tool: ${tool.name} (${tool.id})`);
    console.log(`   Current status: ${tool.content_status}`);

    // 2. Simulate editorial approval
    console.log('\n2️⃣ Simulating editorial approval...');
    
    // Create editorial review (simulating the approval process)
    const { data: newReview, error: createError } = await supabase
      .from('editorial_reviews')
      .insert({
        tool_id: tool.id,
        reviewed_by: 'test-admin',
        review_status: 'approved',
        review_date: new Date().toISOString().split('T')[0],
        review_notes: 'Test approval via workflow script',
        quality_score: 8 // Score between 1-10 as per database constraint
      })
      .select()
      .single();

    if (createError) {
      console.error('❌ Failed to create editorial review:', createError);
      return;
    }

    console.log(`✅ Created editorial review: ${newReview.id}`);

    // 3. Update tool with editorial review reference and publish
    console.log('\n3️⃣ Publishing tool...');
    
    const { error: updateError } = await supabase
      .from('tools')
      .update({
        editorial_review_id: newReview.id,
        content_status: 'published',
        published_at: new Date().toISOString(),
        updated_at: new Date().toISOString()
      })
      .eq('id', tool.id);

    if (updateError) {
      console.error('❌ Failed to update tool:', updateError);
      return;
    }

    console.log(`✅ Tool published successfully!`);

    // 4. Verify the complete workflow
    console.log('\n4️⃣ Verifying workflow completion...');
    
    const { data: updatedTool, error: verifyError } = await supabase
      .from('tools')
      .select(`
        id,
        name,
        content_status,
        published_at,
        editorial_review_id,
        editorial_reviews (
          id,
          review_status,
          quality_score,
          reviewed_by
        )
      `)
      .eq('id', tool.id)
      .single();

    if (verifyError) {
      console.error('❌ Failed to verify workflow:', verifyError);
      return;
    }

    console.log('📊 Workflow Verification Results:');
    console.log(`   Tool: ${updatedTool.name}`);
    console.log(`   Status: ${updatedTool.content_status}`);
    console.log(`   Published: ${updatedTool.published_at}`);
    console.log(`   Editorial Review: ${updatedTool.editorial_review_id}`);
    
    const review = updatedTool.editorial_reviews as any;
    if (review) {
      console.log(`   Review Status: ${review.review_status}`);
      console.log(`   Quality Score: ${review.quality_score}/10`);
      console.log(`   Reviewed By: ${review.reviewed_by}`);
    }

    // 5. Show complete workflow summary
    console.log('\n5️⃣ Complete Workflow Summary:');
    console.log('✅ AI Generation Job → Editorial Review → Tool Published');
    console.log(`✅ Tool "${updatedTool.name}" is now live with status: ${updatedTool.content_status}`);

  } catch (error) {
    console.error('❌ Test error:', error);
    throw error;
  }
}

// Run the test
testEditorialWorkflow()
  .then(() => {
    console.log('\n🎉 Editorial workflow test complete!');
    process.exit(0);
  })
  .catch((error) => {
    console.error('❌ Test failed:', error);
    process.exit(1);
  });
