#!/usr/bin/env tsx

import * as dotenv from 'dotenv';
import { createClient } from '@supabase/supabase-js';

// Load environment variables
dotenv.config({ path: '.env.local' });

// Create Supabase client with service role
const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL!;
const supabaseKey = process.env.SUPABASE_SERVICE_ROLE_KEY!;
const supabase = createClient(supabaseUrl, supabaseKey);

async function testAdminInterface() {
  console.log('🧪 Testing Admin Interface Functionality...\n');

  try {
    // 1. Create test submissions (both types)
    console.log('1️⃣ Creating test submissions:');
    
    const simpleSubmission = {
      name: 'Simple Test Tool',
      url: 'https://simple-test.com',
      description: 'A simple test tool for AI generation',
      category: 'writing-tools',
      submitter_name: 'Test User',
      submitter_email: '<EMAIL>',
      submission_type: 'simple',
      status: 'pending',
      submitted_at: new Date().toISOString()
    };

    const detailedSubmission = {
      name: 'Detailed Test Tool',
      url: 'https://detailed-test.com',
      description: 'A detailed test tool with all fields',
      detailed_description: 'This is a comprehensive AI tool that helps users with various tasks. It features advanced algorithms and user-friendly interface.',
      category: 'productivity-ai',
      submitter_name: 'Power User',
      submitter_email: '<EMAIL>',
      submission_type: 'detailed',
      features: '• Advanced AI algorithms\n• User-friendly interface\n• Real-time processing\n• Multi-language support',
      pricing_type: 'freemium',
      pricing_details: 'Free tier: 100 requests/month\nPro tier: $19/month for unlimited requests\nEnterprise: Custom pricing',
      pros: '• Fast processing\n• Accurate results\n• Great customer support\n• Regular updates',
      cons: '• Limited free tier\n• Learning curve for advanced features\n• Requires internet connection',
      meta_title: 'Detailed Test Tool - AI-Powered Productivity Assistant',
      meta_description: 'Boost your productivity with our AI-powered tool. Features advanced algorithms, real-time processing, and multi-language support.',
      faq: 'Q: How does it work?\nA: Our AI processes your input and provides intelligent suggestions.\n\nQ: Is there a free trial?\nA: Yes, we offer a free tier with 100 requests per month.',
      status: 'pending',
      submitted_at: new Date().toISOString()
    };

    // Insert test submissions
    const { data: simpleData, error: simpleError } = await supabase
      .from('tool_submissions')
      .insert(simpleSubmission)
      .select()
      .single();

    if (simpleError) {
      console.log(`   ❌ Simple submission failed: ${simpleError.message}`);
    } else {
      console.log(`   ✅ Simple submission created: ${simpleData.id}`);
    }

    const { data: detailedData, error: detailedError } = await supabase
      .from('tool_submissions')
      .insert(detailedSubmission)
      .select()
      .single();

    if (detailedError) {
      console.log(`   ❌ Detailed submission failed: ${detailedError.message}`);
    } else {
      console.log(`   ✅ Detailed submission created: ${detailedData.id}`);
    }

    // 2. Test admin API endpoints
    console.log('\n2️⃣ Testing admin API endpoints:');
    
    // Test submissions API
    try {
      const submissionsResponse = await fetch(`${process.env.NEXT_PUBLIC_APP_URL || 'http://localhost:3000'}/api/admin/editorial/submissions`, {
        headers: {
          'x-admin-api-key': process.env.NEXT_PUBLIC_ADMIN_API_KEY || 'aidude_admin_2024_secure_key_xyz789'
        }
      });

      if (submissionsResponse.ok) {
        const submissionsData = await submissionsResponse.json();
        console.log(`   ✅ Submissions API working: ${submissionsData.data?.length || 0} submissions found`);
      } else {
        console.log(`   ❌ Submissions API failed: ${submissionsResponse.status}`);
      }
    } catch (err) {
      console.log(`   ⚠️  Submissions API test skipped (server not running): ${err}`);
    }

    // 3. Test submission approval workflow
    console.log('\n3️⃣ Testing submission approval workflow:');
    
    if (simpleData) {
      // Test simple submission approval
      const { data: approvedSimple, error: approveError } = await supabase
        .from('tool_submissions')
        .update({
          status: 'approved',
          reviewed_at: new Date().toISOString(),
          reviewed_by: 'test-admin',
          review_notes: 'Approved for AI content generation'
        })
        .eq('id', simpleData.id)
        .select()
        .single();

      if (approveError) {
        console.log(`   ❌ Simple approval failed: ${approveError.message}`);
      } else {
        console.log(`   ✅ Simple submission approved: ${approvedSimple.status}`);
      }
    }

    if (detailedData) {
      // Test detailed submission approval
      const { data: approvedDetailed, error: approveDetailedError } = await supabase
        .from('tool_submissions')
        .update({
          status: 'approved',
          reviewed_at: new Date().toISOString(),
          reviewed_by: 'test-admin',
          review_notes: 'Approved - detailed submission ready for publishing'
        })
        .eq('id', detailedData.id)
        .select()
        .single();

      if (approveDetailedError) {
        console.log(`   ❌ Detailed approval failed: ${approveDetailedError.message}`);
      } else {
        console.log(`   ✅ Detailed submission approved: ${approvedDetailed.status}`);
      }
    }

    // 4. Test data transformation to tools table
    console.log('\n4️⃣ Testing data transformation to tools table:');
    
    if (detailedData) {
      // Transform detailed submission to tool
      const toolData = {
        name: detailedData.name,
        slug: detailedData.name.toLowerCase().replace(/[^a-z0-9]+/g, '-'),
        link: `/tools/${detailedData.name.toLowerCase().replace(/[^a-z0-9]+/g, '-')}`,
        description: detailedData.description,
        short_description: detailedData.description,
        website: detailedData.url,
        category_id: detailedData.category,
        company: detailedData.submitter_name,
        features: detailedData.features,
        pricing_type: detailedData.pricing_type,
        pricing_details: detailedData.pricing_details,
        pros: detailedData.pros,
        cons: detailedData.cons,
        meta_title: detailedData.meta_title,
        meta_description: detailedData.meta_description,
        faqs: detailedData.faq ? [{ question: 'General FAQ', answer: detailedData.faq }] : null,
        content_status: 'published',
        submission_type: 'detailed',
        is_verified: true,
        is_claimed: false,
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString(),
        published_at: new Date().toISOString()
      };

      const { data: toolCreated, error: toolError } = await supabase
        .from('tools')
        .insert(toolData)
        .select()
        .single();

      if (toolError) {
        console.log(`   ❌ Tool creation failed: ${toolError.message}`);
      } else {
        console.log(`   ✅ Tool created from detailed submission: ${toolCreated.id}`);
      }
    }

    // 5. Verify workflow differentiation
    console.log('\n5️⃣ Verifying workflow differentiation:');
    
    const { data: allSubmissions, error: fetchError } = await supabase
      .from('tool_submissions')
      .select('id, name, submission_type, status')
      .in('id', [simpleData?.id, detailedData?.id].filter(Boolean));

    if (fetchError) {
      console.log(`   ❌ Failed to fetch submissions: ${fetchError.message}`);
    } else {
      allSubmissions?.forEach(sub => {
        const workflow = sub.submission_type === 'simple' 
          ? 'Admin Approve → AI Generate → Editorial Review → Publish'
          : 'Admin Approve → Editorial Review → Publish';
        console.log(`   📋 ${sub.name} (${sub.submission_type}): ${workflow}`);
      });
    }

    // 6. Cleanup test data
    console.log('\n6️⃣ Cleaning up test data:');
    
    const testIds = [simpleData?.id, detailedData?.id].filter(Boolean);
    if (testIds.length > 0) {
      const { error: cleanupError } = await supabase
        .from('tool_submissions')
        .delete()
        .in('id', testIds);

      if (cleanupError) {
        console.log(`   ❌ Cleanup failed: ${cleanupError.message}`);
      } else {
        console.log(`   🧹 Test submissions cleaned up`);
      }
    }

    // Cleanup test tool
    const { error: toolCleanupError } = await supabase
      .from('tools')
      .delete()
      .eq('name', 'Detailed Test Tool');

    if (!toolCleanupError) {
      console.log(`   🧹 Test tool cleaned up`);
    }

  } catch (error) {
    console.error('❌ Test failed:', error);
  }
}

testAdminInterface()
  .then(() => {
    console.log('\n🎉 Admin interface test complete!');
    process.exit(0);
  })
  .catch((error) => {
    console.error('❌ Test failed:', error);
    process.exit(1);
  });
