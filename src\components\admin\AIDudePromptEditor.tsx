'use client';

import React, { useState, useEffect } from 'react';
import { Save, RefreshCw, Eye, Code, AlertCircle, CheckCircle, Zap } from 'lucide-react';

interface PromptTemplate {
  name: string;
  description: string;
  category: string;
  promptType: 'system' | 'user';
  template: string;
  variables: string[];
  validationRules: string[];
  formatRequirements: string;
  usage: number;
}

interface AIDudePromptEditorProps {
  templateKey: string;
  template: PromptTemplate;
  onSave: (templateKey: string, template: PromptTemplate) => Promise<void>;
  onTest?: (templateKey: string) => Promise<any>;
  isLoading?: boolean;
  isTesting?: boolean;
}

export function AIDudePromptEditor({
  templateKey,
  template: initialTemplate,
  onSave,
  onTest,
  isLoading = false,
  isTesting = false
}: AIDudePromptEditorProps) {
  const [template, setTemplate] = useState<PromptTemplate>(initialTemplate);
  const [hasChanges, setHasChanges] = useState(false);
  const [showPreview, setShowPreview] = useState(false);
  const [validationErrors, setValidationErrors] = useState<string[]>([]);

  useEffect(() => {
    setTemplate(initialTemplate);
    setHasChanges(false);
  }, [initialTemplate]);

  const handleTemplateChange = (field: keyof PromptTemplate, value: any) => {
    setTemplate(prev => ({
      ...prev,
      [field]: value
    }));
    setHasChanges(true);
    
    // Clear validation errors when user makes changes
    if (validationErrors.length > 0) {
      setValidationErrors([]);
    }
  };

  const validateTemplate = (): boolean => {
    const errors: string[] = [];

    if (!template.name.trim()) {
      errors.push('Template name is required');
    }

    if (!template.description.trim()) {
      errors.push('Template description is required');
    }

    if (!template.template.trim()) {
      errors.push('Template content is required');
    }

    if (template.template.includes('{') && template.variables.length === 0) {
      errors.push('Template contains variables but none are defined');
    }

    // Check if all variables in template are defined
    const templateVars = template.template.match(/\{([^}]+)\}/g) || [];
    const definedVars = template.variables.map(v => `{${v}}`);
    const missingVars = templateVars.filter(v => !definedVars.includes(v));
    
    if (missingVars.length > 0) {
      errors.push(`Undefined variables: ${missingVars.join(', ')}`);
    }

    setValidationErrors(errors);
    return errors.length === 0;
  };

  const handleSave = async () => {
    if (validateTemplate()) {
      await onSave(templateKey, template);
      setHasChanges(false);
    }
  };

  const handleTest = async () => {
    if (onTest && validateTemplate()) {
      await onTest(templateKey);
    }
  };

  const addVariable = () => {
    const newVar = prompt('Enter variable name (without braces):');
    if (newVar && !template.variables.includes(newVar)) {
      handleTemplateChange('variables', [...template.variables, newVar]);
    }
  };

  const removeVariable = (index: number) => {
    const newVariables = template.variables.filter((_, i) => i !== index);
    handleTemplateChange('variables', newVariables);
  };

  const addValidationRule = () => {
    const newRule = prompt('Enter validation rule:');
    if (newRule && !template.validationRules.includes(newRule)) {
      handleTemplateChange('validationRules', [...template.validationRules, newRule]);
    }
  };

  const removeValidationRule = (index: number) => {
    const newRules = template.validationRules.filter((_, i) => i !== index);
    handleTemplateChange('validationRules', newRules);
  };

  return (
    <div className="bg-zinc-800 border border-zinc-700 rounded-lg p-6">
      <div className="flex items-center justify-between mb-6">
        <div className="flex items-center gap-2">
          <Zap className="w-5 h-5 text-orange-500" />
          <h3 className="text-lg font-semibold text-white">AI Dude Prompt Template Editor</h3>
          <span className="px-2 py-1 bg-orange-500/20 text-orange-400 text-xs rounded font-medium">
            {template.promptType}
          </span>
        </div>
        
        <div className="flex items-center gap-2">
          <button
            onClick={() => setShowPreview(!showPreview)}
            className="flex items-center gap-2 px-3 py-2 bg-zinc-700 text-white rounded-lg hover:bg-zinc-600 transition-colors"
          >
            {showPreview ? <Code className="w-4 h-4" /> : <Eye className="w-4 h-4" />}
            {showPreview ? 'Edit' : 'Preview'}
          </button>
          
          {onTest && (
            <button
              onClick={handleTest}
              disabled={isTesting || !validateTemplate()}
              className="flex items-center gap-2 px-3 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
            >
              {isTesting ? <RefreshCw className="w-4 h-4 animate-spin" /> : <Eye className="w-4 h-4" />}
              Test
            </button>
          )}
          
          <button
            onClick={handleSave}
            disabled={!hasChanges || isLoading || validationErrors.length > 0}
            className="flex items-center gap-2 px-4 py-2 bg-orange-500 text-white rounded-lg hover:bg-orange-600 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
          >
            {isLoading ? <RefreshCw className="w-4 h-4 animate-spin" /> : <Save className="w-4 h-4" />}
            Save
          </button>
        </div>
      </div>

      {/* Validation Errors */}
      {validationErrors.length > 0 && (
        <div className="mb-6 p-4 bg-red-500/10 border border-red-500/20 rounded-lg">
          <div className="flex items-start gap-2">
            <AlertCircle className="w-4 h-4 text-red-400 mt-0.5 flex-shrink-0" />
            <div>
              <h4 className="text-red-400 font-medium mb-2">Validation Errors:</h4>
              <ul className="list-disc list-inside space-y-1 text-red-300 text-sm">
                {validationErrors.map((error, index) => (
                  <li key={index}>{error}</li>
                ))}
              </ul>
            </div>
          </div>
        </div>
      )}

      {showPreview ? (
        /* Preview Mode */
        <div className="space-y-6">
          <div className="bg-zinc-900 border border-zinc-700 rounded-lg p-4">
            <h4 className="text-white font-medium mb-2">Template Preview</h4>
            <pre className="text-sm text-gray-300 whitespace-pre-wrap font-mono bg-black/30 p-4 rounded border overflow-auto max-h-96">
              {template.template}
            </pre>
          </div>
          
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="bg-zinc-900 border border-zinc-700 rounded-lg p-4">
              <h4 className="text-white font-medium mb-2">Variables ({template.variables.length})</h4>
              {template.variables.length > 0 ? (
                <ul className="space-y-1">
                  {template.variables.map((variable, index) => (
                    <li key={index} className="text-sm text-gray-300 font-mono">
                      {`{${variable}}`}
                    </li>
                  ))}
                </ul>
              ) : (
                <p className="text-gray-400 text-sm">No variables defined</p>
              )}
            </div>
            
            <div className="bg-zinc-900 border border-zinc-700 rounded-lg p-4">
              <h4 className="text-white font-medium mb-2">Validation Rules ({template.validationRules.length})</h4>
              {template.validationRules.length > 0 ? (
                <ul className="space-y-1">
                  {template.validationRules.map((rule, index) => (
                    <li key={index} className="text-sm text-gray-300">
                      • {rule}
                    </li>
                  ))}
                </ul>
              ) : (
                <p className="text-gray-400 text-sm">No validation rules defined</p>
              )}
            </div>
          </div>
        </div>
      ) : (
        /* Edit Mode */
        <div className="space-y-6">
          {/* Basic Information */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <label className="block text-sm font-medium text-white mb-2">
                Template Name
              </label>
              <input
                type="text"
                value={template.name}
                onChange={(e) => handleTemplateChange('name', e.target.value)}
                className="w-full bg-zinc-700 border border-zinc-600 rounded-lg px-3 py-2 text-white focus:outline-none focus:border-orange-500"
                placeholder="Enter template name"
              />
            </div>
            
            <div>
              <label className="block text-sm font-medium text-white mb-2">
                Category
              </label>
              <input
                type="text"
                value={template.category}
                onChange={(e) => handleTemplateChange('category', e.target.value)}
                className="w-full bg-zinc-700 border border-zinc-600 rounded-lg px-3 py-2 text-white focus:outline-none focus:border-orange-500"
                placeholder="Enter category"
              />
            </div>
          </div>

          <div>
            <label className="block text-sm font-medium text-white mb-2">
              Description
            </label>
            <textarea
              value={template.description}
              onChange={(e) => handleTemplateChange('description', e.target.value)}
              rows={2}
              className="w-full bg-zinc-700 border border-zinc-600 rounded-lg px-3 py-2 text-white focus:outline-none focus:border-orange-500"
              placeholder="Enter template description"
            />
          </div>

          {/* Template Content */}
          <div>
            <label className="block text-sm font-medium text-white mb-2">
              Template Content
            </label>
            <textarea
              value={template.template}
              onChange={(e) => handleTemplateChange('template', e.target.value)}
              rows={12}
              className="w-full bg-zinc-700 border border-zinc-600 rounded-lg px-3 py-2 text-white focus:outline-none focus:border-orange-500 font-mono text-sm"
              placeholder="Enter template content with {variables}"
            />
          </div>

          {/* Variables and Validation Rules */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            {/* Variables */}
            <div>
              <div className="flex items-center justify-between mb-3">
                <label className="text-sm font-medium text-white">
                  Variables ({template.variables.length})
                </label>
                <button
                  onClick={addVariable}
                  className="text-xs bg-orange-500 text-white px-2 py-1 rounded hover:bg-orange-600"
                >
                  Add Variable
                </button>
              </div>
              <div className="space-y-2 max-h-32 overflow-y-auto">
                {template.variables.map((variable, index) => (
                  <div key={index} className="flex items-center gap-2 bg-zinc-700 rounded px-3 py-2">
                    <span className="text-sm text-gray-300 font-mono flex-1">{`{${variable}}`}</span>
                    <button
                      onClick={() => removeVariable(index)}
                      className="text-red-400 hover:text-red-300 text-xs"
                    >
                      Remove
                    </button>
                  </div>
                ))}
              </div>
            </div>

            {/* Validation Rules */}
            <div>
              <div className="flex items-center justify-between mb-3">
                <label className="text-sm font-medium text-white">
                  Validation Rules ({template.validationRules.length})
                </label>
                <button
                  onClick={addValidationRule}
                  className="text-xs bg-orange-500 text-white px-2 py-1 rounded hover:bg-orange-600"
                >
                  Add Rule
                </button>
              </div>
              <div className="space-y-2 max-h-32 overflow-y-auto">
                {template.validationRules.map((rule, index) => (
                  <div key={index} className="flex items-center gap-2 bg-zinc-700 rounded px-3 py-2">
                    <span className="text-sm text-gray-300 flex-1">{rule}</span>
                    <button
                      onClick={() => removeValidationRule(index)}
                      className="text-red-400 hover:text-red-300 text-xs"
                    >
                      Remove
                    </button>
                  </div>
                ))}
              </div>
            </div>
          </div>

          {/* Format Requirements */}
          <div>
            <label className="block text-sm font-medium text-white mb-2">
              Format Requirements
            </label>
            <input
              type="text"
              value={template.formatRequirements}
              onChange={(e) => handleTemplateChange('formatRequirements', e.target.value)}
              className="w-full bg-zinc-700 border border-zinc-600 rounded-lg px-3 py-2 text-white focus:outline-none focus:border-orange-500"
              placeholder="Enter format requirements"
            />
          </div>
        </div>
      )}

      {/* Status */}
      {hasChanges && (
        <div className="mt-4 p-3 bg-yellow-500/10 border border-yellow-500/20 rounded-lg">
          <div className="flex items-center gap-2 text-yellow-400 text-sm">
            <AlertCircle className="w-4 h-4" />
            You have unsaved changes
          </div>
        </div>
      )}
    </div>
  );
}

export default AIDudePromptEditor;
