const http = require('http');

function testAPI(path, method = 'GET', data = null) {
  return new Promise((resolve, reject) => {
    const options = {
      hostname: 'localhost',
      port: 3000,
      path: path,
      method: method,
      headers: {
        'Content-Type': 'application/json',
        'x-api-key': 'aidude_admin_2024_secure_key_xyz789'
      }
    };

    const req = http.request(options, (res) => {
      let responseData = '';
      res.on('data', (chunk) => responseData += chunk);
      res.on('end', () => {
        try {
          const parsed = JSON.parse(responseData);
          resolve({ status: res.statusCode, data: parsed });
        } catch (e) {
          resolve({ status: res.statusCode, data: responseData, error: e.message });
        }
      });
    });

    req.on('error', (error) => {
      reject(error);
    });

    if (data) {
      req.write(JSON.stringify(data));
    }
    req.end();
  });
}

async function testAIDudeDetailed() {
  console.log('🔍 Detailed AI Dude System Analysis\n');
  console.log('='.repeat(60));

  try {
    // Test with more engaging content to trigger AI Dude tone
    console.log('\n1. 🎭 Testing AI Dude Tone Generation');
    const testData = {
      url: 'https://example-tool.com',
      methodology: 'ai_dude',
      scrapedData: {
        title: 'AwesomeAI Pro',
        description: 'Revolutionary AI tool that changes everything',
        content: `AwesomeAI Pro is a cutting-edge artificial intelligence platform that revolutionizes how businesses approach automation. 

Key Features:
- Advanced machine learning algorithms
- Real-time data processing
- Intuitive user interface
- Enterprise-grade security
- 24/7 customer support

Pricing:
- Free tier: Basic features
- Pro: $29/month - Advanced features
- Enterprise: $99/month - Full suite

The platform has been praised by industry experts and has over 100,000 active users worldwide. It integrates seamlessly with popular tools like Slack, Microsoft Teams, and Google Workspace.

Customer testimonials highlight the tool's ease of use and powerful capabilities. The company was founded in 2020 and has raised $50M in funding.`
      },
      options: {
        priority: 'cost',
        complexity: 'medium',
        contentQuality: 85
      }
    };

    console.log('   🔄 Generating with rich content...');
    const generateResult = await testAPI('/api/generate-content', 'POST', testData);
    
    if (generateResult.status === 200 && generateResult.data.success) {
      const content = generateResult.data.data.aiContent;
      const metadata = generateResult.data.data.generationMetadata;
      
      console.log(`   ✅ Generation successful!`);
      console.log(`   📊 Provider: ${metadata?.provider}`);
      console.log(`   🎯 Methodology: ${metadata?.methodology}`);
      console.log(`   🔧 Model: ${metadata?.model}`);
      console.log(`   📝 Fields: ${Object.keys(content || {}).length}`);
      
      // Detailed content analysis
      console.log('\n   📋 Generated Content Analysis:');
      
      if (content.name) {
        console.log(`      Name: "${content.name}"`);
      }
      
      if (content.description) {
        console.log(`      Description: "${content.description}"`);
        console.log(`      Description length: ${content.description.length} chars`);
      }
      
      if (content.detailed_description) {
        console.log(`      Detailed desc length: ${content.detailed_description.length} chars`);
        console.log(`      Detailed desc sample: "${content.detailed_description.substring(0, 100)}..."`);
      }
      
      if (content.features && content.features.length > 0) {
        console.log(`      Features count: ${content.features.length}`);
        console.log(`      First feature: "${content.features[0]}"`);
      }
      
      if (content.pros_and_cons) {
        console.log(`      Pros: ${content.pros_and_cons.pros?.length || 0}`);
        console.log(`      Cons: ${content.pros_and_cons.cons?.length || 0}`);
        if (content.pros_and_cons.pros?.[0]) {
          console.log(`      First pro: "${content.pros_and_cons.pros[0]}"`);
        }
      }
      
      if (content.meta_title) {
        console.log(`      Meta title: "${content.meta_title}" (${content.meta_title.length} chars)`);
      }
      
      if (content.meta_description) {
        console.log(`      Meta desc: "${content.meta_description}" (${content.meta_description.length} chars)`);
      }
      
      // Advanced tone analysis
      console.log('\n   🎭 Tone Analysis:');
      const toneAnalysis = analyzeAIDudeTone(content);
      console.log(`      AI Dude indicators found: ${toneAnalysis.indicators.length}`);
      if (toneAnalysis.indicators.length > 0) {
        console.log(`      Indicators: ${toneAnalysis.indicators.join(', ')}`);
      }
      console.log(`      Tone score: ${toneAnalysis.score}/100`);
      console.log(`      Overall assessment: ${toneAnalysis.assessment}`);
      
      // Check if content is too generic
      const genericityCheck = checkGenericity(content);
      console.log(`      Genericity score: ${genericityCheck.score}/100 (lower is better)`);
      if (genericityCheck.issues.length > 0) {
        console.log(`      Generic issues: ${genericityCheck.issues.join(', ')}`);
      }
      
    } else {
      console.log(`   ❌ Generation failed: ${generateResult.status}`);
      if (generateResult.data?.error) {
        console.log(`      Error: ${generateResult.data.error}`);
      }
    }

    // Test 2: Compare with standard methodology
    console.log('\n2. 🔄 Comparison with Standard Methodology');
    const standardData = { ...testData, methodology: 'standard' };
    
    const standardResult = await testAPI('/api/generate-content', 'POST', standardData);
    if (standardResult.status === 200 && standardResult.data.success) {
      const standardContent = standardResult.data.data.aiContent;
      console.log(`   ✅ Standard generation successful`);
      
      if (standardContent.description && content.description) {
        console.log(`   📊 Description comparison:`);
        console.log(`      AI Dude: "${content.description}"`);
        console.log(`      Standard: "${standardContent.description}"`);
        
        const aiDudeTone = analyzeAIDudeTone(content);
        const standardTone = analyzeAIDudeTone(standardContent);
        console.log(`      AI Dude tone score: ${aiDudeTone.score}`);
        console.log(`      Standard tone score: ${standardTone.score}`);
      }
    }

  } catch (error) {
    console.error('❌ Test failed:', error.message);
  }
}

function analyzeAIDudeTone(content) {
  const toneIndicators = [
    // Contractions and casual language
    "ain't", "gonna", "wanna", "gotta", "kinda", "sorta", "you'll", "we'll", "it's", "that's", "here's", "what's",
    // AI Dude specific terms
    "dude", "no-bs", "no bs", "snarky", "witty", "bs", "crap", "damn", "hell", "seriously", "honestly", "frankly", "basically",
    // Casual expressions
    "pretty much", "tbh", "ngl", "lol", "btw", "fyi", "imo", "imho",
    // Exclamations and emphasis
    "!", "?!", "...", "wow", "boom", "bam", "epic", "awesome", "killer", "sick", "dope",
    // Irreverent language
    "cut through", "no fluff", "straight up", "real talk", "bottom line", "let's be real", "truth bomb"
  ];
  
  const textFields = [
    content.description,
    content.detailed_description,
    content.meta_description,
    ...(content.features || []),
    ...(content.pros_and_cons?.pros || []),
    ...(content.pros_and_cons?.cons || [])
  ].filter(Boolean);
  
  const allText = textFields.join(' ').toLowerCase();
  const foundIndicators = toneIndicators.filter(indicator => 
    allText.includes(indicator.toLowerCase())
  );
  
  const score = Math.min(100, foundIndicators.length * 10);
  let assessment = 'Corporate/Generic';
  
  if (score >= 30) assessment = 'Strong AI Dude Tone';
  else if (score >= 20) assessment = 'Moderate AI Dude Tone';
  else if (score >= 10) assessment = 'Weak AI Dude Tone';
  
  return {
    detected: foundIndicators.length > 0,
    indicators: foundIndicators,
    score,
    assessment
  };
}

function checkGenericity(content) {
  const genericPhrases = [
    'cutting-edge', 'revolutionary', 'innovative', 'state-of-the-art', 'industry-leading',
    'comprehensive', 'robust', 'scalable', 'efficient', 'powerful', 'advanced',
    'seamless', 'intuitive', 'user-friendly', 'easy to use', 'simple'
  ];
  
  const textFields = [
    content.description,
    content.detailed_description,
    ...(content.features || [])
  ].filter(Boolean);
  
  const allText = textFields.join(' ').toLowerCase();
  const foundGeneric = genericPhrases.filter(phrase => 
    allText.includes(phrase.toLowerCase())
  );
  
  const score = Math.min(100, foundGeneric.length * 8);
  const issues = [];
  
  if (foundGeneric.length > 5) issues.push('Too many generic phrases');
  if (score > 40) issues.push('Content sounds corporate');
  
  return { score, issues, genericPhrases: foundGeneric };
}

testAIDudeDetailed();
