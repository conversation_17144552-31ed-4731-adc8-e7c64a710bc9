/**
 * Centralized MSW (Mock Service Worker) setup for all test suites
 * Provides consistent mocks for Scrape.do API and Supabase REST API
 */

// Ensure polyfills are available before importing MSW
if (typeof global.TextEncoder === 'undefined') {
  const { TextEncoder, TextDecoder } = require('util');
  global.TextEncoder = TextEncoder;
  global.TextDecoder = TextDecoder;
}

// Ensure fetch is available for MSW
if (typeof global.fetch === 'undefined') {
  const { fetch, Headers, Request, Response } = require('undici');
  global.fetch = fetch;
  global.Headers = Headers;
  global.Request = Request;
  global.Response = Response;
}

import { http, HttpResponse } from 'msw';
import { setupServer } from 'msw/node';

// Mock data for consistent testing
export const mockScrapedContent = {
  title: 'Test Page Title',
  content: 'This is test content with sufficient length for AI processing. It contains multiple paragraphs and structured information that would be useful for content analysis.',
  description: 'Test page description',
  keywords: ['test', 'mock', 'content'],
  author: 'Test Author',
  publishedDate: '2024-01-01',
  language: 'en',
  canonicalUrl: 'https://example.com',
  images: ['https://example.com/image.jpg'],
  links: ['https://example.com/link1', 'https://example.com/link2']
};

export const mockSupabaseData = {
  id: 1,
  url: 'https://example.com',
  title: 'Test Tool',
  description: 'Test tool description',
  scraped_at: '2024-01-01T00:00:00Z',
  content: mockScrapedContent.content,
  metadata: mockScrapedContent
};

// Scrape.do API handlers
const scrapeDoHandlers = [
  // Standard scraping endpoint
  http.get('https://api.scrape.do', ({ request }) => {
    const url = new URL(request.url);
    const targetUrl = url.searchParams.get('url');
    const render = url.searchParams.get('render');
    const screenshot = url.searchParams.get('screenshot');

    // Simulate different responses based on URL patterns
    if (targetUrl?.includes('error-test')) {
      return HttpResponse.json(
        { error: 'Scraping failed', message: 'Target site blocked request' },
        { status: 400 }
      );
    }

    if (targetUrl?.includes('timeout-test')) {
      return HttpResponse.json(
        { error: 'Request timeout', message: 'Target site took too long to respond' },
        { status: 408 }
      );
    }

    // Check for screenshot parameters
    const fullScreenshot = url.searchParams.get('fullScreenShot');
    const returnJSON = url.searchParams.get('returnJSON');

    // Standard successful response
    const response = {
      url: targetUrl,
      status: 200,
      content: `# ${mockScrapedContent.title}\n\n${mockScrapedContent.content}`,
      title: mockScrapedContent.title,
      description: mockScrapedContent.description,
      keywords: mockScrapedContent.keywords,
      author: mockScrapedContent.author,
      published_date: mockScrapedContent.publishedDate,
      language: mockScrapedContent.language,
      canonical_url: mockScrapedContent.canonicalUrl,
      images: mockScrapedContent.images,
      links: mockScrapedContent.links,
      screenshot: screenshot === 'true' ? 'data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNkYPhfDwAChwGA60e6kgAAAABJRU5ErkJggg==' : null,
      fullScreenshot: fullScreenshot === 'true' ? 'data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNkYPhfDwAChwGA60e6kgAAAABJRU5ErkJggg==' : null,
      favicon: 'data:image/x-icon;base64,AAABAAEAEBAAAAEAIABoBAAAFgAAACgAAAAQAAAAIAAAAAEAIAAAAAAAAAQAABILAAASCwAAAAAAAAAAAAA=',
      // Add screenShots array when screenshot is requested
      ...(screenshot === 'true' && {
        screenShots: [
          {
            type: 'ScreenShot',
            image: 'data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNkYPhfDwAChwGA60e6kgAAAABJRU5ErkJggg=='
          }
        ]
      })
    };

    return HttpResponse.json(response);
  }),

  // Usage statistics endpoint
  http.get('https://api.scrape.do/info', ({ request }) => {
    const url = new URL(request.url);
    const token = url.searchParams.get('token');

    if (!token || token.trim() === '') {
      return HttpResponse.json(
        { error: 'Missing or invalid token' },
        { status: 401 }
      );
    }

    return HttpResponse.json({
      IsActive: true,
      ConcurrentRequest: 10,
      MaxMonthlyRequest: 1500,
      RemainingConcurrentRequest: 8,
      RemainingMonthlyRequest: 1000
    });
  })
];

// Supabase REST API handlers
const supabaseHandlers = [
  // Generic table operations
  http.post('https://*.supabase.co/rest/v1/:table', ({ params, request }) => {
    return HttpResponse.json(
      { ...mockSupabaseData, id: Math.floor(Math.random() * 1000) },
      { 
        status: 201,
        headers: {
          'Content-Type': 'application/json',
          'Location': `/${params.table}?id=eq.${Math.floor(Math.random() * 1000)}`
        }
      }
    );
  }),

  http.get('https://*.supabase.co/rest/v1/:table', ({ params, request }) => {
    const url = new URL(request.url);
    const select = url.searchParams.get('select');
    const limit = url.searchParams.get('limit');
    
    // Handle single record requests
    if (url.searchParams.has('id')) {
      return HttpResponse.json(mockSupabaseData);
    }
    
    // Handle list requests
    const data = Array.from({ length: parseInt(limit || '10') }, (_, i) => ({
      ...mockSupabaseData,
      id: i + 1
    }));
    
    return HttpResponse.json(data);
  }),

  http.patch('https://*.supabase.co/rest/v1/:table', ({ params }) => {
    return HttpResponse.json(
      { ...mockSupabaseData, updated_at: new Date().toISOString() },
      { status: 200 }
    );
  }),

  http.delete('https://*.supabase.co/rest/v1/:table', ({ params }) => {
    return HttpResponse.json(
      {},
      { status: 204 }
    );
  }),

  // RPC function calls
  http.post('https://*.supabase.co/rest/v1/rpc/:function', ({ params }) => {
    return HttpResponse.json({
      result: 'success',
      function: params.function,
      data: mockSupabaseData
    });
  })
];

// Create the MSW server with all handlers
export const server = setupServer(...scrapeDoHandlers, ...supabaseHandlers);

// Helper functions for test setup
export function setupMSWServer() {
  beforeAll(() => {
    server.listen({ onUnhandledRequest: 'warn' });
  });

  afterEach(() => {
    server.resetHandlers();
  });

  afterAll(() => {
    server.close();
  });
}

// Helper to override handlers for specific tests
export function mockScrapeDoResponse(url: string, response: any, status = 200) {
  server.use(
    http.get('https://api.scrape.do', ({ request }) => {
      const requestUrl = new URL(request.url);
      const targetUrl = requestUrl.searchParams.get('url');
      
      if (targetUrl === url) {
        return HttpResponse.json(response, { status });
      }
      
      // Fall back to default handler
      return HttpResponse.json(mockScrapedContent);
    })
  );
}

export function mockSupabaseResponse(table: string, operation: 'GET' | 'POST' | 'PATCH' | 'DELETE', response: any, status = 200) {
  const method = operation.toLowerCase() as 'get' | 'post' | 'patch' | 'delete';
  
  server.use(
    http[method](`https://*.supabase.co/rest/v1/${table}`, () => {
      return HttpResponse.json(response, { status });
    })
  );
}

// Mock data is already exported above, no need to re-export
