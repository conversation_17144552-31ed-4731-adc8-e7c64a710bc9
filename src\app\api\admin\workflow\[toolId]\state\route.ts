import { NextRequest, NextResponse } from 'next/server';
import { validate<PERSON><PERSON><PERSON><PERSON> } from '@/lib/auth';
import { workflowManager } from '@/lib/workflow/workflow-manager';

/**
 * GET /api/admin/workflow/[toolId]/state
 * Get current workflow state for a tool
 */
export async function GET(
  request: NextRequest,
  { params }: { params: { toolId: string } }
) {
  try {
    // Validate admin API key
    const isValid = await validateApiKey(request);
    if (!isValid) {
      return NextResponse.json(
        { success: false, error: 'Unauthorized' },
        { status: 401 }
      );
    }

    const { toolId } = params;

    if (!toolId) {
      return NextResponse.json(
        { success: false, error: 'Tool ID is required' },
        { status: 400 }
      );
    }

    // Get workflow state
    const workflowState = await workflowManager.getWorkflowState(toolId);

    if (!workflowState) {
      return NextResponse.json(
        { success: false, error: 'Workflow state not found' },
        { status: 404 }
      );
    }

    return NextResponse.json({
      success: true,
      data: workflowState
    });

  } catch (error: any) {
    console.error('Workflow state API error:', error);
    return NextResponse.json(
      { 
        success: false, 
        error: error.message || 'Failed to get workflow state' 
      },
      { status: 500 }
    );
  }
}

/**
 * POST /api/admin/workflow/[toolId]/state
 * Update workflow state for a tool
 */
export async function POST(
  request: NextRequest,
  { params }: { params: { toolId: string } }
) {
  try {
    // Validate admin API key
    const isValid = await validateApiKey(request);
    if (!isValid) {
      return NextResponse.json(
        { success: false, error: 'Unauthorized' },
        { status: 401 }
      );
    }

    const { toolId } = params;
    const body = await request.json();
    const { action, targetStage, triggeredBy, notes } = body;

    if (!toolId) {
      return NextResponse.json(
        { success: false, error: 'Tool ID is required' },
        { status: 400 }
      );
    }

    switch (action) {
      case 'transition':
        if (!targetStage) {
          return NextResponse.json(
            { success: false, error: 'Target stage is required for transition' },
            { status: 400 }
          );
        }

        const updatedState = await workflowManager.transitionTo(
          toolId,
          targetStage,
          triggeredBy || 'admin',
          notes
        );

        return NextResponse.json({
          success: true,
          data: updatedState,
          message: `Workflow transitioned to ${targetStage}`
        });

      case 'start_content_generation':
        const jobId = await workflowManager.startContentGeneration(toolId, {
          priority: body.priority || 'normal',
          triggeredBy: triggeredBy || 'admin'
        });

        return NextResponse.json({
          success: true,
          data: { jobId },
          message: 'Content generation started'
        });

      case 'start_editorial_review':
        const reviewId = await workflowManager.startEditorialReview(
          toolId,
          triggeredBy || 'admin'
        );

        return NextResponse.json({
          success: true,
          data: { reviewId },
          message: 'Editorial review started'
        });

      case 'initialize':
        const initialStage = body.initialStage || 'draft';
        const newState = await workflowManager.initializeWorkflow(toolId, initialStage);

        return NextResponse.json({
          success: true,
          data: newState,
          message: 'Workflow initialized'
        });

      default:
        return NextResponse.json(
          { success: false, error: 'Invalid action' },
          { status: 400 }
        );
    }

  } catch (error: any) {
    console.error('Workflow state update API error:', error);
    return NextResponse.json(
      { 
        success: false, 
        error: error.message || 'Failed to update workflow state' 
      },
      { status: 500 }
    );
  }
}
