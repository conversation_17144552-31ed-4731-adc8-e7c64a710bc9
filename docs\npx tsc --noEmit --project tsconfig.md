npx tsc --noEmit --project tsconfig.json

refer necessary reference documents and complete the media features task

refer necessary reference documents, code, and plan and complete and test  Task 1.4: Configuration Management System


Check for errors in code and fix / optimise and git commit

Refer necessary documents, code and Plan meticulously and code Task 3.3: Editorial Workflow 
Check for errors and fix 
update necessary documentation
commit git and push

no no correct logic should be like when og images , og twitter,og fb are available  , no screeshot should be captured 

always extract favicon, if favicon is not available use a placeholder image

please double check the code 

Correct Logic (as you specified):

Always extract favicon (use placeholder if not available)
Always extract OG images
Only capture screenshot if NO OG images are available (regardless of favicon status)



oal is to concentrate on 100% pass scraping tests before start working on the new # AI Dude Prompt System - Implementation Recommendations before we start generating content 

**Priority: Complete Jest Test Suite Resolution Before AI Prompt System Implementation**

Your primary goal is to achieve 100% test pass rate across all scraping-related test suites before beginning any work on the AI Dude Prompt System implementation outlined in `docs\ai-prompts\recommendations.md`. 

**Current Status**: 128 failed tests, 263 passed tests (391 total)

**Required Actions**:
1. **Fix all failing Jest tests** in the scraping system, focusing on:
   - Supabase mock setup issues (database integration tests)
   - Favicon extraction tests (null vs empty array handling)
   - Integration workflow tests (ContentProcessor mock configuration)
   - Error handling tests (mock expectation alignment)
   - URL validation tests (constructor behavior alignment)

2. **Verification Criteria**: All 391 tests must pass with 0 failures before proceeding

3. **Only after achieving 100% test pass rate**, then begin implementing the AI content generation system and prompt structure methodology described in the AI prompts documentation

**Context**: This ensures the foundational web scraping infrastructure is stable and reliable before adding AI content generation capabilities on top of it.