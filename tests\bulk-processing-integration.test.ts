/**
 * Bulk Processing Integration Tests
 * Comprehensive tests for the enhanced bulk processing system
 */

import { describe, test, expect, beforeAll, afterAll } from '@jest/globals';
import { createClient } from '@supabase/supabase-js';

const supabase = createClient(
  process.env.NEXT_PUBLIC_SUPABASE_URL!,
  process.env.SUPABASE_SERVICE_ROLE_KEY!
);

const ADMIN_API_KEY = process.env.ADMIN_API_KEY || 'aidude_admin_2024_secure_key_xyz789';
const BASE_URL = process.env.NEXT_PUBLIC_BASE_URL || 'http://localhost:3000';

describe('Bulk Processing Integration Tests', () => {
  let testToolIds: string[] = [];

  beforeAll(async () => {
    // Get tools with scraped data for testing
    const { data: tools } = await supabase
      .from('tools')
      .select('id')
      .not('scraped_data', 'is', null)
      .limit(3);
    
    testToolIds = tools?.map(t => t.id) || [];
    console.log('Test tool IDs:', testToolIds);
  });

  describe('Resume Tools API', () => {
    test('should fetch tools with scraped data', async () => {
      const response = await fetch(`${BASE_URL}/api/admin/bulk-processing/resume-tools`, {
        headers: {
          'x-admin-api-key': ADMIN_API_KEY,
        },
      });

      expect(response.status).toBe(200);
      
      const data = await response.json();
      expect(data.success).toBe(true);
      expect(data.data.tools).toBeInstanceOf(Array);
      expect(data.data.summary.totalToolsWithScrapedData).toBeGreaterThan(0);
      
      // Verify tool structure
      if (data.data.tools.length > 0) {
        const tool = data.data.tools[0];
        expect(tool).toHaveProperty('id');
        expect(tool).toHaveProperty('name');
        expect(tool).toHaveProperty('hasScrapedData', true);
        expect(tool).toHaveProperty('aiGenerationStatus');
        expect(tool).toHaveProperty('scrapedDataSize');
      }
    });

    test('should create resume generation job', async () => {
      if (testToolIds.length === 0) {
        console.log('Skipping test: no tools with scraped data available');
        return;
      }

      const response = await fetch(`${BASE_URL}/api/admin/bulk-processing/resume-tools`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'x-admin-api-key': ADMIN_API_KEY,
        },
        body: JSON.stringify({
          toolIds: [testToolIds[0]],
          options: {
            batchSize: 1,
            aiProvider: 'openai',
            priority: 'normal',
          },
        }),
      });

      expect(response.status).toBe(200);
      
      const data = await response.json();
      expect(data.success).toBe(true);
      expect(data.data.job).toHaveProperty('id');
      expect(data.data.selectedTools.totalSelected).toBe(1);
    });
  });

  describe('Media Collection + Scrape Mode', () => {
    test('should handle media collection mode', async () => {
      const testUrls = ['https://example.com', 'https://test.com'];

      const response = await fetch(`${BASE_URL}/api/admin/bulk-processing`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'x-admin-api-key': ADMIN_API_KEY,
        },
        body: JSON.stringify({
          type: 'processed_data',
          data: testUrls.map(url => ({ url })),
          options: {
            scrapeOnly: true,
            generateContent: false,
            batchSize: 2,
            priority: 'normal',
          },
        }),
      });

      expect(response.status).toBe(200);
      
      const data = await response.json();
      expect(data.success).toBe(true);
      expect(data.data.job).toHaveProperty('id');
    });
  });

  describe('Full Processing Mode', () => {
    test('should handle full processing mode', async () => {
      const testUrls = ['https://example.com'];

      const response = await fetch(`${BASE_URL}/api/admin/bulk-processing`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'x-admin-api-key': ADMIN_API_KEY,
        },
        body: JSON.stringify({
          type: 'processed_data',
          data: testUrls.map(url => ({ url })),
          options: {
            scrapeOnly: false,
            generateContent: true,
            batchSize: 1,
            aiProvider: 'openai',
            priority: 'normal',
          },
        }),
      });

      expect(response.status).toBe(200);
      
      const data = await response.json();
      expect(data.success).toBe(true);
      expect(data.data.job).toHaveProperty('id');
    });
  });

  describe('Processing Options Validation', () => {
    test('should validate processing options correctly', async () => {
      // Test invalid processing mode combination
      const response = await fetch(`${BASE_URL}/api/admin/bulk-processing`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'x-admin-api-key': ADMIN_API_KEY,
        },
        body: JSON.stringify({
          type: 'processed_data',
          data: [{ url: 'https://example.com' }],
          options: {
            scrapeOnly: true,
            generateContent: true, // Invalid combination
            batchSize: 1,
          },
        }),
      });

      // Should still work as the API handles this gracefully
      expect(response.status).toBe(200);
    });

    test('should require tool IDs for resume generation', async () => {
      const response = await fetch(`${BASE_URL}/api/admin/bulk-processing/resume-tools`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'x-admin-api-key': ADMIN_API_KEY,
        },
        body: JSON.stringify({
          toolIds: [], // Empty array should fail
          options: {},
        }),
      });

      expect(response.status).toBe(400);
      
      const data = await response.json();
      expect(data.success).toBe(false);
      expect(data.error).toContain('toolIds array is required');
    });
  });

  describe('Error Handling', () => {
    test('should handle unauthorized requests', async () => {
      const response = await fetch(`${BASE_URL}/api/admin/bulk-processing/resume-tools`, {
        headers: {
          'x-admin-api-key': 'invalid-key',
        },
      });

      expect(response.status).toBe(401);
      
      const data = await response.json();
      expect(data.success).toBe(false);
      expect(data.error).toBe('Unauthorized');
    });

    test('should handle missing data', async () => {
      const response = await fetch(`${BASE_URL}/api/admin/bulk-processing`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'x-admin-api-key': ADMIN_API_KEY,
        },
        body: JSON.stringify({
          type: 'processed_data',
          // Missing data field
          options: {},
        }),
      });

      expect(response.status).toBe(400);
    });
  });

  afterAll(async () => {
    // Cleanup any test data if needed
    console.log('Bulk processing integration tests completed');
  });
});
