/**
 * Global Jest setup file
 * Configures MSW server and other global test utilities
 */

// Only setup MSW in Node.js environment (not jsdom)
if (typeof window === 'undefined') {
  // We're in Node.js environment, safe to import MSW
  try {
    const { server } = require('@/__mocks__/msw-server');

    // Setup MSW server for Node.js tests
    beforeAll(() => {
      server.listen({ onUnhandledRequest: 'warn' });
    });

    afterEach(() => {
      server.resetHandlers();
    });

    afterAll(() => {
      server.close();
    });
  } catch (error: unknown) {
    // MSW not available, skip setup
    const errorMessage = error instanceof Error ? error.message : 'Unknown error';
    console.warn('MSW setup skipped:', errorMessage);
  }
}

// Mock console methods to reduce noise in tests
const originalConsoleError = console.error;
const originalConsoleWarn = console.warn;
const originalConsoleLog = console.log;

beforeEach(() => {
  // Suppress console output during tests unless explicitly needed
  console.error = jest.fn();
  console.warn = jest.fn();
  console.log = jest.fn();
});

afterEach(() => {
  // Restore console methods
  console.error = originalConsoleError;
  console.warn = originalConsoleWarn;
  console.log = originalConsoleLog;
});

// Global test utilities
(global as any).testUtils = {
  // Helper to wait for async operations
  waitFor: (ms: number) => new Promise(resolve => setTimeout(resolve, ms)),

  // Helper to create mock functions with better typing
  createMockFn: <T extends (...args: any[]) => any>(implementation?: T) => {
    return jest.fn(implementation) as any;
  }
};
