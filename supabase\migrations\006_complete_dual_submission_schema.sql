-- Complete dual submission system schema migration
-- Migration: 006_complete_dual_submission_schema.sql

-- Add missing columns to tool_submissions table
DO $$ BEGIN
  IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name='tool_submissions' AND column_name='integrations') THEN
    ALTER TABLE tool_submissions ADD COLUMN integrations TEXT;
    COMMENT ON COLUMN tool_submissions.integrations IS 'Third-party integrations and compatibility';
  END IF;
END $$;

DO $$ BEGIN
  IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name='tool_submissions' AND column_name='tags') THEN
    ALTER TABLE tool_submissions ADD COLUMN tags TEXT;
    COMMENT ON COLUMN tool_submissions.tags IS 'Comma-separated tags for categorization';
  END IF;
END $$;

DO $$ BEGIN
  IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name='tool_submissions' AND column_name='meta_keywords') THEN
    ALTER TABLE tool_submissions ADD COLUMN meta_keywords TEXT;
    COMMENT ON COLUMN tool_submissions.meta_keywords IS 'SEO keywords for search optimization';
  END IF;
END $$;

-- Ensure all enhanced fields exist in tools table
DO $$ BEGIN
  IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name='tools' AND column_name='detailed_description') THEN
    ALTER TABLE tools ADD COLUMN detailed_description TEXT;
    COMMENT ON COLUMN tools.detailed_description IS 'Comprehensive tool description for detail pages';
  END IF;
END $$;

DO $$ BEGIN
  IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name='tools' AND column_name='use_cases') THEN
    ALTER TABLE tools ADD COLUMN use_cases TEXT;
    COMMENT ON COLUMN tools.use_cases IS 'Common use cases and scenarios';
  END IF;
END $$;

DO $$ BEGIN
  IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name='tools' AND column_name='target_audience') THEN
    ALTER TABLE tools ADD COLUMN target_audience TEXT;
    COMMENT ON COLUMN tools.target_audience IS 'Primary target audience description';
  END IF;
END $$;

DO $$ BEGIN
  IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name='tools' AND column_name='integrations') THEN
    ALTER TABLE tools ADD COLUMN integrations TEXT;
    COMMENT ON COLUMN tools.integrations IS 'Third-party integrations and compatibility';
  END IF;
END $$;

DO $$ BEGIN
  IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name='tools' AND column_name='tags') THEN
    ALTER TABLE tools ADD COLUMN tags TEXT[];
    COMMENT ON COLUMN tools.tags IS 'Array of tool tags for categorization';
  END IF;
END $$;

DO $$ BEGIN
  IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name='tools' AND column_name='meta_keywords') THEN
    ALTER TABLE tools ADD COLUMN meta_keywords TEXT;
    COMMENT ON COLUMN tools.meta_keywords IS 'SEO keywords for search optimization';
  END IF;
END $$;

-- Add submission tracking fields to tools table
DO $$ BEGIN
  IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name='tools' AND column_name='submission_id') THEN
    ALTER TABLE tools ADD COLUMN submission_id UUID;
    COMMENT ON COLUMN tools.submission_id IS 'Reference to original tool_submissions record';
  END IF;
END $$;

DO $$ BEGIN
  IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name='tools' AND column_name='submitted_by') THEN
    ALTER TABLE tools ADD COLUMN submitted_by VARCHAR(255);
    COMMENT ON COLUMN tools.submitted_by IS 'Original submitter name/email';
  END IF;
END $$;

DO $$ BEGIN
  IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name='tools' AND column_name='submission_date') THEN
    ALTER TABLE tools ADD COLUMN submission_date TIMESTAMP;
    COMMENT ON COLUMN tools.submission_date IS 'Original submission timestamp';
  END IF;
END $$;

DO $$ BEGIN
  IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name='tools' AND column_name='approved_by') THEN
    ALTER TABLE tools ADD COLUMN approved_by VARCHAR(255);
    COMMENT ON COLUMN tools.approved_by IS 'Admin who approved the submission';
  END IF;
END $$;

DO $$ BEGIN
  IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name='tools' AND column_name='approved_at') THEN
    ALTER TABLE tools ADD COLUMN approved_at TIMESTAMP;
    COMMENT ON COLUMN tools.approved_at IS 'Timestamp of approval';
  END IF;
END $$;

DO $$ BEGIN
  IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name='tools' AND column_name='editorial_notes') THEN
    ALTER TABLE tools ADD COLUMN editorial_notes TEXT;
    COMMENT ON COLUMN tools.editorial_notes IS 'Internal editorial notes and comments';
  END IF;
END $$;

-- Add simplified media fields to tools table
DO $$ BEGIN
  IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name='tools' AND column_name='primary_image') THEN
    ALTER TABLE tools ADD COLUMN primary_image TEXT;
    COMMENT ON COLUMN tools.primary_image IS 'Primary image URL (screenshot or OG image)';
  END IF;
END $$;

DO $$ BEGIN
  IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name='tools' AND column_name='primary_image_type') THEN
    ALTER TABLE tools ADD COLUMN primary_image_type VARCHAR(20) CHECK (primary_image_type IN ('screenshot', 'ogImage'));
    COMMENT ON COLUMN tools.primary_image_type IS 'Type of primary image: screenshot or ogImage';
  END IF;
END $$;

DO $$ BEGIN
  IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name='tools' AND column_name='logo_url') THEN
    ALTER TABLE tools ADD COLUMN logo_url TEXT;
    COMMENT ON COLUMN tools.logo_url IS 'Logo/favicon URL';
  END IF;
END $$;

DO $$ BEGIN
  IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name='tools' AND column_name='media_source') THEN
    ALTER TABLE tools ADD COLUMN media_source VARCHAR(50) DEFAULT 'auto_collected';
    COMMENT ON COLUMN tools.media_source IS 'Source of media: auto_collected, user_provided, manual';
  END IF;
END $$;

DO $$ BEGIN
  IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name='tools' AND column_name='media_updated_at') THEN
    ALTER TABLE tools ADD COLUMN media_updated_at TIMESTAMP;
    COMMENT ON COLUMN tools.media_updated_at IS 'When media assets were last updated';
  END IF;
END $$;

-- Add indexes for performance
CREATE INDEX IF NOT EXISTS idx_tool_submissions_integrations ON tool_submissions USING GIN (to_tsvector('english', integrations));
CREATE INDEX IF NOT EXISTS idx_tool_submissions_tags ON tool_submissions(tags);
CREATE INDEX IF NOT EXISTS idx_tool_submissions_meta_keywords ON tool_submissions(meta_keywords);

CREATE INDEX IF NOT EXISTS idx_tools_submission_id ON tools(submission_id);
CREATE INDEX IF NOT EXISTS idx_tools_submitted_by ON tools(submitted_by);
CREATE INDEX IF NOT EXISTS idx_tools_submission_date ON tools(submission_date);
CREATE INDEX IF NOT EXISTS idx_tools_tags ON tools USING GIN (tags);
CREATE INDEX IF NOT EXISTS idx_tools_use_cases ON tools USING GIN (to_tsvector('english', use_cases));
CREATE INDEX IF NOT EXISTS idx_tools_target_audience ON tools USING GIN (to_tsvector('english', target_audience));
CREATE INDEX IF NOT EXISTS idx_tools_media_source ON tools(media_source);
CREATE INDEX IF NOT EXISTS idx_tools_primary_image_type ON tools(primary_image_type);

-- Add foreign key constraint
DO $$ BEGIN
  IF NOT EXISTS (SELECT 1 FROM information_schema.table_constraints WHERE constraint_name='fk_tools_submission_id') THEN
    ALTER TABLE tools ADD CONSTRAINT fk_tools_submission_id 
    FOREIGN KEY (submission_id) REFERENCES tool_submissions(id) ON DELETE SET NULL;
  END IF;
END $$;

-- Update existing tools to have default media_source
UPDATE tools 
SET media_source = 'auto_collected' 
WHERE media_source IS NULL;
