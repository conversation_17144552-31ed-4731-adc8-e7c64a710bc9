/**
 * Unit Tests for Scrape.do API Client
 * Tests API integration, authentication, response handling, and enhanced scraping parameters
 */

import { describe, it, expect, beforeEach, afterEach, jest } from '@jest/globals';
import { ScrapeDoClient } from '@/lib/scraping/scrape-do-client';
import { ScrapeOptions, ScrapeResult, UsageStats } from '@/lib/scraping/types';
import { setupMSWServer, mockScrapeDoResponse, mockScrapedContent } from '@/__mocks__/msw-server';

// Setup MSW server for consistent API mocking
setupMSWServer();

// Mock environment variables
const mockEnv = {
  SCRAPE_DO_API_KEY: 'test-api-key-12345',
  SCRAPE_DO_BASE_URL: 'https://api.scrape.do'
};

Object.defineProperty(process, 'env', {
  value: { ...process.env, ...mockEnv }
});

describe('ScrapeDoClient', () => {
  let client: ScrapeDoClient;

  beforeEach(() => {
    client = new ScrapeDoClient();
    jest.clearAllMocks();
  });

  describe('Constructor and Configuration', () => {
    it('should initialize with correct default configuration', () => {
      expect(client).toBeInstanceOf(ScrapeDoClient);
    });

    it('should use environment variables for configuration', () => {
      // Test that the client uses the mocked environment variables
      expect(process.env.SCRAPE_DO_API_KEY).toBe('test-api-key-12345');
      expect(process.env.SCRAPE_DO_BASE_URL).toBe('https://api.scrape.do');
    });
  });

  describe('Basic Scraping Functionality', () => {
    it('should perform basic scraping with minimal options', async () => {
      // MSW will return the default JSON response, but client should handle it as text
      const result = await client.scrapePage('https://example.com');

      expect(result.success).toBe(true);
      // The MSW server returns JSON, but the client should extract the content field
      expect(result.content).toContain('Test Page Title');
      expect(result.content).toContain('This is test content with sufficient length');
      expect(result.url).toBe('https://example.com');
    });

    it('should handle API errors gracefully', async () => {
      // Use MSW helper to mock error response
      mockScrapeDoResponse('https://error-test.com',
        { error: 'Scraping failed', message: 'Target site blocked request' },
        400
      );

      const result = await client.scrapePage('https://error-test.com');

      expect(result.success).toBe(false);
      expect(result.error).toContain('HTTP 400');
    });

    it('should handle network errors', async () => {
      // Use MSW helper to mock network error
      mockScrapeDoResponse('https://network-error.com',
        { error: 'Network error', message: 'Connection failed' },
        500
      );

      const result = await client.scrapePage('https://network-error.com');

      expect(result.success).toBe(false);
      expect(result.error).toContain('HTTP 500');
    });
  });

  describe('Enhanced Scraping with render=true', () => {
    it('should configure enhanced scraping options correctly', async () => {
      const options: ScrapeOptions = {
        enableJSRendering: true,
        waitCondition: 'networkidle0',
        customWaitTime: 3000,
        blockResources: true,
        timeout: 45000
      };

      const result = await client.scrapePage('https://spa-example.com', options);

      expect(result.success).toBe(true);
      expect(result.content).toContain('Test Page Title');
      expect(result.metadata?.browserEnabled).toBe(true);
      expect(result.metadata?.requestType).toContain('Browser');
    });

    it('should handle different wait conditions', async () => {
      const waitConditions = ['domcontentloaded', 'load', 'networkidle0', 'networkidle2'] as const;

      for (const waitCondition of waitConditions) {
        const options: ScrapeOptions = {
          enableJSRendering: true,
          waitCondition
        };

        const result = await client.scrapePage('https://example.com', options);

        expect(result.success).toBe(true);
        expect(result.content).toContain('Test Page Title');
        expect(result.metadata?.browserEnabled).toBe(true);
      }
    });

    it('should configure device types correctly', async () => {
      const deviceTypes = ['desktop', 'mobile', 'tablet'] as const;

      for (const deviceType of deviceTypes) {
        const options: ScrapeOptions = {
          deviceType
        };

        const result = await client.scrapePage('https://example.com', options);

        expect(result.success).toBe(true);
        expect(result.content).toContain('Test Page Title');
      }
    });
  });

  describe('Proxy Configuration', () => {
    it('should configure residential proxy correctly', async () => {
      const options: ScrapeOptions = {
        useResidentialProxy: true,
        geoTargeting: 'us',
        stickySession: 12345
      };

      const result = await client.scrapePage('https://example.com', options);

      expect(result.success).toBe(true);
      expect(result.content).toContain('Test Page Title');
      expect(result.metadata?.proxyType).toBe('residential');
    });

    it('should default to datacenter proxy when residential is false', async () => {
      const options: ScrapeOptions = {
        useResidentialProxy: false
      };

      const result = await client.scrapePage('https://example.com', options);

      expect(result.success).toBe(true);
      expect(result.content).toContain('Test Page Title');
      expect(result.metadata?.proxyType).toBe('datacenter');
    });
  });

  describe('Screenshot Functionality', () => {
    it('should handle JSON response for screenshots', async () => {
      // Mock a response with screenshots
      mockScrapeDoResponse('https://screenshot-test.com', {
        content: 'Page content',
        screenShots: [
          {
            type: 'ScreenShot',
            image: 'base64-encoded-image-data'
          }
        ]
      });

      const options: ScrapeOptions = {
        captureScreenshot: true,
        returnJSON: true
      };

      const result = await client.scrapePage('https://screenshot-test.com', options);

      expect(result.success).toBe(true);
      expect(result.content).toBe('Page content');
      expect(result.screenShots).toHaveLength(1);
      expect(result.screenShots![0].type).toBe('ScreenShot');
    });

    it('should handle full page screenshots', async () => {
      const options: ScrapeOptions = {
        fullPageScreenshot: true,
        returnJSON: true
      };

      const result = await client.scrapePage('https://example.com', options);

      expect(result.success).toBe(true);
      expect(result.content).toContain('Test Page Title');
      // The MSW server should return a fullScreenshot when fullPageScreenshot is requested
      expect(result.fullScreenshot).toBeDefined();
    });
  });

  describe('Usage Statistics', () => {
    it('should fetch usage statistics', async () => {
      const stats = await client.getUsageStatistics();

      expect(stats.isActive).toBe(true);
      expect(stats.concurrentRequests).toBe(10);
      expect(stats.maxMonthlyRequests).toBe(1500);
      expect(stats.remainingConcurrentRequests).toBe(8);
      expect(stats.remainingMonthlyRequests).toBe(1000);
      expect(stats.lastUpdated).toBeDefined();
    });

    it('should handle usage statistics errors', async () => {
      // Mock an error response for usage statistics
      mockScrapeDoResponse('https://api.scrape.do/info',
        { error: 'Unauthorized' },
        401
      );

      // Override the MSW handler for the /info endpoint specifically
      const { server } = await import('@/__mocks__/msw-server');
      const { http, HttpResponse } = await import('msw');

      server.use(
        http.get('https://api.scrape.do/info', () => {
          return HttpResponse.json(
            { error: 'Unauthorized' },
            { status: 401 }
          );
        })
      );

      await expect(client.getUsageStatistics()).rejects.toThrow('Failed to fetch usage stats');
    });
  });

  describe('Retry Logic', () => {
    it('should retry failed requests', async () => {
      // Use timeout-test URL which MSW handles with error response
      const result = await client.scrapePage('https://timeout-test.com');

      // The client should handle the error response from MSW
      expect(result.success).toBe(false);
      expect(result.error).toContain('HTTP 408');
    });

    it('should fail after maximum retry attempts', async () => {
      // Use error-test URL which MSW handles with error response
      const result = await client.scrapePage('https://error-test.com');

      expect(result.success).toBe(false);
      expect(result.error).toContain('HTTP 400');
    });
  });

  describe('Parameter Building', () => {
    it('should build request parameters correctly', async () => {
      const options: ScrapeOptions = {
        enableJSRendering: true,
        waitCondition: 'networkidle2',
        customWaitTime: 5000,
        blockResources: true,
        timeout: 60000,
        deviceType: 'mobile',
        outputFormat: 'markdown',
        useResidentialProxy: true,
        geoTargeting: 'uk'
      };

      const result = await client.scrapePage('https://example.com', options);

      // Verify the request was successful and metadata reflects the options
      expect(result.success).toBe(true);
      expect(result.content).toContain('Test Page Title');
      expect(result.metadata?.browserEnabled).toBe(true);
      expect(result.metadata?.proxyType).toBe('residential');
      expect(result.metadata?.requestType).toContain('Residential + Browser');
    });
  });
});
