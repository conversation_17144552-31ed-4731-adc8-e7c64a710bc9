'use client';

import { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { apiClient } from '@/lib/api';
import { DbTool, ContentStatus } from '@/lib/types';
import { EnhancedToolsTable } from '@/components/admin/EnhancedToolsTable';
import { ToolBulkActions } from '@/components/admin/ToolBulkActions';
import { BulkProcessingManager } from '@/components/admin/BulkProcessingManager';
import { ImportExportPanel } from '@/components/admin/tools/ImportExportPanel';
import { EnhancedToolsFilters } from '@/components/admin/EnhancedToolsFilters';

interface EnhancedFilters {
  search: string;
  contentStatus: string;
  submissionType: string;
  submittedBy: string;
  dateRange: string;
  hasMedia: string;
}

export default function ToolsManagementPage() {
  const router = useRouter();
  const [tools, setTools] = useState<DbTool[]>([]);
  const [loading, setLoading] = useState(true);
  const [selectedToolIds, setSelectedToolIds] = useState<string[]>([]);
  const [showImportExport, setShowImportExport] = useState(false);
  const [showBulkProcessing, setShowBulkProcessing] = useState(false);
  const [filters, setFilters] = useState<EnhancedFilters>({
    search: '',
    contentStatus: '',
    submissionType: '',
    submittedBy: '',
    dateRange: '',
    hasMedia: ''
  });
  const [currentPage, setCurrentPage] = useState(1);
  const [totalPages, setTotalPages] = useState(1);
  const [totalTools, setTotalTools] = useState(0);
  const itemsPerPage = 20;

  // Enhanced filter change handler
  const handleFilterChange = (newFilters: Partial<EnhancedFilters>) => {
    setFilters(prev => ({ ...prev, ...newFilters }));
    setCurrentPage(1); // Reset to first page when filters change
  };

  useEffect(() => {
    loadData();
  }, [currentPage, filters]);

  const loadData = async () => {
    try {
      // Build query parameters with enhanced filters
      const params = new URLSearchParams({
        page: currentPage.toString(),
        limit: itemsPerPage.toString(),
        ...Object.fromEntries(
          Object.entries(filters).filter(([_, value]) => value !== '')
        )
      });

      const response = await fetch(`/api/admin/tools?${params}`, {
        headers: {
          'x-admin-api-key': process.env.NEXT_PUBLIC_ADMIN_API_KEY || 'aidude_admin_2024_secure_key_xyz789'
        }
      });

      if (!response.ok) {
        throw new Error(`Failed to fetch tools: ${response.status}`);
      }

      const result = await response.json();

      if (result.success) {
        setTools(result.data.tools || []);
        setTotalPages(result.data.pagination?.totalPages || 1);
        setTotalTools(result.data.pagination?.totalItems || 0);
      } else {
        throw new Error(result.error || 'Failed to fetch tools');
      }

      // Validate API response structure
      if (!result.success) {
        throw new Error(result.error || 'Failed to fetch tools');
      }

      // Tools are already in the correct format from the enhanced API
      setTools(result.data.tools || []);
      setTotalPages(result.data.pagination?.totalPages || 1);
      setTotalTools(result.data.pagination?.totalItems || 0);
    } catch (error) {
      console.error('Failed to load data:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleDeleteTool = async (toolId: string) => {
    if (!confirm('Are you sure you want to delete this tool?')) return;

    try {
      const response = await fetch(`/api/admin/tools/${toolId}`, {
        method: 'DELETE',
        headers: {
          'x-admin-api-key': process.env.NEXT_PUBLIC_ADMIN_API_KEY || 'aidude_admin_2024_secure_key_xyz789'
        }
      });

      const result = await response.json();

      if (result.success) {
        setTools(tools.filter(t => t.id !== toolId));
        setTotalTools(prev => prev - 1);
        alert('Tool deleted successfully!');
      } else {
        throw new Error(result.error || 'Failed to delete tool');
      }
    } catch (error) {
      alert('Failed to delete tool: ' + (error as Error).message);
    }
  };

  const handleUpdateStatus = async (toolId: string, updates: Partial<DbTool>) => {
    try {
      const response = await fetch(`/api/admin/tools/${toolId}`, {
        method: 'PATCH',
        headers: {
          'Content-Type': 'application/json',
          'x-admin-api-key': process.env.NEXT_PUBLIC_ADMIN_API_KEY || 'aidude_admin_2024_secure_key_xyz789'
        },
        body: JSON.stringify(updates)
      });

      const result = await response.json();

      if (result.success) {
        // Update local state
        setTools(prev => prev.map(tool =>
          tool.id === toolId ? { ...tool, ...updates } : tool
        ));
        alert('Tool updated successfully!');
      } else {
        throw new Error(result.error || 'Failed to update tool');
      }
    } catch (error) {
      alert('Failed to update tool: ' + (error as Error).message);
    }
  };

  // Selection handlers
  const handleToolSelection = (toolId: string, selected: boolean) => {
    setSelectedToolIds(prev =>
      selected
        ? [...prev, toolId]
        : prev.filter(id => id !== toolId)
    );
  };

  const handleSelectAll = (selected: boolean) => {
    setSelectedToolIds(selected ? tools.map(t => t.id) : []);
  };

  const handleClearSelection = () => {
    setSelectedToolIds([]);
  };

  // Bulk operations
  const handleBulkStatusUpdate = async (toolIds: string[], status: ContentStatus) => {
    try {
      const result = await apiClient.bulkUpdateToolStatus(toolIds, status, 'aidude_admin_2024_secure_key_xyz789');

      // Update local state
      setTools(tools.map(t =>
        toolIds.includes(t.id) ? { ...t, content_status: status } : t
      ));

      setSelectedToolIds([]);
      alert(result.message || `Successfully updated ${toolIds.length} tools`);
    } catch (error) {
      alert('Failed to update tools: ' + (error as Error).message);
    }
  };

  const handleBulkDelete = async (toolIds: string[]) => {
    try {
      const result = await apiClient.bulkDeleteTools(toolIds, 'aidude_admin_2024_secure_key_xyz789');

      // Update local state
      setTools(tools.filter(t => !toolIds.includes(t.id)));

      setSelectedToolIds([]);
      alert(result.message || `Successfully deleted ${toolIds.length} tools`);
    } catch (error) {
      alert('Failed to delete tools: ' + (error as Error).message);
    }
  };

  const handleImportComplete = () => {
    loadData(); // Reload data after import
    setShowImportExport(false);
  };

  const handleError = (error: string) => {
    alert('Error: ' + error);
  };

  if (loading) {
    return (
      <div className="min-h-screen bg-zinc-900 flex items-center justify-center">
        <div className="text-white text-xl">Loading tools management...</div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Page Header */}
      <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
        <div>
          <h1 className="text-2xl font-bold text-white">Tool Management</h1>
          <p className="text-gray-400 mt-1">Manage your AI tools directory</p>
        </div>
        <div className="flex flex-wrap gap-3">
          <button
            onClick={() => router.push('/admin/tools/new')}
            className="text-white px-4 py-2 rounded-lg font-medium transition-all duration-200"
            style={{
              backgroundColor: 'rgb(255, 150, 0)'
            }}
            onMouseEnter={(e) => {
              e.currentTarget.style.backgroundColor = 'rgb(255, 170, 30)';
            }}
            onMouseLeave={(e) => {
              e.currentTarget.style.backgroundColor = 'rgb(255, 150, 0)';
            }}
          >
            ➕ Add New Tool
          </button>
          <button
            onClick={() => setShowImportExport(!showImportExport)}
            className="text-white px-4 py-2 rounded-lg font-medium transition-all duration-200 bg-zinc-700 hover:bg-zinc-600"
          >
            {showImportExport ? '📤 Hide Import/Export' : '📥 Import/Export Tools'}
          </button>

          <button
            onClick={() => setShowBulkProcessing(!showBulkProcessing)}
            className="text-white px-4 py-2 rounded-lg font-medium transition-all duration-200 bg-purple-700 hover:bg-purple-600"
          >
            {showBulkProcessing ? '🔄 Hide Bulk Processing' : '⚡ Bulk Processing'}
          </button>
        </div>
      </div>

      {/* Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <div className="bg-zinc-800 border border-black rounded-lg p-4 shadow-lg">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-gray-400 text-sm">Total Tools</p>
              <p className="text-xl font-bold text-white">{tools.length}</p>
            </div>
            <div className="text-2xl">🔧</div>
          </div>
        </div>

        <div className="bg-zinc-800 border border-black rounded-lg p-4 shadow-lg">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-gray-400 text-sm">Published</p>
              <p className="text-xl font-bold text-white">
                {tools.filter(t => t.content_status === 'published').length}
              </p>
            </div>
            <div className="text-2xl">✅</div>
          </div>
        </div>

        <div className="bg-zinc-800 border border-black rounded-lg p-4 shadow-lg">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-gray-400 text-sm">Drafts</p>
              <p className="text-xl font-bold text-white">
                {tools.filter(t => t.content_status === 'draft' || !t.content_status).length}
              </p>
            </div>
            <div className="text-2xl">📝</div>
          </div>
        </div>

        <div className="bg-zinc-800 border border-black rounded-lg p-4 shadow-lg">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-gray-400 text-sm">Archived</p>
              <p className="text-xl font-bold text-white">
                {tools.filter(t => t.content_status === 'archived').length}
              </p>
            </div>
            <div className="text-2xl">📦</div>
          </div>
        </div>
      </div>

      {/* Bulk Actions */}
      <ToolBulkActions
        selectedCount={selectedToolIds.length}
        selectedToolIds={selectedToolIds}
        onBulkStatusUpdate={handleBulkStatusUpdate}
        onBulkDelete={handleBulkDelete}
        onClearSelection={handleClearSelection}
      />

      {/* Enhanced Filters */}
      <EnhancedToolsFilters
        filters={filters}
        onFilterChange={handleFilterChange}
        loading={loading}
      />

      {/* Enhanced Tools Table */}
      <EnhancedToolsTable
        tools={tools}
        loading={loading}
        selectedTools={selectedToolIds}
        onSelectionChange={setSelectedToolIds}
        onToolUpdate={handleUpdateStatus}
        onToolDelete={handleDeleteTool}
        currentPage={currentPage}
        totalPages={totalPages}
        onPageChange={setCurrentPage}
      />

      {/* Import/Export Panel */}
      {showImportExport && (
        <ImportExportPanel
          onImportComplete={handleImportComplete}
          onError={handleError}
        />
      )}

      {/* Bulk Processing Panel */}
      {showBulkProcessing && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
          <div className="bg-zinc-900 rounded-lg max-w-6xl w-full max-h-[90vh] overflow-hidden">
            <BulkProcessingManager
              onClose={() => setShowBulkProcessing(false)}
            />
          </div>
        </div>
      )}
    </div>
  );
}
