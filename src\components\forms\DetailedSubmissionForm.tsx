'use client';

import { useState } from 'react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { z } from 'zod';
import { apiClient } from '@/lib/api';

// Validation schema for detailed submission
const detailedSubmissionSchema = z.object({
  // Basic Information
  name: z.string().min(1, 'Tool name is required').max(100, 'Tool name must be less than 100 characters'),
  url: z.string().url('Please enter a valid URL'),
  description: z.string().min(50, 'Description must be at least 50 characters').max(500, 'Description must be less than 500 characters'),
  detailedDescription: z.string().min(150, 'Detailed description must be at least 150 characters').max(2000, 'Detailed description must be less than 2000 characters'),
  category: z.string().min(1, 'Please select a category'),
  subcategory: z.string().optional(),
  
  // Features and Pricing
  features: z.string().min(50, 'Features must be at least 50 characters').max(1000, 'Features must be less than 1000 characters'),
  pricingType: z.string().min(1, 'Please select a pricing type'),
  pricingDetails: z.string().min(20, 'Pricing details must be at least 20 characters').max(500, 'Pricing details must be less than 500 characters'),
  
  // Pros and Cons
  pros: z.string().min(30, 'Pros must be at least 30 characters').max(500, 'Pros must be less than 500 characters'),
  cons: z.string().min(30, 'Cons must be at least 30 characters').max(500, 'Cons must be less than 500 characters'),
  
  // SEO and Meta
  metaTitle: z.string().min(10, 'Meta title must be at least 10 characters').max(60, 'Meta title must be less than 60 characters'),
  metaDescription: z.string().min(50, 'Meta description must be at least 50 characters').max(160, 'Meta description must be less than 160 characters'),
  metaKeywords: z.string().optional(),

  // Company and Use Cases
  company: z.string().min(1, 'Company name is required').max(100, 'Company name must be less than 100 characters'),
  useCases: z.string().min(50, 'Use cases must be at least 50 characters').max(1000, 'Use cases must be less than 1000 characters'),
  targetAudience: z.string().min(30, 'Target audience must be at least 30 characters').max(500, 'Target audience must be less than 500 characters'),

  // Additional fields
  tags: z.string().optional(),
  integrations: z.string().optional(),

  // Media Assets (simplified - one primary image + one logo required)
  primaryImage: z.any().optional(), // File upload - screenshot OR OG image
  primaryImageType: z.enum(['screenshot', 'ogImage']).optional(),
  logo: z.any().optional(), // File upload for logo/favicon
  logoUrl: z.string().url().optional().or(z.literal('')), // URL alternative for logo

  // FAQ (optional)
  faq: z.string().optional(),
  
  // Submitter Information
  submitterName: z.string().min(1, 'Your name is required').max(100, 'Name must be less than 100 characters'),
  submitterEmail: z.string().email('Please enter a valid email address'),
});

type DetailedSubmissionData = z.infer<typeof detailedSubmissionSchema>;

interface DetailedSubmissionFormProps {
  onSuccess?: () => void;
  onBack?: () => void;
}

export function DetailedSubmissionForm({ onSuccess, onBack }: DetailedSubmissionFormProps) {
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [submitError, setSubmitError] = useState<string | null>(null);
  const [success, setSuccess] = useState(false);
  const [currentSection, setCurrentSection] = useState(0);
  const [uploadedFiles, setUploadedFiles] = useState<{
    primaryImage?: File;
    primaryImageType?: 'screenshot' | 'ogImage';
    logo?: File;
  }>({});

  const {
    register,
    handleSubmit,
    formState: { errors },
    reset,
    watch,
  } = useForm<DetailedSubmissionData>({
    resolver: zodResolver(detailedSubmissionSchema),
    mode: 'onChange'
  });

  const categories = [
    { value: 'writing-tools', label: 'Writing Tools' },
    { value: 'image-generators', label: 'Image Generators' },
    { value: 'chatbots', label: 'Chatbots' },
    { value: 'dev-tools', label: 'Developer Tools' },
    { value: 'video-ai', label: 'Video AI' },
    { value: 'music-generation', label: 'Music Generation' },
    { value: 'voice-ai', label: 'Voice AI' },
    { value: 'data-analysis', label: 'Data Analysis' },
    { value: 'productivity-ai', label: 'Productivity AI' },
    { value: 'design-ai', label: 'Design AI' },
    { value: 'marketing-ai', label: 'Marketing AI' },
    { value: 'education-ai', label: 'Education AI' },
    { value: 'healthcare-ai', label: 'Healthcare AI' },
    { value: 'finance-ai', label: 'Finance AI' },
  ];

  const pricingTypes = [
    { value: 'free', label: 'Free' },
    { value: 'freemium', label: 'Freemium' },
    { value: 'paid', label: 'Paid' },
    { value: 'open-source', label: 'Open Source' },
  ];

  const sections = [
    { title: 'Basic Information', fields: ['name', 'url', 'description', 'detailedDescription', 'category', 'subcategory'] },
    { title: 'Features & Pricing', fields: ['features', 'pricingType', 'pricingDetails'] },
    { title: 'Analysis & SEO', fields: ['pros', 'cons', 'metaTitle', 'metaDescription', 'metaKeywords'] },
    { title: 'Company & Audience', fields: ['company', 'useCases', 'targetAudience', 'tags', 'integrations'] },
    { title: 'Media Assets', fields: ['primaryImage', 'logo', 'logoUrl'] },
    { title: 'Additional & Contact', fields: ['faq', 'submitterName', 'submitterEmail'] },
  ];

  const onSubmit = async (data: DetailedSubmissionData) => {
    setIsSubmitting(true);
    setSubmitError(null);

    try {
      // Create FormData for file uploads
      const formData = new FormData();

      // Add all text fields
      Object.entries(data).forEach(([key, value]) => {
        if (value !== undefined && value !== null && value !== '') {
          formData.append(key, value.toString());
        }
      });

      // Add uploaded files
      if (uploadedFiles.primaryImage) {
        formData.append('primaryImage', uploadedFiles.primaryImage);
        formData.append('primaryImageType', uploadedFiles.primaryImageType || 'screenshot');
      }
      if (uploadedFiles.logo) {
        formData.append('logo', uploadedFiles.logo);
      }

      // Submit to detailed submission API
      const response = await fetch('/api/submissions/detailed', {
        method: 'POST',
        body: formData,
      });

      const result = await response.json();

      if (!response.ok) {
        throw new Error(result.error || 'Failed to submit detailed submission');
      }

      console.log('✅ Detailed submission successful:', result);
      setSuccess(true);
      reset();
      setUploadedFiles({});
      setCurrentSection(0);

      if (onSuccess) {
        setTimeout(onSuccess, 2000);
      }
    } catch (err) {
      setSubmitError(err instanceof Error ? err.message : 'Failed to submit detailed submission');
    } finally {
      setIsSubmitting(false);
    }
  };

  if (success) {
    return (
      <div className="bg-zinc-800 p-8 rounded-lg border border-green-500">
        <div className="text-center">
          <div className="text-green-400 text-4xl mb-4">✅</div>
          <h3 className="text-2xl font-bold text-white mb-4">Detailed Submission Received!</h3>
          <p className="text-gray-300 mb-6">
            Thank you for providing comprehensive details about your tool! 
            Your submission will be reviewed by our editorial team.
          </p>
          <div className="bg-zinc-700/50 rounded-lg p-4 mb-6">
            <h4 className="font-semibold mb-2 text-orange-400">What happens next:</h4>
            <ul className="text-sm text-gray-300 space-y-2 text-left">
              <li>• Editorial team reviews your detailed content (12-24 hours)</li>
              <li>• Quality check and fact verification</li>
              <li>• Your tool goes live on our directory</li>
              <li>• You'll receive email confirmation when published</li>
            </ul>
          </div>
          <p className="text-sm text-gray-400">
            Expected review time: 12-24 hours (faster than simple submissions!)
          </p>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-8">
      {/* Header */}
      <div className="text-center">
        <button
          onClick={onBack}
          className="inline-flex items-center text-gray-400 hover:text-white mb-4 transition-colors"
        >
          ← Back to submission options
        </button>
        <h2 className="text-3xl font-bold text-white mb-4">📝 Detailed Submission</h2>
        <p className="text-gray-300">
          Provide comprehensive details for maximum accuracy and faster approval
        </p>
      </div>

      {/* Progress Indicator */}
      <div className="bg-zinc-800 p-4 rounded-lg border border-zinc-700">
        <div className="flex justify-between items-center mb-2">
          <span className="text-sm text-gray-300">Progress</span>
          <span className="text-sm text-gray-300">{currentSection + 1} of {sections.length}</span>
        </div>
        <div className="w-full bg-zinc-700 rounded-full h-2">
          <div 
            className="bg-orange-500 h-2 rounded-full transition-all duration-300"
            style={{ width: `${((currentSection + 1) / sections.length) * 100}%` }}
          ></div>
        </div>
        <div className="flex justify-between mt-2">
          {sections.map((section, index) => (
            <button
              key={index}
              onClick={() => setCurrentSection(index)}
              className={`text-xs px-2 py-1 rounded transition-colors ${
                index === currentSection 
                  ? 'bg-orange-500 text-white' 
                  : index < currentSection 
                    ? 'bg-green-600 text-white' 
                    : 'bg-zinc-700 text-gray-400'
              }`}
            >
              {section.title}
            </button>
          ))}
        </div>
      </div>

      {/* Form */}
      <div className="bg-zinc-800 p-6 rounded-lg border border-zinc-700">
        <form onSubmit={handleSubmit(onSubmit)} className="space-y-6">
          {submitError && (
            <div className="bg-red-900/20 border border-red-500 rounded-lg p-4">
              <p className="text-red-400 text-sm">{submitError}</p>
            </div>
          )}

          <h3 className="text-xl font-bold text-white mb-4">
            {sections[currentSection].title}
          </h3>

          {/* Section 0: Basic Information */}
          {currentSection === 0 && (
            <div className="space-y-6">
              {/* Tool Name */}
              <div>
                <label htmlFor="name" className="block text-sm font-medium text-gray-300 mb-2">
                  Tool Name *
                </label>
                <input
                  type="text"
                  id="name"
                  {...register('name')}
                  className={`w-full px-3 py-2 bg-zinc-700 border rounded-md text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-orange-500 focus:border-transparent ${
                    errors.name ? 'border-red-500' : 'border-zinc-600'
                  }`}
                  placeholder="Enter the name of your AI tool"
                />
                {errors.name && (
                  <p className="mt-1 text-sm text-red-400">{errors.name.message}</p>
                )}
              </div>

              {/* Website URL */}
              <div>
                <label htmlFor="url" className="block text-sm font-medium text-gray-300 mb-2">
                  Website URL *
                </label>
                <input
                  type="url"
                  id="url"
                  {...register('url')}
                  className={`w-full px-3 py-2 bg-zinc-700 border rounded-md text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-orange-500 focus:border-transparent ${
                    errors.url ? 'border-red-500' : 'border-zinc-600'
                  }`}
                  placeholder="https://your-ai-tool.com"
                />
                {errors.url && (
                  <p className="mt-1 text-sm text-red-400">{errors.url.message}</p>
                )}
              </div>

              {/* Short Description */}
              <div>
                <label htmlFor="description" className="block text-sm font-medium text-gray-300 mb-2">
                  Short Description *
                </label>
                <textarea
                  id="description"
                  {...register('description')}
                  rows={3}
                  className={`w-full px-3 py-2 bg-zinc-700 border rounded-md text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-orange-500 focus:border-transparent ${
                    errors.description ? 'border-red-500' : 'border-zinc-600'
                  }`}
                  placeholder="Brief description for listings and previews (50-500 characters)"
                />
                {errors.description && (
                  <p className="mt-1 text-sm text-red-400">{errors.description.message}</p>
                )}
              </div>

              {/* Detailed Description */}
              <div>
                <label htmlFor="detailedDescription" className="block text-sm font-medium text-gray-300 mb-2">
                  Detailed Description *
                </label>
                <textarea
                  id="detailedDescription"
                  {...register('detailedDescription')}
                  rows={6}
                  className={`w-full px-3 py-2 bg-zinc-700 border rounded-md text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-orange-500 focus:border-transparent ${
                    errors.detailedDescription ? 'border-red-500' : 'border-zinc-600'
                  }`}
                  placeholder="Comprehensive description of your tool, its capabilities, and use cases (150-2000 characters)"
                />
                {errors.detailedDescription && (
                  <p className="mt-1 text-sm text-red-400">{errors.detailedDescription.message}</p>
                )}
              </div>

              {/* Category and Subcategory */}
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                  <label htmlFor="category" className="block text-sm font-medium text-gray-300 mb-2">
                    Category *
                  </label>
                  <select
                    id="category"
                    {...register('category')}
                    className={`w-full px-3 py-2 bg-zinc-700 border rounded-md text-white focus:outline-none focus:ring-2 focus:ring-orange-500 focus:border-transparent ${
                      errors.category ? 'border-red-500' : 'border-zinc-600'
                    }`}
                  >
                    <option value="">Select a category</option>
                    {categories.map(cat => (
                      <option key={cat.value} value={cat.value}>
                        {cat.label}
                      </option>
                    ))}
                  </select>
                  {errors.category && (
                    <p className="mt-1 text-sm text-red-400">{errors.category.message}</p>
                  )}
                </div>

                <div>
                  <label htmlFor="subcategory" className="block text-sm font-medium text-gray-300 mb-2">
                    Subcategory <span className="text-gray-500">(Optional)</span>
                  </label>
                  <input
                    type="text"
                    id="subcategory"
                    {...register('subcategory')}
                    className="w-full px-3 py-2 bg-zinc-700 border border-zinc-600 rounded-md text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-orange-500 focus:border-transparent"
                    placeholder="e.g., Text-to-Image, Code Completion"
                  />
                </div>
              </div>
            </div>
          )}

          {/* Section 1: Features & Pricing */}
          {currentSection === 1 && (
            <div className="space-y-6">
              {/* Features */}
              <div>
                <label htmlFor="features" className="block text-sm font-medium text-gray-300 mb-2">
                  Features *
                </label>
                <textarea
                  id="features"
                  {...register('features')}
                  rows={5}
                  className={`w-full px-3 py-2 bg-zinc-700 border rounded-md text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-orange-500 focus:border-transparent ${
                    errors.features ? 'border-red-500' : 'border-zinc-600'
                  }`}
                  placeholder="List the key features of your tool. Use bullet points or numbered lists for clarity."
                />
                {errors.features && (
                  <p className="mt-1 text-sm text-red-400">{errors.features.message}</p>
                )}
              </div>

              {/* Pricing Type and Details */}
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                  <label htmlFor="pricingType" className="block text-sm font-medium text-gray-300 mb-2">
                    Pricing Type *
                  </label>
                  <select
                    id="pricingType"
                    {...register('pricingType')}
                    className={`w-full px-3 py-2 bg-zinc-700 border rounded-md text-white focus:outline-none focus:ring-2 focus:ring-orange-500 focus:border-transparent ${
                      errors.pricingType ? 'border-red-500' : 'border-zinc-600'
                    }`}
                  >
                    <option value="">Select pricing type</option>
                    {pricingTypes.map(type => (
                      <option key={type.value} value={type.value}>
                        {type.label}
                      </option>
                    ))}
                  </select>
                  {errors.pricingType && (
                    <p className="mt-1 text-sm text-red-400">{errors.pricingType.message}</p>
                  )}
                </div>

                <div>
                  <label htmlFor="pricingDetails" className="block text-sm font-medium text-gray-300 mb-2">
                    Pricing Details *
                  </label>
                  <textarea
                    id="pricingDetails"
                    {...register('pricingDetails')}
                    rows={3}
                    className={`w-full px-3 py-2 bg-zinc-700 border rounded-md text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-orange-500 focus:border-transparent ${
                      errors.pricingDetails ? 'border-red-500' : 'border-zinc-600'
                    }`}
                    placeholder="Specific pricing information, plans, free tier details, etc."
                  />
                  {errors.pricingDetails && (
                    <p className="mt-1 text-sm text-red-400">{errors.pricingDetails.message}</p>
                  )}
                </div>
              </div>
            </div>
          )}

          {/* Section 2: Analysis & SEO */}
          {currentSection === 2 && (
            <div className="space-y-6">
              {/* Pros and Cons */}
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                  <label htmlFor="pros" className="block text-sm font-medium text-gray-300 mb-2">
                    Pros *
                  </label>
                  <textarea
                    id="pros"
                    {...register('pros')}
                    rows={4}
                    className={`w-full px-3 py-2 bg-zinc-700 border rounded-md text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-orange-500 focus:border-transparent ${
                      errors.pros ? 'border-red-500' : 'border-zinc-600'
                    }`}
                    placeholder="List the advantages and strengths of your tool"
                  />
                  {errors.pros && (
                    <p className="mt-1 text-sm text-red-400">{errors.pros.message}</p>
                  )}
                </div>

                <div>
                  <label htmlFor="cons" className="block text-sm font-medium text-gray-300 mb-2">
                    Cons *
                  </label>
                  <textarea
                    id="cons"
                    {...register('cons')}
                    rows={4}
                    className={`w-full px-3 py-2 bg-zinc-700 border rounded-md text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-orange-500 focus:border-transparent ${
                      errors.cons ? 'border-red-500' : 'border-zinc-600'
                    }`}
                    placeholder="List any limitations or areas for improvement"
                  />
                  {errors.cons && (
                    <p className="mt-1 text-sm text-red-400">{errors.cons.message}</p>
                  )}
                </div>
              </div>

              {/* SEO Meta Information */}
              <div>
                <label htmlFor="metaTitle" className="block text-sm font-medium text-gray-300 mb-2">
                  Meta Title *
                </label>
                <input
                  type="text"
                  id="metaTitle"
                  {...register('metaTitle')}
                  className={`w-full px-3 py-2 bg-zinc-700 border rounded-md text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-orange-500 focus:border-transparent ${
                    errors.metaTitle ? 'border-red-500' : 'border-zinc-600'
                  }`}
                  placeholder="SEO-optimized title for search engines (10-60 characters)"
                />
                {errors.metaTitle && (
                  <p className="mt-1 text-sm text-red-400">{errors.metaTitle.message}</p>
                )}
              </div>

              <div>
                <label htmlFor="metaDescription" className="block text-sm font-medium text-gray-300 mb-2">
                  Meta Description *
                </label>
                <textarea
                  id="metaDescription"
                  {...register('metaDescription')}
                  rows={3}
                  className={`w-full px-3 py-2 bg-zinc-700 border rounded-md text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-orange-500 focus:border-transparent ${
                    errors.metaDescription ? 'border-red-500' : 'border-zinc-600'
                  }`}
                  placeholder="SEO-optimized description for search results (50-160 characters)"
                />
                {errors.metaDescription && (
                  <p className="mt-1 text-sm text-red-400">{errors.metaDescription.message}</p>
                )}
              </div>

              {/* Meta Keywords */}
              <div>
                <label htmlFor="metaKeywords" className="block text-sm font-medium text-gray-300 mb-2">
                  Meta Keywords <span className="text-gray-500">(Optional)</span>
                </label>
                <input
                  type="text"
                  id="metaKeywords"
                  {...register('metaKeywords')}
                  className="w-full px-3 py-2 bg-zinc-700 border border-zinc-600 rounded-md text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-orange-500 focus:border-transparent"
                  placeholder="AI tool, productivity, automation (comma-separated)"
                />
              </div>
            </div>
          )}

          {/* Section 3: Company & Audience */}
          {currentSection === 3 && (
            <div className="space-y-6">
              {/* Company */}
              <div>
                <label htmlFor="company" className="block text-sm font-medium text-gray-300 mb-2">
                  Company/Organization *
                </label>
                <input
                  type="text"
                  id="company"
                  {...register('company')}
                  className={`w-full px-3 py-2 bg-zinc-700 border rounded-md text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-orange-500 focus:border-transparent ${
                    errors.company ? 'border-red-500' : 'border-zinc-600'
                  }`}
                  placeholder="Company or organization name"
                />
                {errors.company && (
                  <p className="mt-1 text-sm text-red-400">{errors.company.message}</p>
                )}
              </div>

              {/* Use Cases */}
              <div>
                <label htmlFor="useCases" className="block text-sm font-medium text-gray-300 mb-2">
                  Use Cases *
                </label>
                <textarea
                  id="useCases"
                  {...register('useCases')}
                  rows={4}
                  className={`w-full px-3 py-2 bg-zinc-700 border rounded-md text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-orange-500 focus:border-transparent ${
                    errors.useCases ? 'border-red-500' : 'border-zinc-600'
                  }`}
                  placeholder="Describe common use cases and scenarios where this tool is most effective"
                />
                {errors.useCases && (
                  <p className="mt-1 text-sm text-red-400">{errors.useCases.message}</p>
                )}
              </div>

              {/* Target Audience */}
              <div>
                <label htmlFor="targetAudience" className="block text-sm font-medium text-gray-300 mb-2">
                  Target Audience *
                </label>
                <textarea
                  id="targetAudience"
                  {...register('targetAudience')}
                  rows={3}
                  className={`w-full px-3 py-2 bg-zinc-700 border rounded-md text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-orange-500 focus:border-transparent ${
                    errors.targetAudience ? 'border-red-500' : 'border-zinc-600'
                  }`}
                  placeholder="Who is this tool designed for? (e.g., developers, marketers, content creators)"
                />
                {errors.targetAudience && (
                  <p className="mt-1 text-sm text-red-400">{errors.targetAudience.message}</p>
                )}
              </div>

              {/* Tags and Integrations */}
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                  <label htmlFor="tags" className="block text-sm font-medium text-gray-300 mb-2">
                    Tags <span className="text-gray-500">(Optional)</span>
                  </label>
                  <input
                    type="text"
                    id="tags"
                    {...register('tags')}
                    className="w-full px-3 py-2 bg-zinc-700 border border-zinc-600 rounded-md text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-orange-500 focus:border-transparent"
                    placeholder="productivity, automation, AI (comma-separated)"
                  />
                </div>

                <div>
                  <label htmlFor="integrations" className="block text-sm font-medium text-gray-300 mb-2">
                    Integrations <span className="text-gray-500">(Optional)</span>
                  </label>
                  <input
                    type="text"
                    id="integrations"
                    {...register('integrations')}
                    className="w-full px-3 py-2 bg-zinc-700 border border-zinc-600 rounded-md text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-orange-500 focus:border-transparent"
                    placeholder="Slack, Google Workspace, Zapier (comma-separated)"
                  />
                </div>
              </div>
            </div>
          )}

          {/* Section 4: Media Assets */}
          {currentSection === 4 && (
            <div className="space-y-6">
              <div className="bg-zinc-700/30 rounded-lg p-4 mb-6">
                <h4 className="font-semibold mb-2 text-orange-400">📸 Media Assets (Required)</h4>
                <p className="text-sm text-gray-300 mb-2">
                  Provide media assets for your tool. Both a primary image and logo are required for the best presentation.
                </p>
                <ul className="text-xs text-gray-400 space-y-1">
                  <li>• <strong>Primary Image:</strong> Either a screenshot OR social media image (required)</li>
                  <li>• <strong>Logo:</strong> Upload logo file OR provide logo URL (required)</li>
                </ul>
              </div>

              {/* Primary Image Upload */}
              <div>
                <label className="block text-sm font-medium text-gray-300 mb-2">
                  Primary Image <span className="text-red-400">*</span>
                </label>
                <p className="text-xs text-gray-400 mb-3">
                  Upload either a screenshot of your tool OR a social media preview image
                </p>

                {/* Image Type Selection */}
                <div className="flex gap-4 mb-4">
                  <label className="flex items-center">
                    <input
                      type="radio"
                      name="imageType"
                      value="screenshot"
                      checked={uploadedFiles.primaryImageType !== 'ogImage'}
                      onChange={() => setUploadedFiles(prev => ({ ...prev, primaryImageType: 'screenshot' }))}
                      className="mr-2"
                    />
                    <span className="text-sm text-gray-300">📸 Screenshot</span>
                  </label>
                  <label className="flex items-center">
                    <input
                      type="radio"
                      name="imageType"
                      value="ogImage"
                      checked={uploadedFiles.primaryImageType === 'ogImage'}
                      onChange={() => setUploadedFiles(prev => ({ ...prev, primaryImageType: 'ogImage' }))}
                      className="mr-2"
                    />
                    <span className="text-sm text-gray-300">🖼️ Social Media Image</span>
                  </label>
                </div>

                <div className="border-2 border-dashed border-zinc-600 rounded-lg p-4 text-center hover:border-orange-500 transition-colors">
                  <input
                    type="file"
                    id="primaryImage"
                    accept=".png,.jpg,.jpeg"
                    className="hidden"
                    onChange={(e) => {
                      const file = e.target.files?.[0];
                      if (file) {
                        setUploadedFiles(prev => ({
                          ...prev,
                          primaryImage: file,
                          primaryImageType: prev.primaryImageType || 'screenshot'
                        }));
                        console.log('Primary image selected:', file.name);
                      }
                    }}
                  />
                  <label htmlFor="primaryImage" className="cursor-pointer">
                    <div className="text-gray-400 mb-2">
                      {uploadedFiles.primaryImageType === 'ogImage' ? '🖼️' : '📸'}
                    </div>
                    {uploadedFiles.primaryImage ? (
                      <div>
                        <p className="text-sm text-green-400">✅ {uploadedFiles.primaryImage.name}</p>
                        <p className="text-xs text-gray-400">
                          Type: {uploadedFiles.primaryImageType === 'ogImage' ? 'Social Media Image' : 'Screenshot'}
                        </p>
                        <p className="text-xs text-gray-500">Click to change</p>
                      </div>
                    ) : (
                      <div>
                        <p className="text-sm text-gray-300">
                          Click to upload {uploadedFiles.primaryImageType === 'ogImage' ? 'social media image' : 'screenshot'}
                        </p>
                        <p className="text-xs text-gray-500">PNG, JPG up to 5MB</p>
                      </div>
                    )}
                  </label>
                </div>
              </div>

              {/* Logo Upload */}
              <div>
                <label className="block text-sm font-medium text-gray-300 mb-2">
                  Logo/Favicon <span className="text-red-400">*</span>
                </label>
                <p className="text-xs text-gray-400 mb-3">
                  Upload a logo file OR provide a logo URL below
                </p>

                <div className="border-2 border-dashed border-zinc-600 rounded-lg p-4 text-center hover:border-orange-500 transition-colors">
                  <input
                    type="file"
                    id="logo"
                    accept=".ico,.png,.jpg,.jpeg"
                    className="hidden"
                    onChange={(e) => {
                      const file = e.target.files?.[0];
                      if (file) {
                        setUploadedFiles(prev => ({ ...prev, logo: file }));
                        console.log('Logo selected:', file.name);
                      }
                    }}
                  />
                  <label htmlFor="logo" className="cursor-pointer">
                    <div className="text-gray-400 mb-2">🎨</div>
                    {uploadedFiles.logo ? (
                      <div>
                        <p className="text-sm text-green-400">✅ {uploadedFiles.logo.name}</p>
                        <p className="text-xs text-gray-500">Click to change</p>
                      </div>
                    ) : (
                      <div>
                        <p className="text-sm text-gray-300">Click to upload logo</p>
                        <p className="text-xs text-gray-500">ICO, PNG, JPG up to 1MB</p>
                      </div>
                    )}
                  </label>
                </div>
              </div>

              {/* Logo URL Alternative */}
              <div>
                <label htmlFor="logoUrl" className="block text-sm font-medium text-gray-300 mb-2">
                  Logo URL <span className="text-gray-500">(Optional - Alternative to uploads)</span>
                </label>
                <input
                  type="url"
                  id="logoUrl"
                  {...register('logoUrl')}
                  className="w-full px-3 py-2 bg-zinc-700 border border-zinc-600 rounded-md text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-orange-500 focus:border-transparent"
                  placeholder="https://your-domain.com/logo.png"
                />
                <p className="mt-1 text-xs text-gray-500">
                  If you have a logo hosted elsewhere, provide the direct URL
                </p>
              </div>
            </div>
          )}

          {/* Section 5: Additional & Contact */}
          {currentSection === 5 && (
            <div className="space-y-6">
              {/* FAQ */}
              <div>
                <label htmlFor="faq" className="block text-sm font-medium text-gray-300 mb-2">
                  FAQ <span className="text-gray-500">(Optional)</span>
                </label>
                <textarea
                  id="faq"
                  {...register('faq')}
                  rows={6}
                  className="w-full px-3 py-2 bg-zinc-700 border border-zinc-600 rounded-md text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-orange-500 focus:border-transparent"
                  placeholder="Frequently asked questions and answers about your tool. Format: Q: Question? A: Answer."
                />
              </div>

              {/* Submitter Information */}
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                  <label htmlFor="submitterName" className="block text-sm font-medium text-gray-300 mb-2">
                    Your Name *
                  </label>
                  <input
                    type="text"
                    id="submitterName"
                    {...register('submitterName')}
                    className={`w-full px-3 py-2 bg-zinc-700 border rounded-md text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-orange-500 focus:border-transparent ${
                      errors.submitterName ? 'border-red-500' : 'border-zinc-600'
                    }`}
                    placeholder="Your full name"
                  />
                  {errors.submitterName && (
                    <p className="mt-1 text-sm text-red-400">{errors.submitterName.message}</p>
                  )}
                </div>

                <div>
                  <label htmlFor="submitterEmail" className="block text-sm font-medium text-gray-300 mb-2">
                    Your Email *
                  </label>
                  <input
                    type="email"
                    id="submitterEmail"
                    {...register('submitterEmail')}
                    className={`w-full px-3 py-2 bg-zinc-700 border rounded-md text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-orange-500 focus:border-transparent ${
                      errors.submitterEmail ? 'border-red-500' : 'border-zinc-600'
                    }`}
                    placeholder="<EMAIL>"
                  />
                  {errors.submitterEmail && (
                    <p className="mt-1 text-sm text-red-400">{errors.submitterEmail.message}</p>
                  )}
                </div>
              </div>

              {/* Submission Summary */}
              <div className="bg-green-900/20 border border-green-500 rounded-lg p-4">
                <h4 className="text-green-400 font-medium mb-2">📋 Submission Summary</h4>
                <p className="text-sm text-gray-300 mb-3">
                  You're providing complete details for faster editorial review:
                </p>
                <div className="grid grid-cols-2 gap-4 text-xs text-gray-300">
                  <ul className="space-y-1">
                    <li>✅ Basic information</li>
                    <li>✅ Detailed descriptions</li>
                    <li>✅ Features & pricing</li>
                  </ul>
                  <ul className="space-y-1">
                    <li>✅ Pros & cons analysis</li>
                    <li>✅ SEO optimization</li>
                    <li>✅ FAQ content</li>
                  </ul>
                </div>
              </div>
            </div>
          )}

          {/* Navigation Buttons */}
          <div className="flex justify-between pt-6">
            <button
              type="button"
              onClick={() => setCurrentSection(Math.max(0, currentSection - 1))}
              disabled={currentSection === 0}
              className="px-4 py-2 rounded-lg font-medium transition-all duration-200 border border-zinc-600 hover:border-zinc-500 bg-transparent hover:bg-zinc-800 text-gray-300 disabled:opacity-50 disabled:cursor-not-allowed"
            >
              Previous
            </button>

            {currentSection < sections.length - 1 ? (
              <button
                type="button"
                onClick={() => setCurrentSection(Math.min(sections.length - 1, currentSection + 1))}
                className="px-4 py-2 rounded-lg font-medium transition-all duration-200 bg-orange-500 hover:bg-orange-400 text-white"
              >
                Next Section
              </button>
            ) : (
              <button
                type="submit"
                disabled={isSubmitting}
                className="px-6 py-2 rounded-lg font-medium transition-all duration-200 bg-green-600 hover:bg-green-500 text-white disabled:opacity-50 disabled:cursor-not-allowed"
              >
                {isSubmitting ? 'Submitting...' : 'Submit Tool'}
              </button>
            )}
          </div>
        </form>
      </div>
    </div>
  );
}
