#!/usr/bin/env tsx

/**
 * Apply Meta Keywords Migration Script
 *
 * This script applies the meta_keywords column migration to the tools table.
 * This field is required for the AI content generation system as documented
 * in the AI prompts implementation guide.
 */

import { config } from 'dotenv';
import { createClient } from '@supabase/supabase-js';
import * as fs from 'fs';
import * as path from 'path';

// Load environment variables
config({ path: '.env.local' });

const supabase = createClient(
  process.env.NEXT_PUBLIC_SUPABASE_URL!,
  process.env.SUPABASE_SERVICE_ROLE_KEY!
);

async function applyMigration() {
  console.log('🚀 Starting Meta Keywords Migration...');
  console.log('📋 This migration will:');
  console.log('   • Add meta_keywords column to tools table');
  console.log('   • Create search index for meta_keywords');
  console.log('   • Add sample keywords to existing published tools');
  console.log('');

  try {
    // Step 1: Check if column already exists
    console.log('1️⃣ Checking if meta_keywords column exists...');

    const { data: testQuery1, error: testError1 } = await supabase
      .from('tools')
      .select('meta_keywords')
      .limit(1);

    if (testError1 && (testError1.message.includes('column "meta_keywords" does not exist') || testError1.message.includes('column tools.meta_keywords does not exist'))) {
      console.log('   Column does not exist, need to add it manually via Supabase dashboard');
      console.log('');
      console.log('🔧 MANUAL MIGRATION REQUIRED:');
      console.log('   Please execute this SQL in your Supabase SQL editor:');
      console.log('');
      console.log('   -- Add meta_keywords column to tools table');
      console.log('   ALTER TABLE tools ADD COLUMN meta_keywords TEXT;');
      console.log('');
      console.log('   -- Add comment for documentation');
      console.log('   COMMENT ON COLUMN tools.meta_keywords IS \'SEO meta keywords for the tool page, comma-separated list of relevant keywords\';');
      console.log('');
      console.log('   -- Create index for meta_keywords for search performance');
      console.log('   CREATE INDEX idx_tools_meta_keywords ON tools USING gin(to_tsvector(\'english\', meta_keywords));');
      console.log('');
      console.log('📋 After executing the SQL above, run this script again to populate sample data.');
      return;
    } else if (testError1) {
      console.error('❌ Unexpected error:', testError1.message);
      throw testError1;
    } else {
      console.log('✅ meta_keywords column already exists');
    }

    // Step 2: Verify migration
    console.log('\n2️⃣ Verifying migration...');
    const { data: testQuery2, error: testError2 } = await supabase
      .from('tools')
      .select('id, meta_keywords')
      .limit(1);

    if (testError2) {
      console.error('❌ Migration verification failed:', testError2.message);
      throw testError2;
    }

    console.log('✅ Migration verification successful');

    // Step 3: Add sample meta keywords to published tools (optional)
    console.log('\n3️⃣ Adding sample meta keywords to published tools...');
    const { data: publishedTools, error: toolsError } = await supabase
      .from('tools')
      .select('id, name, category_id')
      .eq('content_status', 'published')
      .or('meta_keywords.is.null,meta_keywords.eq.')
      .limit(10);

    if (toolsError) {
      console.warn('⚠️  Could not fetch published tools:', toolsError.message);
    } else if (publishedTools && publishedTools.length > 0) {
      console.log(`   Found ${publishedTools.length} tools to add sample meta keywords to`);

      for (const tool of publishedTools) {
        // Get category name for keywords
        let categoryName = 'productivity';
        if (tool.category_id) {
          const { data: category } = await supabase
            .from('categories')
            .select('name')
            .eq('id', tool.category_id)
            .single();
          if (category) {
            categoryName = category.name.toLowerCase();
          }
        }

        const sampleKeywords = `${tool.name.toLowerCase()}, ai tool, artificial intelligence, ${categoryName}, automation`;

        const { error: updateError } = await supabase
          .from('tools')
          .update({ meta_keywords: sampleKeywords })
          .eq('id', tool.id);

        if (updateError) {
          console.warn(`   ⚠️  Failed to add keywords to ${tool.name}:`, updateError.message);
        } else {
          console.log(`   ✅ Added sample keywords to ${tool.name}`);
        }
      }
    } else {
      console.log('   ℹ️  No published tools found without meta keywords');
    }

    console.log('\n🎉 Meta Keywords Migration completed successfully!');
    console.log('\n📋 Migration Summary:');
    console.log('   ✅ meta_keywords column verified in tools table');
    console.log('   ✅ Sample keywords added to published tools');
    console.log('   ✅ System ready for AI content generation with meta keywords');

  } catch (error) {
    console.error('\n❌ Migration failed:', error);
    console.error('\n🔧 Troubleshooting:');
    console.error('   1. Check your database connection');
    console.error('   2. Verify you have the correct permissions');
    console.error('   3. Execute the SQL manually in Supabase dashboard');
    console.error('   4. Review the Supabase logs for more details');
    process.exit(1);
  }
}

// Execute the migration
if (require.main === module) {
  applyMigration().catch(console.error);
}

export { applyMigration };
